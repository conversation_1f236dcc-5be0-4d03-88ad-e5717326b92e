<?php

namespace App\Enums;

use <PERSON><PERSON><PERSON><PERSON>\Enum\Enum;

final class AppConfigEnum extends Enum
{
    public const SUCCESS_EMAIL = 'SUCCESS_EMAIL';
    public const SUPPORT_EMAIL = 'SUPPORT_EMAIL';
    public const DIRECT_SUPPORT_EMAIL = 'DIRECT_SUPPORT_EMAIL';
    public const PLAID_ACCOUNT_SYNC_TIMEOUT_IN_HOUR = 'PLAID_ACCOUNT_SYNC_TIMEOUT_IN_HOUR';
    public const PLAID_COUNTRY_ALLOW_LIST = 'PLAID_COUNTRY_ALLOW_LIST';
    public const PLAID_REMOVE_BLACKLIST_WORDS_FROM_DESCRIPTION = 'PLAID_REMOVE_BLACKLIST_WORDS_FROM_DESCRIPTION';
    public const TOP_TRENDING_INITIAL_DAYS = 'TOP_TRENDING_INITIAL_DAYS';
    public const TOP_TRENDING_DAYS = 'TOP_TRENDING_DAYS';
    public const TOP_TRENDING_PRODUCTS = 'TOP_TRENDING_PRODUCTS';
    public const TOP_TRENDING_COMPANIES = 'TOP_TRENDING_COMPANIES';
    public const REVIEW_COUNT_PERCENTAGE = 'REVIEW_COUNT_PERCENTAGE';
    public const STACK_COUNT_PERCENTAGE = 'STACK_COUNT_PERCENTAGE';
    public const DIRECT_SUBDOMAIN = 'DIRECT_SUBDOMAIN';
    public const DIRECT_PRICING_PAGE_LINK = 'DIRECT_PRICING_PAGE_LINK';
}
