<?php

namespace App\Enums\Email;

use BenSampo\Enum\Enum;

final class EmailBladeFiles extends Enum
{
    const AbandonedReviewsReminder = 'abandonedReviewsReminder.blade.php';
    const AdminMSPNewClaimersResponsesNotification = 'adminMSPNewClaimersResponsesNotification.blade.php';
    const AffiliateLocationInvitation = 'affiliateLocationInvitation.blade.php';
    const AttendantWelcomeNotification = 'attendantWelcome.blade.php';
    const ChangeEmail = 'emailReset.blade.php';
    const ChatNotificationMessages = 'chatNotificationMessages.blade.php';
    const ConfirmEmail = 'confirmRegister.blade.php';
    const ConfirmedEmail = 'confirmedRegister.blade.php';
    const ConfirmedEmailMSP = 'confirmedRegisterMSP.blade.php';
    const ConfirmedEmailDirect = 'confirmedRegisterDirect.blade.php';
    const EventApprovedNotification = 'eventApprovedNotification.blade.php';
    const MSPClientInvite = 'mspClientInvite.blade.php';
    const MSPUserInvite = 'mspUserInvite.blade.php';
    const MSPCustomerUserInvite = 'mspCustomerUserInvite.blade.php';
    const MSPLocationInvite = 'mspLocationInvite.blade.php';
    const MinutesBeforePitchReminder = 'minutesBeforePitchReminder.blade.php';
    const MissingVendorDistributorNotification = 'missingVendorDistributorNotification.blade.php';
    const MissingProductOrVendorNotification = 'missingProductOrVendorNotification.blade.php';
    const MyStackFreeVendorClaimerNotification = 'myStackFreeVendorClaimerNotification.blade.php';
    const MyStackFreeVendorClaimerNoPartnerNotification = 'myStackFreeVendorClaimerNoPartnerNotification.blade.php';
    const MyStackVendorNoPrmClaimerNoPartnerNotification = 'myStackVendorNoPrmClaimerNoPartnerNotification.blade.php';
    const MyStackVendorNoPrmClaimerPartnerNotification = 'myStackVendorNoPrmClaimerPartnerNotification.blade.php';
    const MyStackVendorPrmClaimerNoPartnerNotification = 'myStackVendorPrmClaimerNoPartnerNotification.blade.php';
    const MyStackVendorPrmClaimerPartnerNotification = 'myStackVendorPrmClaimerPartnerNotification.blade.php';
    const PartnerInvite = 'partnerInvite.blade.php';
    const PartnerRequested = 'prmRequestsReminder.blade.php';
    const PendingInviteRequestNotification = 'pendingInviteRequestNotification.blade.php';
    const VendorPendingRequestNotification = 'vendorPendingRequestNotification.blade.php';
    const PitchEventVendorWelcome = 'pitchEventVendorWelcome.blade.php';
    const ResetPassword = 'forgotPassword.blade.php';
    const ResetPasswordClient = 'forgotPasswordClient.blade.php';
    const RegisteredEmailMSP = 'registeredMSP.blade.php';
    const RegisteredEmailDirect = 'registeredDirect.blade.php';
    const ReviewApproved = 'reviewApproved.blade.php';
    const ReviewApprovedForClaimer = 'reviewApprovedForClaimer.blade.php';
    const UpcomingEventNotification = 'upcomingEventNotification.blade.php';
    const VerificationCode = 'verificationCode.blade.php';
    const VendorProductWeeklySummary = 'vendorProductWeeklySummary.blade.php';
    const VendorUserInvite = 'vendorUserInvite.blade.php';
    const VendorWeeklySummary = 'vendorWeeklySummary.blade.php';
    const WelcomeClaimer = 'welcomeClaimer.blade.php';
    const WelcomeMSPClaimer = 'welcomeMSPClaimer.blade.php';
    const NoActivityVendorNotification = 'noActivityVendorNotification.blade.php';
    const FreeVendorTrendingNotification = 'freeVendorTrendingNotification.blade.php';
    const MyStackBuildingReminder = 'myStackBuildingReminder.blade.php';
    const InviteToReviewEmail = 'inviteToReviewEmail.blade.php';
    const InviteToReviewDraftedMessage = 'InviteToReviewDraftedMessage'; // This is not an EMAIL, we are just using the email architecture so marketing can administrate it
    const RecommendNaviStackEmail = 'recommendNaviStackEmail.blade.php';
    const RecommendNaviStackDraftedMessage = 'RecommendNaviStackDraftedMessage'; // This is not an EMAIL, we are just using the email architecture so marketing can administrate it
    const FirstReviewApproved = 'firstReviewApproved.blade.php';
    const VendorContractRenewalReminder = 'vendorContractRenewalReminder.blade.php';
    const VendorContractRenewalReminderCustomer = 'vendorContractRenewalReminderCustomer.blade.php';
    const VendorNoticeDeadlineApproaching = 'vendorNoticeDeadlineApproaching.blade.php';
    const VendorNoticeDeadlineApproachingCustomer = 'vendorNoticeDeadlineApproachingCustomer.blade.php';
    const VendorWeeklySummaryForVendor = 'vendorWeeklySummaryForVendor.blade.php';
    const NewMultiStackInvitation = 'newMultiStackInvitation.blade.php';
    const FreeVendorTopTrendingProduct = 'freeVendorTopTrendingProduct.blade.php';
    const DealSubmittedToVendor = 'dealSubmittedToVendor.blade.php';
    const DealSubmittedToFreeVendor = 'dealSubmittedToFreeVendor.blade.php';
    const DealSubmittedToVendorNoChannelCommand = 'dealSubmittedToVendorNoChannelCommand.blade.php';
    const DealApprovedByVendor = 'dealApprovedByVendor.blade.php';
    const DealRejectedByVendor = 'dealRejectedByVendor.blade.php';
    const DealAdittionalInfoRequested = 'dealAdittionalInfoRequested.blade.php';
    const DealAdittionalInfoProvided = 'dealAdittionalInfoProvided.blade.php';
    const DealValidityDateApproachingMSP = 'dealValidityDateApproachingMSP.blade.php';
    const DealValidityDateApproachingVendor = 'dealValidityDateApproachingVendor.blade.php';
    const ApprovedDealChanged = 'approvedDealChanged.blade.php';
    const DeclinedDealChanged = 'declinedDealChanged.blade.php';
    public const CHANNEL_DEAL_APPROVED = 'channelDealApproved.blade.php';
    public const CHANNEL_DEAL_SEND_LEADS = 'channelDealSendLeads.blade.php';
    public const CHANNEL_DEAL_USER_INVITATION = 'channelDealUserInvitation.blade.php';
    public const COMPANY_EXPENSES_UPCOMING_RECURRING_CHARGES = 'companyExpensesUpcomingRecurringCharges.blade.php';
    public const COMPANY_EXPENSES_INCREASE_OVER_TIMEFRAME = 'companyExpensesIncreaseOverTimeframe.blade.php';
    public const COMPANY_EXPENSES_DECREASE_OVER_TIMEFRAME = 'companyExpensesDecreaseOverTimeframe.blade.php';
    public const COMPANY_EXPENSES_INDIVIDUAL_RECURRING_EXPENSE_INCREASE = 'companyExpensesIndividualRecurringExpenseIncrease.blade.php';
    public const COMPANY_EXPENSES_INDIVIDUAL_RECURRING_EXPENSE_DECREASE = 'companyExpensesIndividualRecurringExpenseDecrease.blade.php';
    public const COMPANY_EXPENSES_WEEKLY_SUMMARY = 'companyExpensesWeeklySummary.blade.php';
    public const COMPANY_EXPENSES_MONTHLY_SUMMARY = 'companyExpensesMonthlySummary.blade.php';
    public const COMPANY_EXPENSES_TIME_TO_REVIEW_YOUR_EXPENSES = 'companyExpensesTimeToReviewYourExpenses.blade.php';
    public const COMPANY_EXPENSES_UPCOMING_RECURRING_CHARGES_MSP = 'companyExpensesUpcomingRecurringChargesMSP.blade.php';
    public const COMPANY_EXPENSES_INCREASE_OVER_TIMEFRAME_MSP = 'companyExpensesIncreaseOverTimeframeMSP.blade.php';
    public const COMPANY_EXPENSES_DECREASE_OVER_TIMEFRAME_MSP = 'companyExpensesDecreaseOverTimeframeMSP.blade.php';
    public const COMPANY_EXPENSES_INDIVIDUAL_RECURRING_EXPENSE_INCREASE_MSP = 'companyExpensesIndividualRecurringExpenseIncreaseMSP.blade.php';
    public const COMPANY_EXPENSES_INDIVIDUAL_RECURRING_EXPENSE_DECREASE_MSP = 'companyExpensesIndividualRecurringExpenseDecreaseMSP.blade.php';
    public const COMPANY_EXPENSES_WEEKLY_SUMMARY_MSP = 'companyExpensesWeeklySummaryMSP.blade.php';
    public const COMPANY_EXPENSES_MONTHLY_SUMMARY_MSP = 'companyExpensesMonthlySummaryMSP.blade.php';
    public const COMPANY_EXPENSES_TIME_TO_REVIEW_YOUR_EXPENSES_MSP = 'companyExpensesTimeToReviewYourExpensesMSP.blade.php';
    public const HAPPY_ANNIVERSARY_ONE_MONTH = 'happyAnniversaryOneMonth.blade.php';
    public const HAPPY_ANNIVERSARY_SIX_MONTHS = 'happyAnniversarySixMonths.blade.php';
    public const HAPPY_ANNIVERSARY_ONE_YEAR = 'happyAnniversaryOneYear.blade.php';
}
