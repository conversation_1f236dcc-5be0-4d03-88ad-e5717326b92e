<?php

namespace App\Enums\Email;

use BenSampo\Enum\Enum;

final class EmailType extends Enum
{
    const AbandonedReviewsReminder = 'AbandonedReviewsReminder';
    const AdminMSPNewClaimersResponsesNotification = 'AdminMSPNewClaimersResponsesNotification';
    const AffiliateLocationInvitation = 'AffiliateLocationInvitation';
    const MSPLocationInvitation = 'MSPLocationInvitation';
    const AttendantWelcomeNotification = 'AttendantWelcomeNotification';
    const ChangeEmail = 'ChangeEmail';
    const ChatNotificationMessages = 'ChatNotificationMessages';
    const ConfirmEmail = 'ConfirmEmail';
    const ConfirmedEmail = 'ConfirmedEmail';
    const ConfirmedEmailMSP = 'ConfirmedEmailMSP';
    const ConfirmedEmailDirect = 'ConfirmedEmailDirect';
    const EventApprovedNotification = 'eventApprovedNotification';
    const MspClientInviteNotification = 'MspClientInviteNotification';
    const MspUserInviteNotification = 'MspUserInviteNotification';
    const MspCustomerUserInviteNotification = 'MspCustomerUserInviteNotification';
    const MinutesBeforePitchReminder = 'MinutesBeforePitchReminder';
    const MissingVendorDistributorNotification = 'missingVendorDistributorNotification';
    const MissingProductOrVendorNotification = 'missingProductOrVendorNotification';
    const MyStackFreeVendorClaimerNotification = 'myStackFreeVendorClaimerNotification';
    const MyStackVendorNoPrmClaimerNotification = 'myStackVendorNoPrmClaimerNotification';
    const MyStackVendorPrmClaimerNotification = 'myStackVendorPrmClaimerNotification';
    const PartnerInvite = 'PartnerInvite';
    const PartnerRequested = 'PartnerRequested';
    const PendingInviteRequestNotification = 'pendingInviteRequestNotification';
    const VendorPendingRequestNotification = 'vendorPendingRequestNotification';
    const PitchEventVendorWelcome = 'PitchEventVendorWelcome';
    const ResetPassword = 'ResetPassword';
    const ResetPasswordClient = 'ResetPasswordClient';
    const RegisteredEmailMSP = 'RegisteredEmailMSP';
    const RegisteredEmailDirect = 'RegisteredEmailDirect';
    const ReviewApproved = 'ReviewApproved';
    const ReviewApprovedForClaimer = 'ReviewApprovedForClaimer';
    const UpcomingEventNotification = 'upcomingEventNotification';
    const VendorUserInviteNotification = 'VendorUserInviteNotification';
    const VendorWeeklySummary = 'VendorWeeklySummary';
    const VendorProductWeeklySummary = 'VendorProductWeeklySummary';
    const VerificationCode = 'VerificationCode';
    const WelcomeClaimer = 'WelcomeClaimer';
    const WelcomeMSPClaimer = 'WelcomeMSPClaimer';
    const NoActivityVendorNotification = 'NoActivityVendorNotification';
    const FreeVendorTrendingNotification = 'FreeVendorTrendingNotification';
    const MyStackBuildingReminder = 'MyStackBuildingReminder';
    const InviteToReviewDraftedMessage = 'InviteToReviewDraftedMessage';
    const InviteToReviewEmail = 'InviteToReviewEmail';
    const RecommendNaviStackDraftedMessage = 'RecommendNaviStackDraftedMessage';
    const RecommendNaviStackEmail = 'RecommendNaviStackEmail';
    const FirstReviewApproved = 'FirstReviewApproved';
    const VendorContractRenewalReminder = 'VendorContractRenewalReminder';
    const VendorContractRenewalReminderCustomer = 'VendorContractRenewalReminderCustomer';
    const VendorNoticeDeadlineApproaching = 'VendorNoticeDeadlineApproaching';
    const VendorNoticeDeadlineApproachingCustomer = 'VendorNoticeDeadlineApproachingCustomer';
    const VendorWeeklySummaryForVendor = 'VendorWeeklySummaryForVendor';
    const NewMultiStackInvitation = 'NewMultiStackInvitation';
    const FreeVendorTopTrendingProduct = 'FreeVendorTopTrendingProduct';
    const DealSubmittedToVendor = 'DealSubmittedToVendor';
    const DealSubmittedToFreeVendor = 'DealSubmittedToFreeVendor';
    const DealSubmittedToVendorNoChannelCommand = 'DealSubmittedToVendorNoChannelCommand';
    const DealAdittionalInfoRequested = 'DealAdittionalInfoRequested';
    const DealAdittionalInfoProvided = 'DealAdittionalInfoProvided';
    const DealApprovedByVendor = 'DealApprovedByVendor';
    const DealRejectedByVendor = 'DealRejectedByVendor';
    const DealValidityDateApproachingMSP = 'DealValidityDateApproachingMSP';
    const DealValidityDateApproachingVendor = 'DealValidityDateApproachingVendor';
    const ApprovedDealChanged = 'ApprovedDealChanged';
    const DeclinedDealChanged = 'DeclinedDealChanged';
    public const CHANNEL_DEAL_APPROVED = 'ChannelDealApproved';
    public const CHANNEL_DEAL_SEND_LEADS = 'ChannelDealSendLeads';
    public const CHANNEL_DEAL_USER_INVITATION = 'ChannelDealUserInvitation';
    public const COMPANY_EXPENSES_UPCOMING_RECURRING_CHARGES = 'companyExpensesUpcomingRecurringCharges';
    public const COMPANY_EXPENSES_INCREASE_OVER_TIMEFRAME = 'companyExpensesIncreaseOverTimeframe';
    public const COMPANY_EXPENSES_DECREASE_OVER_TIMEFRAME = 'companyExpensesDecreaseOverTimeframe';
    public const COMPANY_EXPENSES_INDIVIDUAL_RECURRING_EXPENSE_INCREASE = 'companyExpensesIndividualRecurringExpenseIncrease';
    public const COMPANY_EXPENSES_INDIVIDUAL_RECURRING_EXPENSE_DECREASE = 'companyExpensesIndividualRecurringExpenseDecrease';
    public const COMPANY_EXPENSES_WEEKLY_SUMMARY = 'companyExpensesWeeklySummary';
    public const COMPANY_EXPENSES_MONTHLY_SUMMARY = 'companyExpensesMonthlySummary';
    public const COMPANY_EXPENSES_TIME_TO_REVIEW_YOUR_EXPENSES = 'companyExpensesTimeToReviewYourExpenses';
    public const COMPANY_EXPENSES_UPCOMING_RECURRING_CHARGES_MSP = 'companyExpensesUpcomingRecurringChargesMSP';
    public const COMPANY_EXPENSES_INCREASE_OVER_TIMEFRAME_MSP = 'companyExpensesIncreaseOverTimeframeMSP';
    public const COMPANY_EXPENSES_DECREASE_OVER_TIMEFRAME_MSP = 'companyExpensesDecreaseOverTimeframeMSP';
    public const COMPANY_EXPENSES_INDIVIDUAL_RECURRING_EXPENSE_INCREASE_MSP = 'companyExpensesIndividualRecurringExpenseIncreaseMSP';
    public const COMPANY_EXPENSES_INDIVIDUAL_RECURRING_EXPENSE_DECREASE_MSP = 'companyExpensesIndividualRecurringExpenseDecreaseMSP';
    public const COMPANY_EXPENSES_WEEKLY_SUMMARY_MSP = 'companyExpensesWeeklySummaryMSP';
    public const COMPANY_EXPENSES_MONTHLY_SUMMARY_MSP = 'companyExpensesMonthlySummaryMSP';
    public const COMPANY_EXPENSES_TIME_TO_REVIEW_YOUR_EXPENSES_MSP = 'companyExpensesTimeToReviewYourExpensesMSP';
    public const HAPPY_ANNIVERSARY_ONE_MONTH = 'happyAnniversaryOneMonth';
    public const HAPPY_ANNIVERSARY_SIX_MONTHS = 'happyAnniversarySixMonths';
    public const HAPPY_ANNIVERSARY_ONE_YEAR = 'happyAnniversaryOneYear';
}
