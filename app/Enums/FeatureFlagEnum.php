<?php

namespace App\Enums;

use <PERSON><PERSON><PERSON><PERSON>\Enum\Enum;

class FeatureFlagEnum extends Enum
{
    public const CHANNEL_DEAL_EMAIL_APPROVED = 'CHANNEL_DEAL_EMAIL_APPROVED_NOTIFICATION';
    public const CHANNEL_GET_DEAL_EMAIL = 'CHANNEL_GET_DEAL_NOTIFICATION';
    public const CHANNEL_GET_DEAL_EMAIL_INVITATION = 'CHANNEL_GET_DEAL_INVITATION_NOTIFICATION';
    public const COMPANY_EXPENSES_EMAIL_NOTIFICATIONS = 'COMPANY_EXPENSES_EMAIL_NOTIFICATIONS';
    public const HAPPY_ANNIVERSARY_ONE_MONTH_EMAIL_NOTIFICATIONS = 'HAPPY_ANNIVERSARY_ONE_MONTH_EMAIL_NOTIFICATIONS';
    public const HAPPY_ANNIVERSARY_SIX_MONTHS_EMAIL_NOTIFICATIONS = 'HAPPY_ANNIVE<PERSON>ARY_SIX_MONTHS_EMAIL_NOTIFICATIONS';
    public const HAPPY_ANNIVERSARY_ONE_YEAR_EMAIL_NOTIFICATIONS = 'HAPPY_ANNIVERSARY_ONE_YEAR_EMAIL_NOTIFICATIONS';
    public const CONFIRMED_DIRECT_EMAIL_NOTIFICATION = 'CONFIRMED_DIRECT_EMAIL_NOTIFICATION';
}
