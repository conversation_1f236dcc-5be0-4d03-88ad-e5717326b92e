<?php

namespace App\Enums\Plaid\Filters;

use App\Enums\Filters\FilterTypesEnum;
use BenSampo\Enum\Enum;

final class AlertNotificationFilters extends Enum
{
    public const VENDOR = [
        'multiple' => true,
        'placeholder' => 'Vendor',
        'is_public' => true,
    ];

    public const ALERT_TYPE = [
        'multiple' => true,
        'placeholder' => 'Alert Type',
        'is_public' => true,
    ];

    public const PERIOD = [
        'multiple' => true,
        'placeholder' => 'Period',
        'is_public' => true,
    ];

    public const DATE = [
        'type' => FilterTypesEnum::DATE_RANGE,
        'multiple' => false,
        'placeholder' => 'Date',
        'is_public' => true,
    ];
}
