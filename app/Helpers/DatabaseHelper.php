<?php

namespace App\Helpers;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class DatabaseHelper
{
    /**
     * Checks if a foreign key exists in a table and drops it if it exists
     *
     * @param  string  $table  Table name
     * @param  string  $foreignKey  Foreign key name (can be full name or just the field)
     * @param  \Illuminate\Database\Schema\Blueprint|null  $blueprint  Blueprint instance if used within a migration
     * @return bool True if the key was dropped, false if it didn't exist
     */
    public static function dropForeignKeyIfExists(string $table, string $foreignKey, $blueprint = null): bool
    {
        // Normalize the foreign key name
        // If only the field name is provided (e.g., 'user_id'), build the full name (e.g., 'table_user_id_foreign')
        if (!str_contains($foreignKey, '_foreign') && !str_contains($foreignKey, 'fk_')) {
            $fullForeignKeyName = "{$table}_{$foreignKey}_foreign";
        } else {
            $fullForeignKeyName = $foreignKey;
        }

        // Check if the foreign key exists
        $foreignKeyExists = self::foreignKeyExists($table, $fullForeignKeyName);

        // If it exists, drop it
        if ($foreignKeyExists) {
            if ($blueprint) {
                // If a blueprint is provided, use that context (within a migration)
                $blueprint->dropForeign([$foreignKey]);
            } else {
                // If no blueprint, use Schema directly
                Schema::table($table, function ($table) use ($foreignKey, $fullForeignKeyName) {
                    // Try to drop using both the full name and the field name
                    try {
                        $table->dropForeign($fullForeignKeyName);
                    } catch (\Exception $e) {
                        try {
                            $table->dropForeign([$foreignKey]);
                        } catch (\Exception $e) {
                            // If both fail, log the error but don't interrupt
                            \Log::warning("Could not drop foreign key: {$e->getMessage()}");
                        }
                    }
                });
            }

            return true;
        }

        return false;
    }

    /**
     * Checks if a foreign key exists in a table
     *
     * @param  string  $table  Table name
     * @param  string  $foreignKey  Foreign key name
     */
    public static function foreignKeyExists(string $table, string $foreignKey): bool
    {
        // Get all foreign keys from the table
        $foreignKeys = [];

        try {
            // Query for PostgreSQL
            if (DB::connection()->getDriverName() === 'pgsql') {
                $foreignKeys = DB::select("
                    SELECT constraint_name
                    FROM information_schema.table_constraints
                    WHERE table_name = ? AND constraint_type = 'FOREIGN KEY'
                ", [$table]);
            }
            // Query for MySQL
            else {
                $foreignKeys = DB::select("
                    SELECT CONSTRAINT_NAME
                    FROM information_schema.TABLE_CONSTRAINTS
                    WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ? AND CONSTRAINT_TYPE = 'FOREIGN KEY'
                ", [$table]);
            }
        } catch (\Exception $e) {
            \Log::error("Error checking foreign keys: {$e->getMessage()}");

            return false;
        }

        // Convert results to a simple array of names
        $constraintNames = array_map(function ($item) {
            return strtolower($item->constraint_name ?? $item->CONSTRAINT_NAME);
        }, $foreignKeys);

        // Check if the foreign key exists
        return in_array(strtolower($foreignKey), $constraintNames);
    }
}
