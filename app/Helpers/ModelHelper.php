<?php

namespace App\Helpers;

use App\Enums\ModelType;
use App\Models\AppConfiguration;
use App\Models\Blog\Blog;
use App\Models\Category\Category;
use App\Models\Category\CategoryCompanyType;
use App\Models\ChannelDeals\ChannelDeal;
use App\Models\ChannelDeals\ChannelDealsClaimed;
use App\Models\Comment\Comment;
use App\Models\Company\Company;
use App\Models\Company\CompanyClient;
use App\Models\Company\CompanyContactList;
use App\Models\Contract\Contract;
use App\Models\ContractBillingContact;
use App\Models\ContractField;
use App\Models\CpJobs;
use App\Models\Deal\Deal;
use App\Models\IndustryEvent\IndustryEvent;
use App\Models\Integrations\Integration;
use App\Models\Integrations\IntegrationAppendValue;
use App\Models\Integrations\IntegrationMapping;
use App\Models\Integrations\IntegrationValue;
use App\Models\Integrations\VendorCustomer;
use App\Models\JobTitle;
use App\Models\MediaGallery;
use App\Models\MSPFollowingPartner;
use App\Models\Permission\Feature\PermissionFeature;
use App\Models\Permission\Feature\PermissionFeatureGroup;
use App\Models\Permission\Role\Role;
use App\Models\Permission\Role\TemplateRole;
use App\Models\Plaid\Alert\PlaidCompanyNotification;
use App\Models\Plaid\Alert\PlaidNotifyAlert;
use App\Models\Plaid\Alert\PlaidNotifyRecipient;
use App\Models\Plaid\PlaidBankAccount;
use App\Models\Plaid\PlaidBankLink;
use App\Models\Plaid\PlaidInstitution;
use App\Models\Plaid\PlaidSubscription;
use App\Models\Plaid\PlaidTransaction;
use App\Models\Product;
use App\Models\ProfanityFilter;
use App\Models\Profile\ProfileRuleValue;
use App\Models\Review\Review;
use App\Models\ShoutOut\ShoutOut;
use App\Models\StatusScope\StatusScope;
use App\Models\User;
use App\Models\UserCompanyRequest;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Validation\ValidationException;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * This helper handle the utilities used in multiple places in the project
 */
class ModelHelper
{
    /**
     * This method receives a model id and class and returns the author
     *
     * @return mixed
     *
     * @throws ValidationException
     */
    public static function getModelAuthor($modelId, $modelClass)
    {
        return match ($modelClass) {
            User::class => User::findOrFail($modelId),
            ShoutOut::class => ShoutOut::findOrFail($modelId)->author,
            Media::class => self::getMediaAuthor($modelId),
            MediaGallery::class => self::getMediaGalleryAuthor($modelId),
            Blog::class => Blog::findOrFail($modelId)->author,
            Product::class => self::getProductAuthor($modelId),
            Review::class => Review::findOrFail($modelId)->reviewer,
            Company::class => Company::findOrFail($modelId)->claimers,
            UserCompanyRequest::class => UserCompanyRequest::findOrFail($modelId)->claimers,
            default => throw ValidationException::withMessages([
                'ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::Logic not implemented for: ' . $modelClass,
            ]),
        };
    }

    /**
     * @throws ValidationException
     */
    public static function getMediaAuthor($modelId)
    {
        $author = Media::findOrFail($modelId)->model;
        if (get_class($author) === MediaGallery::class) {
            return self::getMediaGalleryAuthor($author->id);
        }
        if (get_class($author) === Company::class) {
            return User::select('users.*')
                ->join('company_claimers', 'users.id', '=', 'company_claimers.user_id')
                ->where('company_claimers.company_id', $author->id)
                ->get();
        }

        return $author;
    }

    /**
     * @throws ValidationException
     */
    public static function getMediaGalleryAuthor($modelId)
    {
        $mediaGallery = MediaGallery::findOrFail($modelId);
        $authorId = $mediaGallery->getCustomProperty('author_id');
        if (empty($authorId)) {
            throw ValidationException::withMessages([
                'ERROR(Model->getMediaGalleryAuthor):The media gallery (' . $mediaGallery->id . ') has not author_id.',
            ]);
        }

        if ($mediaGallery->model_type === Company::class) {
            return User::select('users.*')
                ->join('company_claimers', 'users.id', '=', 'company_claimers.user_id')
                ->where('company_claimers.company_id', $mediaGallery->model_id)
                ->get();
        }

        return User::findOrFail($authorId);
    }

    public static function getProductAuthor($modelId)
    {
        $company = Product::findOrFail($modelId)->company;

        return $company->claimers;
    }

    /**
     * This method receives a model id and class and returns the parent
     *
     * @throws ValidationException
     */
    public static function getModelParent($modelId, $modelClass): Model
    {
        return match ($modelClass) {
            User::class => User::findOrFail($modelId),
            ShoutOut::class => ShoutOut::findOrFail($modelId)->subject,
            Media::class => self::getMediaParent($modelId),
            MediaGallery::class => self::getMediaGalleryParent($modelId),
            Blog::class => Blog::findOrFail($modelId)->subject,
            Product::class => Product::findOrFail($modelId)->company,
            Review::class => Review::findOrFail($modelId)->reviewer,
            Company::class => Company::findOrFail($modelId),
            UserCompanyRequest::class => UserCompanyRequest::findOrFail($modelId),
            default => throw ValidationException::withMessages([
                'ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::Logic not implemented for: ' . $modelClass,
            ]),
        };
    }

    public static function getMediaParent($modelId)
    {
        $parent = Media::findOrFail($modelId)->model;
        if (get_class($parent) === MediaGallery::class) {
            return self::getMediaGalleryParent($parent->id);
        }

        return $parent;
    }

    public static function getMediaGalleryParent($modelId)
    {
        $mediaGallery = MediaGallery::findOrFail($modelId);

        return $mediaGallery->parent;
    }

    /**
     * This method returns TRUE if an ID exists in a model, FALSE if not
     *
     * @throws ValidationException
     */
    public static function idExistsInModel($modelType, $id): bool
    {
        return match ($modelType) {
            ModelType::userType, ModelType::users,
            ModelType::userProfile => (bool)User::find($id),
            ModelType::companies, ModelType::companyProfile => (bool)Company::find($id),
            ModelType::companyContract, ModelType::contracts => (bool)Contract::find($id),
            ModelType::mediaType, ModelType::media => (bool)Media::find($id),
            ModelType::product, ModelType::products => (bool)Product::find($id),
            ModelType::industryEvent, ModelType::industry_events => (bool)IndustryEvent::find($id),
            ModelType::job_titles => (bool)JobTitle::find($id),
            ModelType::categories => (bool)Category::find($id),
            ModelType::category_company_types => (bool)CategoryCompanyType::find($id),
            ModelType::app_configurations => (bool)AppConfiguration::find($id),
            ModelType::cp_jobs => (bool)CpJobs::find($id),
            ModelType::shout_outs => (bool)ShoutOut::find($id),
            ModelType::profile_rule_values => (bool)ProfileRuleValue::find($id),
            ModelType::media_galleries => (bool)MediaGallery::find($id),
            ModelType::blogs => (bool)Blog::find($id),
            ModelType::profanity_filters => (bool)ProfanityFilter::find($id),
            ModelType::deals => (bool)Deal::find($id),
            ModelType::company_contact_lists => (bool)CompanyContactList::find($id),
            ModelType::permission_features => (bool)PermissionFeature::find($id),
            ModelType::permission_feature_groups => (bool)PermissionFeatureGroup::find($id),
            ModelType::roles => (bool)Role::find($id),
            ModelType::template_roles => (bool)TemplateRole::find($id),
            ModelType::user_company_requests => (bool)UserCompanyRequest::find($id),
            ModelType::integration_vendor_customer => (bool)VendorCustomer::find($id),
            ModelType::channel_deals => (bool)ChannelDeal::find($id),
            ModelType::channel_deals_claimed => (bool)ChannelDealsClaimed::find($id),
            ModelType::company_partners => (bool)MSPFollowingPartner::find($id),
            ModelType::plaid_institutions => (bool)PlaidInstitution::find($id),
            ModelType::plaid_bank_links => (bool)PlaidBankLink::find($id),
            ModelType::plaid_bank_accounts => (bool)PlaidBankAccount::find($id),
            ModelType::plaid_transactions => (bool)PlaidTransaction::find($id),
            ModelType::plaid_subscriptions => (bool)PlaidSubscription::find($id),
            ModelType::plaid_notify_alerts => (bool)PlaidNotifyAlert::find($id),
            ModelType::plaid_notify_recipients => (bool)PlaidNotifyRecipient::find($id),
            ModelType::plaid_company_notifications => (bool)PlaidCompanyNotification::find($id),
            ModelType::status_scope => (bool)StatusScope::find($id),
            default => throw ValidationException::withMessages([
                'ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::Logic not implemented for: ' . $modelType,
            ]),
        };
    }

    /**
     * This method returns a model found by class and id, aborts with 404 if not found
     *
     *
     * @throws ValidationException
     */
    public static function findByIdAndClass($class, $id): mixed
    {
        return match ($class) {
            Blog::class => Blog::findOrFail($id),
            Comment::class => Comment::findOrFail($id),
            Company::class => Company::findOrFail($id),
            Contract::class => Contract::findOrFail($id),
            Media::class => Media::findOrFail($id),
            MediaGallery::class => MediaGallery::findOrFail($id),
            ShoutOut::class => ShoutOut::findOrFail($id),
            User::class => User::findOrFail($id),
            UserCompanyRequest::class => UserCompanyRequest::findOrFail($id),
            default => throw ValidationException::withMessages([
                'ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::Logic not implemented for: ' . $class,
            ]),
        };
    }

    /**
     * This method receives a ModelType value and return it´s corresponding ModelClass
     *
     * @param  string  $modelType  this value must exist in the ModelType Enum, if not it will throw an error
     *
     * @throws ValidationException
     */
    public static function getClassByModelType(string $modelType): string
    {
        return match ($modelType) {
            ModelType::userType, ModelType::users => User::class,
            ModelType::companies => Company::class,
            ModelType::company_clients => CompanyClient::class,
            ModelType::companyContract, ModelType::contracts => Contract::class,
            ModelType::mediaType, ModelType::media => Media::class,
            ModelType::media_galleries, ModelType::mediaGalleryType => MediaGallery::class,
            ModelType::product, ModelType::products => Product::class,
            ModelType::blog, ModelType::blogs => Blog::class,
            ModelType::shoutOut, ModelType::shout_outs => ShoutOut::class,
            ModelType::industryEvent, ModelType::industry_events => IndustryEvent::class,
            ModelType::job_titles => JobTitle::class,
            ModelType::categories => Category::class,
            ModelType::category_company_types => CategoryCompanyType::class,
            ModelType::app_configurations => AppConfiguration::class,
            ModelType::cp_jobs => CpJobs::class,
            ModelType::profile_rule_values => ProfileRuleValue::class,
            ModelType::deals => Deal::class,
            ModelType::profanity_filters => ProfanityFilter::class,
            ModelType::company_contact_lists => CompanyContactList::class,
            ModelType::permission_features => PermissionFeature::class,
            ModelType::permission_feature_groups => PermissionFeatureGroup::class,
            ModelType::roles => Role::class,
            ModelType::template_roles => TemplateRole::class,
            ModelType::user_company_requests => UserCompanyRequest::class,
            ModelType::integration_vendor_customer => VendorCustomer::class,
            ModelType::channel_deals => ChannelDeal::class,
            ModelType::channel_deals_claimed => ChannelDealsClaimed::class,
            ModelType::plaid_institutions => PlaidInstitution::class,
            ModelType::plaid_bank_links => PlaidBankLink::class,
            ModelType::plaid_bank_accounts => PlaidBankAccount::class,
            ModelType::plaid_transactions => PlaidTransaction::class,
            ModelType::plaid_subscriptions => PlaidSubscription::class,
            ModelType::plaid_notify_alerts => PlaidNotifyAlert::class,
            ModelType::plaid_notify_recipients => PlaidNotifyRecipient::class,
            ModelType::plaid_company_notifications => PlaidCompanyNotification::class,
            ModelType::company_partners => MSPFollowingPartner::class,
            ModelType::contract_fields => ContractField::class,
            ModelType::contract_billing_contacts => ContractBillingContact::class,
            ModelType::integrations => Integration::class,
            ModelType::integration_mapping => IntegrationMapping::class,
            ModelType::integration_values => IntegrationValue::class,
            ModelType::integration_append_values => IntegrationAppendValue::class,
            ModelType::status_scope => StatusScope::class,
            default => throw ValidationException::withMessages([
                'ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::Logic not implemented for: ' . $modelType,
            ]),
        };
    }

    /**
     * This method receives a Model Class and return it´s corresponding ModelType
     *
     * @param  string  $modelClass  this value must be a class, if not it will throw an error
     *
     * @throws ValidationException
     */
    public static function getModelTypeByModelClass(string $modelClass): string
    {
        return match ($modelClass) {
            User::class => ModelType::userType,
            Company::class => ModelType::companies,
            CompanyClient::class => ModelType::company_clients,
            Contract::class => ModelType::companyContract,
            Deal::class => ModelType::deals,
            Media::class => ModelType::mediaType,
            Blog::class => ModelType::blog,
            MediaGallery::class => ModelType::mediaGalleryType,
            ProfileRuleValue::class => ModelType::profileRuleValueType,
            AppConfiguration::class => ModelType::appConfiguration,
            Product::class => ModelType::product,
            ProfanityFilter::class => ModelType::profanity,
            CpJobs::class => ModelType::cpjobs,
            JobTitle::class => ModelType::job_titles,
            Category::class => ModelType::categories,
            CompanyContactList::class => ModelType::company_contact_lists,
            UserCompanyRequest::class => ModelType::user_company_requests,
            ChannelDeal::class => ModelType::channel_deals,
            ChannelDealsClaimed::class => ModelType::channel_deals_claimed,
            PlaidInstitution::class => ModelType::plaid_institutions,
            PlaidBankLink::class => ModelType::plaid_bank_links,
            PlaidBankAccount::class => ModelType::plaid_bank_accounts,
            PlaidTransaction::class => ModelType::plaid_transactions,
            PlaidSubscription::class => ModelType::plaid_subscriptions,
            PlaidNotifyAlert::class => ModelType::plaid_notify_alerts,
            PlaidNotifyRecipient::class => ModelType::plaid_notify_recipients,
            PlaidCompanyNotification::class => ModelType::plaid_company_notifications,
            StatusScope::class => ModelType::status_scope,
            Role::class => ModelType::roles,
            default => throw ValidationException::withMessages([
                'ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::Logic not implemented for: ' . $modelClass,
            ]),
        };
    }
}
