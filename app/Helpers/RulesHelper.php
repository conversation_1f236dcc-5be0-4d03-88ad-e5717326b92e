<?php

namespace App\Helpers;

use App\Enums\ProfileRuleValueType;
use App\Models\Company\Company;
use App\Models\Profile\ProfileRule;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class RulesHelper
{
    /**
     * Retrieves all rules associated with the given profile key.
     * The rules are cached to improve performance on repeated calls.
     *
     * @param  string  $profileKey  The profile key for which to retrieve rules.
     * @return array Returns an associative array where the key is the rule name and the value is an object containing rule details (id, type, value).
     */
    public static function getProfileRules(
        string $profileKey
    ): array {
        $cacheKey = "PROFILE_RULES_{$profileKey}";

        return Cache::remember($cacheKey, now()->addSeconds(10), function () use ($profileKey) {
            $response = [];
            $query = ProfileRule::whereHas(
                'values', function ($q) use ($profileKey) {
                    $q->whereHas('companyProfileType', function ($q) use ($profileKey) {
                        $q->where('value', $profileKey);
                    })->orWhereHas('userProfileType', function ($q) use ($profileKey) {
                        $q->where('value', $profileKey);
                    });
                },
            )->with(['values' => function ($q) use ($profileKey) {
                $q->whereHas('companyProfileType', function ($q) use ($profileKey) {
                    $q->where('value', $profileKey);
                })->orWhereHas('userProfileType', function ($q) use ($profileKey) {
                    $q->where('value', $profileKey);
                });
            }]);
            $rules = $query->get();
            foreach ($rules as $rule) {
                $value = $rule->values->first();
                $response[$rule->rule] = [
                    'id' => $rule->id,
                    'type' => $rule->value_type,
                    'value' => self::castRuleValue($rule->value_type, $value->rule_value),
                ];
            }

            return $response;
        });
    }

    /**
     * Retrieves all custom rules associated with the given owner (Company or User).
     * The rules are cached to improve performance on repeated calls.
     *
     * @param  Company|User  $owner  The owner entity (Company or User) for which to retrieve custom rules.
     * @return array Returns an associative array where the key is the rule name and the value is an object containing rule details (id, type, value, expires_at).
     */
    public static function getProfileCustomRules(
        Company|User $owner
    ): array {
        $cacheKey = "PROFILE_CUSTOM_RULES_{$owner->id}";

        return Cache::remember($cacheKey, now()->addSeconds(10), function () use ($owner) {
            $owner->load('profileType');
            $ownerType = get_class($owner);
            $ownerProfile = $owner->profileType ?? null;
            $response = [];
            if (!$ownerProfile) {
                return $response;
            }

            $query = ProfileRule::whereHas(
                'customValues', function ($q) use ($owner, $ownerType, $ownerProfile) {
                    $q->where([
                        'owner_type' => $ownerType,
                        'owner_id' => $owner->id,
                        'profile_id' => $ownerProfile->id,
                    ])->where(function ($q) {
                        $q->whereNull('expires_at')->orWhere('expires_at', '>=', now());
                    });
                },
            )->with(['customValues' => function ($q) use ($owner, $ownerType, $ownerProfile) {
                $q->where([
                    'owner_type' => $ownerType,
                    'owner_id' => $owner->id,
                    'profile_id' => $ownerProfile->id,
                ])->where(function ($q) {
                    $q->whereNull('expires_at')->orWhere('expires_at', '>=', now());
                });
            }]);
            $rules = $query->get();
            foreach ($rules as $rule) {
                $value = $rule->customValues->first();
                $response[$rule->rule] = [
                    'id' => $rule->id,
                    'type' => $rule->value_type,
                    'value' => self::castRuleValue($rule->value_type, $value->rule_value),
                    'expires_at' => $value->expires_at,
                ];
            }
            Log::debug(__CLASS__ . '::' . __FUNCTION__ . '::Custom Rules For ID: ' . $owner->id . ': ' . json_encode($response, JSON_PRETTY_PRINT));

            return $response;
        });
    }

    public static function getProfileRulesValues(
        string $profileKey,
        null|Company|User $owner = null
    ): array {
        $source = __CLASS__ . '::' . __FUNCTION__ . '::';
        $profileRules = self::getProfileRules($profileKey);
        Log::debug($source .
            '::Profile Rules Profile Key: ' . $profileKey .
            ': ' . json_encode($profileRules, JSON_PRETTY_PRINT)
        );
        $customRules = !$owner ? [] : self::getProfileCustomRules($owner);
        Log::debug($source .
            '::Custom Rules Profile Key: ' . $profileKey .
            ' and Owner ID: ' . ($owner ? $owner->id : 'null') .
            ': ' . json_encode($customRules, JSON_PRETTY_PRINT)
        );

        $response = array_merge($profileRules, $customRules);
        Log::debug($source . '::Merged Rules: ' . json_encode($response, JSON_PRETTY_PRINT));

        return $response;
    }

    /**
     * Casts the rule value to the appropriate PHP type based on the rule value type.
     *
     * @param  string  $type  The type of the rule value (e.g., boolean, numeric, datetime).
     * @param  string|null  $value  The value to be cast.
     * @return mixed Returns the value cast to the appropriate PHP type, or null if the value is null.
     */
    public static function castRuleValue(
        string $type,
        ?string $value = null
    ): mixed {
        if ($value === null) {
            return null;
        }

        return match ($type) {
            ProfileRuleValueType::BooleanType => in_array(strtolower($value), ['true', '1', 'yes']),
            ProfileRuleValueType::NumericType => (float)$value,
            ProfileRuleValueType::DatetimeType => Carbon::parse($value),
            default => $value,
        };
    }

    public static function getProfileRuleValue(
        string $profileKey,
        string $ruleName,
        null|Company|User $owner = null
    ): mixed {
        $source = __CLASS__ . '::' . __FUNCTION__ . '::';
        $ruleConfigName = self::prepareConfigurationName($profileKey, $ruleName);
        $profileRulesValues = self::getProfileRulesValues($profileKey, $owner);
        $ruleValueArray = $profileRulesValues[$ruleName] ?? null;
        if ($ruleValueArray === null) {
            Log::warning("No value configured for Profile rule '{$ruleConfigName}'");

            return null;
        }
        Log::debug($source . 'Rule Value Array: ' . json_encode($ruleValueArray, JSON_PRETTY_PRINT));

        return self::parseRuleValue($ruleValueArray);
    }

    private static function prepareConfigurationName(
        string $profileTypeName,
        string $configName
    ): string {
        return implode('.', ['business_rules', $profileTypeName, $configName]);
    }

    private static function parseRuleValue(array $ruleValueArray)
    {
        $ruleValue = Arr::get($ruleValueArray, 'value');
        Log::debug(__CLASS__ . '::' . __FUNCTION__ . '::Rule Value: ' . $ruleValue);
        if (is_null($ruleValue)) {
            return null;
        }
        $ruleValueType = Arr::get($ruleValueArray, 'type');

        return self::castRuleValue($ruleValueType, $ruleValue);
    }

    // public static function getProfileRuleValue($rules, $profileTypeName, $ruleName)
    // {
    //     if (!isset($rules[$profileTypeName])) {
    //         return null;
    //     }
    //     if (!isset($rules[$profileTypeName][$ruleName])) {
    //         return null;
    //     }

    //     return $rules[$profileTypeName][$ruleName];
    // }
}
