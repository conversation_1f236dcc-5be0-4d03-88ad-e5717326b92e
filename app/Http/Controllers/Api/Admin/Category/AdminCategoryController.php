<?php

namespace App\Http\Controllers\Api\Admin\Category;

use App\Enums\AffiliateBrands\CategoriesFilters;
use App\Helpers\UtilityHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Category\CategoryDeleteRequest;
use App\Http\Requests\Admin\Category\CategoryShowAllRequest;
use App\Http\Requests\Admin\Category\CategoryStoreRequest;
use App\Http\Requests\Admin\Category\CategoryUpdateRequest;
use App\Http\Resources\Admin\Category\AdminCategoryResource;
use App\Models\Category\Category;
use App\Models\Category\CategoryCompanyType;
use App\Models\Company\CompanyType;
use App\Services\CategoryService;
use Carbon\Carbon;
use DateTime;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use stdClass;

class AdminCategoryController extends Controller
{
    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/admin/categories/filters",
     *     operationId="admin/categories/showAllFilters",
     *     tags={"AdminCategoryController"},
     *     summary="Get categories filters",
     *     description="Get categories filters",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws \Exception
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/admin/categories
    // needs Bearer Token
    public function showAllFilters()
    {
        $categories = Category::select('created_at')->get();
        $filters = [];
        $result = [];
        if ($categories->count() > 0) {
            $filters['is_hidden'] = CategoriesFilters::is_hidden;
            $filters['is_hidden']['items'][] = ['id' => 1, 'name' => 'Hidden'];
            $filters['is_hidden']['items'][] = ['id' => 0, 'name' => 'Visible'];
            // Filter by company type
            $filters['company_type'] = CategoriesFilters::company_type;
            $filters['company_type']['items'][] = ['id' => 'DEFAULT', 'name' => 'Default'];
            $companyTypes = CategoryService::getCategoriesCompanyTypes();
            foreach ($companyTypes as $companyType) {
                $filters['company_type']['items'][] = [
                    'id' => $companyType->value,
                    'name' => $companyType->label,
                ];
            }
            $filters['created_at'] = CategoriesFilters::created_at;
            $filters['created_at']['items'] =
                $categories->unique('created_at')->map(function ($affiliateBrand) {
                    $nameDate = new Carbon(new DateTime($affiliateBrand->created_at));
                    $createdAt = new Carbon(new DateTime($affiliateBrand->created_at));
                    $dateObj = new stdClass();
                    $dateObj->id = $createdAt->format('Y-m-d');
                    $dateObj->name = $nameDate->format('M d, Y');

                    return $dateObj;
                })->unique('name')->sortByDesc('id')->values();
            $result = [
                'filters' => $filters,
            ];
        }

        return response()->json($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/admin/categories",
     *     operationId="admin/categories/showAll",
     *     tags={"AdminCategoryController"},
     *     summary="Get categories list",
     *     description="Get categories list",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/admin/categories
    // needs Bearer Token
    public function showAll(CategoryShowAllRequest $request)
    {
        $query = $this->prepareShowAllQuery($request);
        $result = UtilityHelper::getSearchRequestQueryResults($request, $query);
        if ($result instanceof LengthAwarePaginator) {
            $pageResults = $result->getCollection();
        } else {
            $pageResults = $result;
        }
        $pageResults = CategoryService::appendUsage($pageResults);
        if ($result instanceof LengthAwarePaginator) {
            $result->setCollection($pageResults);
        } else {
            $result = $pageResults;
        }

        return AdminCategoryResource::collection($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/admin/categories/store",
     *     operationId="admin/categories/store",
     *     tags={"AdminCategoryController"},
     *     summary="Store new category",
     *     description="Creates a new category",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *
     *         @OA\JsonContent(ref="#/components/schemas/CategoryStoreRequest")
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/admin/categories/store
    /* needs Bearer Token */
    public function store(CategoryStoreRequest $request): JsonResponse
    {
        CategoryService::validateNameExistence($request->name, $request->parent_id);
        $category = Category::make($request->validated());
        if ($request->has('parent_id')) {
            CategoryService::validateParentVisibility($request->parent_id, $request->is_hidden);
        }
        $category->save();
        if ($request->has('company_type_id')) {
            CategoryCompanyType::create([
                'category_id' => $category->id,
                'company_type_id' => $request->company_type_id,
            ]);
        }

        return response()->json(new AdminCategoryResource($category));
    }
    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/admin/categories/update",
     *     operationId="admin/categories/update",
     *     tags={"AdminCategoryController"},
     *     summary="Update category",
     *     description="Returns ok",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *
     *         @OA\JsonContent(ref="#/components/schemas/CategoryUpdateRequest")
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: PUT
    // http://127.0.0.1:8000/api/v1/admin/categories/update
    // needs Bearer Token
    public function update(CategoryUpdateRequest $request): JsonResponse
    {
        $category = Category::findOrFail($request->id);
        if ($category->parent_id && $request->has('is_hidden')) {
            CategoryService::validateParentVisibility($request->parent_id, $request->is_hidden);
        }
        if ($request->has('name') && (strtolower($request->name) !== $category->name)) {
            if ($request->has('parent_id')) {
                $parentId = $request->parent_id;
            } else {
                $parentId = $category->parent_id;
            }
            CategoryService::validateNameExistence($request->name, $parentId, $request->id, $request->company_type_id);
        }
        $category->update($request->validated());
        if ($request->has('company_type_id')) {
            CategoryCompanyType::where('category_id', $category->id)
                ->where('company_type_id', $request->company_type_id)
                ->delete();
            CategoryCompanyType::create([
                'category_id' => $category->id,
                'company_type_id' => $request->company_type_id,
            ]);
        }
        // If the requested category is a parent, and it is updating its visibility,
        // it should update all its children visibility
        if ($category->parent_id === null && $request->has('is_hidden')) {
            $category->subCategories()->update(['is_hidden' => $request->is_hidden]);
        }
        // If the requested category is a parent, and it is updating its color,
        // it should update all its children color
        if ($category->parent_id === null && $request->has('color')) {
            $category->subCategories()->update(['color' => $request->color]);
        }
        // Appending category usage count
        $collectionCat = CategoryService::appendCategoryCount(collect($category));

        return response()->json(new AdminCategoryResource($collectionCat));
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Delete(
     *     path="/api/v1/admin/categories/delete",
     *     operationId="admin/categories/delete",
     *     tags={"AdminCategoryController"},
     *     summary="Delete existing category",
     *     description="Deletes a record and returns no content",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *
     *         @OA\JsonContent(ref="#/components/schemas/CategoryDeleteRequest")
     *     ),
     *
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: DELETE
    // http://127.0.0.1:8000/api/v1/admin/categories/delete
    // needs Bearer Token
    public function delete(CategoryDeleteRequest $request): JsonResponse
    {
        Category::where('parent_id', $request->id)->delete();
        Category::destroy($request->id);

        return response()->json();
    }

    private function prepareShowAllQuery(CategoryShowAllRequest $request): Builder
    {
        $query = Category::with('companyTypes:company_types.id,company_types.value');
        if ($request->has('only_parents') && $request->only_parents) {
            $query = $query->whereNull('parent_id');
        }
        if ($request->has('search_word')) {
            $lowerSearchWord = '%' . strtolower($request->search_word) . '%';
            $subCategoriesParentsIds = Category::select('parent_id')
                ->whereNotNull('parent_id')
                ->whereRaw('(lower(name) like ?)', [$lowerSearchWord])
                ->get()->pluck('parent_id')->toArray();
            if (count($subCategoriesParentsIds) > 0) {
                $query->where(function ($query) use ($subCategoriesParentsIds, $lowerSearchWord) {
                    $query->whereRaw('(lower(name) like ?)', [$lowerSearchWord])
                        ->orWhereIn('id', $subCategoriesParentsIds);
                });
            } else {
                $query->whereRaw('(lower(name) like ?)', [$lowerSearchWord]);
            }
        }
        if ($request->has('created_at')) {
            $query->whereIn(DB::raw('DATE(categories.created_at)'), $request->get('created_at'));
        }
        if ($request->has('is_hidden')) {
            $query->where('is_hidden', (bool)$request->is_hidden);
        }
        if ($request->has('parent_id')) {
            $query->where('parent_id', $request->parent_id);
        }
        if ($request->has('featured')) {
            $query->where('featured', (bool)$request->featured);
        }
        if ($request->has('company_type')) {
            if ($request->company_type === 'DEFAULT') {
                $query = CategoryService::getGlobalCategoriesQuery($query);
            } else {
                // FOR NOW IS JUST MSP_CLIENT, OTHERS MAY BE ADDED IN THE FUTURE
                $companyType = CompanyType::select('id')
                    ->where('value', $request->company_type)
                    ->first();
                $query = CategoryService::getParticularCompanyTypeQuery($query, $companyType->id);
            }
        }

        return $query;
    }
}
