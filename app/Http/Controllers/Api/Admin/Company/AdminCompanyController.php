<?php

namespace App\Http\Controllers\Api\Admin\Company;

use App\Enums\AffiliateBrands\AffiliateFilters;
use App\Enums\AffiliateBrands\AffiliateFiltersSort;
use App\Enums\AnalyticAction;
use App\Enums\Company\CompanyFilters;
use App\Enums\Company\CompanyFiltersSort;
use App\Enums\Company\CompanyPRMStatus;
use App\Enums\Company\CompanySubscriptionStatus;
use App\Enums\Partner\PartnerPortalInvitationInitiator;
use App\Enums\Partner\PartnerPortalInvitationStatus;
use App\Helpers\UtilityHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Company\AdminCompanyPartnerPortalFlagRequest;
use App\Http\Requests\Admin\Company\AdminCompanyRequest;
use App\Http\Requests\Admin\Company\AdminCompanyUpdateIsDistributorFlagRequest;
use App\Http\Requests\Admin\Company\AdminCompanyUpdateManageAffiliatesFlagRequest;
use App\Http\Requests\Admin\Company\AdminCompanyUpdateManageClientsFlagRequest;
use App\Http\Requests\Admin\Company\AdminCompanyUpdateManageExpensesFlagRequest;
use App\Http\Resources\Admin\Company\AdminCompanyResource;
use App\Http\Resources\Company\CompanyResource;
use App\Http\Resources\Partner\Invite\OpenInvitationResource;
use App\Jobs\SyncAllHubspotDeals;
use App\Jobs\SyncHubspotCompany;
use App\Jobs\SyncHubspotCompanyCategory;
use App\Jobs\SyncHubspotCompanyUrl;
use App\Models\AffiliateBrand\AffiliateBrand;
use App\Models\Company\Company;
use App\Models\MSPFollowingPartner;
use App\Models\MyStack\MyStack;
use App\Models\User;
use App\Models\UserNotification;
use App\Services\Company\CompanyService;
use App\Services\Partner\InvitePartnerService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class AdminCompanyController extends Controller
{
    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/admin/company",
     *     operationId="admin/company/showAll",
     *     tags={"AdminCompanyController"},
     *     summary="Get companies list",
     *     description="Get companies list",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/admin/company
    // OPTIONAL PARAMS
    /*
     * ?paged=BOOLEAN&page=PAGE&order_by=COLUMN_NAME&sort=SORT_VALUE&search_word=SEARCH_WORD
     * */
    // needs Bearer Token
    public function showAll(AdminCompanyRequest $request)
    {
        $useFuzzy = $request->has('search_word') && $request->has('use_fuzzy_search') && (bool)$request->use_fuzzy_search;
        $searchTerms = $useFuzzy
            ? explode(' ', strtolower($request->search_word))
            : null;
        $query = Company::with('subscriptionHistory')
            ->selectRaw('companies.id, companies.name, companies.friendly_url, companies.created_at, companies.founded,
                companies.hubspot_id, company_profile_types.label as profile_type_name,
                company_types.id as company_type_id, company_types.value as company_type_value,
                company_types.label as company_type_label, company_types.order as company_type_order,
                company_types.type_is_of_vendor as type_is_of_vendor,
                CAST(CASE WHEN company_types.type_is_of_vendor IS TRUE THEN (
                    case when companies.partner_flag is true then 1 else 0 end
                ) else -1 end as INT) as partner_flag_num,
                companies.is_distributor as is_distributor')
            ->join('company_profile_types',
                'companies.company_profile_types_id', '=', 'company_profile_types.id')
            ->join('company_types', 'companies.type', '=', 'company_types.id')
            ->when($request->has('search_word'), function ($query) use ($request, $useFuzzy, $searchTerms) {
                $query->where(function ($query) use ($request, $useFuzzy, $searchTerms) {
                    $searchText = Str::lower(urldecode(($request->search_word)));
                    $searchText = str_replace("'", "''", $searchText);
                    // Applying search by full text
                    $query->whereRaw("(lower(companies.name) LIKE '%" . $searchText . "%')")
                        ->orWhere('companies.hubspot_id', $request->search_word);
                    // Applying fuzzy search
                    if ($useFuzzy) {
                        foreach ($searchTerms as $term) {
                            $query->orWhereRaw("similarity(companies.name, '" . $term . "') > 0");
                        }
                    }
                });
            })->when($request->has('parent_id'), function ($query) use ($request) {
                $query->where('parent_id', $request->parent_id);
            })->when($request->has('locations'), function ($query) use ($request) {
                $query->whereIn('address', $request->locations);
            })->when($request->has('subscription_status'), function ($query) use ($request) {
                $query->join('companies_subscription_history', 'companies_subscription_history.company_id', '=', 'companies.id')
                    ->where('companies_subscription_history.is_active', $request->subscription_status)
                    ->where(function ($query) {
                        $query->whereRaw('? BETWEEN companies_subscription_history.started_at AND companies_subscription_history.ended_at', [now()])
                            ->orWhereNull('companies_subscription_history.ended_at');
                    });
            })->when($request->has('partner_flag'), function ($query) use ($request) {
                $query->where('partner_flag', (bool)$request->partner_flag)
                    ->where('company_types.type_is_of_vendor', true);
            })->when($request->has('company_profile_types'), function ($query) use ($request) {
                $query->whereIn('companies.company_profile_types_id', $request->company_profile_types);
            })->when($request->has('types'), function ($query) use ($request) {
                $query->whereIn('company_types.id', $request->types);
            })->when($useFuzzy, function ($query) use ($request) {
                $query->orderByRaw('similarity(companies.name, ?) DESC', [$request->search_word]);
            });
        $result = UtilityHelper::getSearchRequestQueryResults($request, $query, 0, 'companies', !$useFuzzy);

        return AdminCompanyResource::collection($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/admin/company/filters",
     *     operationId="admin/company/showAllFilters",
     *     tags={"AdminCompanyController"},
     *     summary="Get filters for the companies list",
     *     description="Get filters for the companies list",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/admin/company/filters
    // NO PARAMS
    // needs Bearer Token
    public function showAllFilters(): JsonResponse
    {
        $companies = Company::with('enumType', 'companyProfileType')->get();
        $filters = [];
        if ($companies->count() > 0) {
            $partnerFlags = $companies->pluck('partner_flag')->unique();
            if ($partnerFlags->count() > 0) {
                $filters['partner_flag'] = CompanyFilters::partner_flag;
                $partnerFlags->map(function ($partnerFlag) use (&$filters) {
                    $filters['partner_flag']['items'][] = [
                        'id' => $partnerFlag ? '1' : '0',
                        'name' => CompanyPRMStatus::getKey($partnerFlag),
                    ];
                });
            }
            $profileTypes = $companies->pluck('companyProfileType')->filter()->unique()->sortBy('label');
            if ($profileTypes->count() > 0) {
                $filters['company_profile_types'] = CompanyFilters::company_profile_types;
                $profileTypes->map(function ($profileType) use (&$filters) {
                    $filters['company_profile_types']['items'][] = ['id' => '' . $profileType->id, 'name' => $profileType->label];
                });
            }
            $types = $companies->pluck('enumType')->filter()->unique()->sortBy('label');
            if ($types->count() > 0) {
                $filters['types'] = CompanyFilters::types;
                $types->map(function ($type) use (&$filters) {
                    $filters['types']['items'][] = ['id' => '' . $type->id, 'name' => $type->label];
                });
            }

            return response()->json([
                'filters' => $filters,
                'sorts' => CompanyFiltersSort::asArray(),
            ]);
        }

        return response()->json();
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/admin/company/{company}/affiliates-filters",
     *     operationId="admin/company/showAffiliatesFilters",
     *     tags={"AdminCompanyController"},
     *     summary="Get affiliate filters for the companies list",
     *     description="Get affiliate filters for the companies list",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/admin/company/{company}/affiliates-filters
    // NO PARAMS
    // needs Bearer Token
    public function showAffiliatesFilters(Company $company): JsonResponse
    {
        $filters = [];
        if ($company->affiliates->count() > 0) {
            $filters['subscription_status'] = AffiliateFilters::subscription_status;
            $filters['subscription_status']['items'] = CompanySubscriptionStatus::asSelectArray();
            $filters['locations'] = AffiliateFilters::locations;
            $company->affiliates->map(function ($affiliate) use (&$filters) {
                if ($affiliate->address) {
                    $filters['locations']['items'][] = ['id' => $affiliate->address, 'name' => $affiliate->address];
                }
            });

            return response()->json([
                'filters' => $filters,
                'sorts' => AffiliateFiltersSort::asArray(),
            ]);
        }

        return response()->json();
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/admin/company/{company}/open-invitation/refresh",
     *     operationId="refreshVendorOpenInvitation",
     *     tags={"AdminCompanyController"},
     *     summary="Invalidates the current open invitation for Partner Portal and creates a new one",
     *     description="Invalidates the current open invitation for Partner Portal and creates a new one",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/admin/company/{company}/open-invitation/refresh
    // Bearer token needed
    public function refreshVendorOpenInvitation(Company $company): OpenInvitationResource
    {
        CompanyService::validateCompanyIsVendor($company->id);
        CompanyService::validateCompanyPartnerPageIsActive($company);

        $invite = InvitePartnerService::refreshOpenInvitation($company);

        return new OpenInvitationResource($invite);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/admin/company/sync-all-hubspot",
     *     operationId="admin/company/syncAllHubspot",
     *     tags={"AdminCompanyController"},
     *     summary="Sync All Hobspot jobs for a Company to channel",
     *     description="Sync All Hobspot jobs for a Company to channel",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=false
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/admin/company/sync-all-hubspot
    // needs Bearer Token
    public function syncAllHubspot(): JsonResponse
    {
        // SyncHubspotContact is for syncing contacts from channel to hubspot you can uncomment it if you want to sync contacts
        // SyncHubspotContact::dispatch();
        SyncHubspotCompany::dispatch();
        SyncHubspotCompanyCategory::dispatch();
        SyncHubspotCompanyUrl::dispatch();
        SyncAllHubspotDeals::dispatch();

        return response()->json();
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/admin/company/sync-company-categories",
     *     operationId="admin/company/syncCompanyCategoriesToHubspot",
     *     tags={"AdminCompanyController"},
     *     summary="Sync Company Categories from Channel to Hubspot",
     *     description="Sync Company Categories from Channel to Hubspot",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=false
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/admin/company/sync-company-categories
    // needs Bearer Token
    public function syncCompanyCategoriesToHubspot(): JsonResponse
    {
        SyncHubspotCompanyCategory::dispatch();

        return response()->json();
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/admin/company/sync-company-url",
     *     operationId="admin/company/syncCompanyUrlToHubspot",
     *     tags={"AdminCompanyController"},
     *     summary="Sync Company url from Channel to Hubspot",
     *     description="Sync Company url from Channel to Hubspot",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=false
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // http://127.0.0.1:8000/api/v1/admin/company/sync-company-url
    // needs Bearer Token
    public function syncCompanyUrlToHubspot(): JsonResponse
    {
        SyncHubspotCompanyUrl::dispatch();

        return response()->json();
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/admin/company/pull-hubspot-deals",
     *     operationId="admin/company/pullAllCompaniesHubspotDeals",
     *     tags={"AdminCompanyController"},
     *     summary="Pull all companies hubspot subscription",
     *     description="Sync Company url from Channel to Hubspot",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=false
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // http://127.0.0.1:8000/api/v1/admin/company/pull-hubspot-deals
    // needs Bearer Token
    public function pullAllCompaniesHubspotDeals(): JsonResponse
    {
        SyncAllHubspotDeals::dispatch();

        return response()->json();
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/admin/company/{company}/partner-flag",
     *     operationId="company/companyPartnerPortalFlag",
     *     tags={"AdminCompanyController"},
     *     summary="Update company partner flag",
     *     description="Returns ok",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: PUT
    // http://127.0.0.1:8000/api/v1/admin/company/{company}/partner-flag
    // BODY
    /*{
        "partner_flag" : "required|boolean"
    }*/
    // needs Bearer Token
    public function companyPartnerPortalFlag(
        AdminCompanyPartnerPortalFlagRequest $request, Company $company): CompanyResource
    {
        CompanyService::validateCompanyIsVendor($company->id);
        $company->partner_flag = $request->partner_flag;
        if ($company->partner_flag) {
            $company->subdomain = CompanyService::getCompanySubdomain($company);
        }
        $company->save();

        if ($request->has('partner_flag') && $request->partner_flag) {
            $stacksCompany = MyStack::where('stack_company_id', $company->id)->get();
            if (count($stacksCompany) > 0) {
                foreach ($stacksCompany as $followerPartnerCompany) {
                    $checkInvite = MSPFollowingPartner::where('followed_partner_id', $company->id)
                        ->where('follower_partner_id', $followerPartnerCompany->company_id)
                        ->where('initiator', 'user')->first();
                    if (empty($checkInvite) && (!empty($followerPartnerCompany->user_id))) {
                        $user = User::select('email')->where('id', $followerPartnerCompany->user_id)->first();
                        MSPFollowingPartner::create([
                            'followed_partner_id' => $company->id,
                            'follower_partner_id' => $followerPartnerCompany->company_id,
                            'status' => PartnerPortalInvitationStatus::Requested,
                            'initiator' => PartnerPortalInvitationInitiator::User,
                            'invited_by' => $followerPartnerCompany->user_id,
                            'invited_at' => Carbon::now(),
                            'email' => $user->email,
                        ]);
                    }
                }
            }
        }

        if ($request->has('partner_flag') && !$request->partner_flag) {
            // As the partner flag is been disabled we need to delete the invite notifications
            UserNotification::where('subject_id', $company->id)
                ->where('subject_type', Company::class)
                ->where('action', AnalyticAction::partnerPortalRequestInvite)
                ->delete();
        }

        return new CompanyResource($company);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/admin/company/{company}/is_distributor",
     *     operationId="company/updateCompanyIsDistributor",
     *     tags={"AdminCompanyController"},
     *     summary="Update company partner flag",
     *     description="Returns ok",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: PUT
    // http://127.0.0.1:8000/api/v1/admin/company/{company}/is_distributor
    // BODY
    /*{
        "is_distributor" : "required|boolean"
    }*/
    // needs Bearer Token
    public function updateCompanyIsDistributor(
        AdminCompanyUpdateIsDistributorFlagRequest $request, Company $company): CompanyResource
    {
        CompanyService::validateCompanyIsVendor($company);
        $company->is_distributor = (bool)$request->is_distributor;
        $company->save();

        return new CompanyResource($company);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/admin/company/{company}/manage-clients",
     *     operationId="company/updateCompanyManageClients",
     *     tags={"AdminCompanyController"},
     *     summary="Update company manage clients flag",
     *     description="Returns ok",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: PUT
    // http://127.0.0.1:8000/api/v1/admin/company/{company}/manage-clients
    // BODY
    /*{
        "manage_clients" : "required|boolean"
    }*/
    // needs Bearer Token
    public function updateCompanyManageClients(
        AdminCompanyUpdateManageClientsFlagRequest $request, Company $company): CompanyResource
    {
        CompanyService::validateCompanyIsMSP($company);
        $company->manage_clients = (bool)$request->manage_clients;
        $company->save();

        return new CompanyResource($company);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/admin/company/{company}/manage-affiliates",
     *     operationId="company/updateCompanyManageAffiliates",
     *     tags={"AdminCompanyController"},
     *     summary="Update company manage affiliates flag",
     *     description="Returns ok",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: PUT
    // http://127.0.0.1:8000/api/v1/admin/company/{company}/manage-affiliates
    // BODY
    /*{
        "manage_affiliates" : "required|boolean"
    }*/
    // needs Bearer Token
    public function updateCompanyManageAffiliates(
        AdminCompanyUpdateManageAffiliatesFlagRequest $request, Company $company): CompanyResource
    {
        CompanyService::validateCompanyIsNotLocation($company);
        $company->manage_affiliates = (bool)$request->manage_affiliates;
        $company->save();
        // if the user is disabling the manage_affiliates the company needs to be removed from the affiliate brand
        if ($company->manage_affiliates === false) {
            AffiliateBrand::where('main_company_id', $company->id)->update(['main_company_id' => null]);
        }

        return new CompanyResource($company);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/admin/company/{company}/manage-expenses",
     *     operationId="company/updateCompanyManageExpenses",
     *     tags={"AdminCompanyController"},
     *     summary="Update company manage expenses flag",
     *     description="Returns ok",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: PUT
    // http://127.0.0.1:8000/api/v1/admin/company/{company}/manage-expenses
    // needs Bearer Token
    public function updateCompanyManageExpenses(
        AdminCompanyUpdateManageExpensesFlagRequest $request, Company $company): CompanyResource
    {
        CompanyService::validateCompanyIsMSP($company);
        $company->manage_expenses = (bool)$request->manage_expenses;
        $company->save();

        return new CompanyResource($company);
    }
}
