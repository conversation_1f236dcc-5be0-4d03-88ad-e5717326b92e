<?php

namespace App\Http\Controllers\Api\Admin\Permission\Role;

use App\Helpers\UtilityHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Permission\Role\RoleDeleteRequest;
use App\Http\Requests\Admin\Permission\Role\RoleDuplicateRequest;
use App\Http\Requests\Admin\Permission\Role\RoleShowAllRequest;
use App\Http\Requests\Admin\Permission\Role\RoleStoreRequest;
use App\Http\Requests\Admin\Permission\Role\RoleUpdateRequest;
use App\Http\Resources\Permission\Role\RoleResource;
use App\Log;
use App\Models\Permission\Group\PermissionGroup;
use App\Models\Permission\Role\PermissionRole;
use App\Models\Permission\Role\Role;
use App\Models\Permission\Role\RoleUser;
use App\Services\Permission\RoleUserService;
use Exception;
use Illuminate\Database\QueryException;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class AdminRoleController extends Controller
{
    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/admin/permission/role",
     *     operationId="admin/permission/role/showAll",
     *     tags={"AdminRoleController"},
     *     summary="Get all the roles for a company",
     *     description="Returns all the roles for a company",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/admin/permission/role
    // Bearer token needed
    public function showAll(RoleShowAllRequest $request): AnonymousResourceCollection
    {
        $query = Role::select('roles.id', 'roles.company_id', 'roles.title', 'roles.display_name', 'roles.description',
            'roles.key', 'roles.template_role_id', 'roles.created_at', 'roles.updated_at')
            ->with('permissionGroups:permission_groups.id,permission_groups.permission_feature_id,'
                . 'permission_groups.key,permission_groups.title,permission_groups.description,'
                . 'permission_groups.created_at,permission_groups.updated_at',
                'permissionGroups.permissionFeature:permission_features.id,permission_features.key,'
                . 'permission_features.title,permission_features.description,'
                . 'permission_features.created_at,permission_features.updated_at')
            ->where('company_id', $request->company_id)
            ->when($request->has('search_word'), static function ($query) use ($request) {
                $query->where(function ($query) use ($request) {
                    $searchWords = explode(' ', strtolower($request->search_word));
                    foreach ($searchWords as $searchWord) {
                        $nameLikeParameter = "%{$searchWord}%";
                        $query->whereRaw('lower(roles.title) LIKE ?', [$nameLikeParameter]);
                        $query->orWhereRaw('lower(roles.description) LIKE ?', [$nameLikeParameter]);
                    }
                });
            });
        $results = UtilityHelper::getSearchRequestQueryResults($request, $query);
        if ($results instanceof LengthAwarePaginator) {
            $pageResults = $results->getCollection();
        } else {
            $pageResults = $results;
        }
        $roleUsers = RoleUser::select('id', 'role_id', 'user_id')
            ->whereIn('role_id', $pageResults->pluck('id'))
            ->get();
        $pageResults->transform(function ($item) use ($roleUsers) {
            $item->users_count = $roleUsers->where('role_id', $item->id)->count();

            return $item;
        });
        if ($results instanceof LengthAwarePaginator) {
            $results->setCollection($pageResults);
        } else {
            $results = $pageResults;
        }

        return RoleResource::collection($results);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/admin/permission/role/store",
     *     operationId="admin/permission/role/store",
     *     tags={"AdminRoleController"},
     *     summary="Store a role",
     *     description="Store a role",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws Exception
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/admin/permission/role/store
    // Bearer token needed*/
    public function store(RoleStoreRequest $request): RoleResource
    {
        DB::beginTransaction();

        try {
            $requestAttributes = $request->validated();
            $requestAttributes['key'] = strtoupper(UtilityHelper::generateUniqueWord(
                'roles',
                'key',
                UtilityHelper::generateKeyFromString($request->title, 3, '_', true)
            ));
            $requestAttributes['company_id'] = app('asCompanyId');
            $role = Role::create($requestAttributes);
            $this->appendPermissionGroups($role, $request->permission_groups);
        } catch (Exception $ex) {
            DB::rollBack();
            Log::error('ERROR::' . __CLASS__ . '::' . __FUNCTION__
                . '::' . $ex->getMessage() . '::' . $ex->getTraceAsString());

            throw ValidationException::withMessages([
                'ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::' . Role::class . '::' . $ex->getMessage(),
            ]);
        }
        DB::commit();

        return new RoleResource($role);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/admin/permission/role/update",
     *     operationId="admin/permission/role/update",
     *     tags={"AdminRoleController"},
     *     summary="Update a role",
     *     description="Update a role",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws Exception
     */
    // </editor-fold>
    // API Call: PUT
    // http://127.0.0.1:8000/api/v1/admin/permission/role/update
    // Bearer token needed */
    public function update(RoleUpdateRequest $request): RoleResource
    {
        DB::beginTransaction();

        try {
            $role = Role::find($request->id);
            $role->update($request->validated());
            $this->appendPermissionGroups($role, $request->permission_groups);
        } catch (Exception $ex) {
            DB::rollBack();
            Log::error('ERROR::' . __CLASS__ . '::' . __FUNCTION__
                . '::' . $ex->getMessage() . '::' . $ex->getTraceAsString());

            throw ValidationException::withMessages([
                'ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::' . Role::class . '::' . $ex->getMessage(),
            ]);
        }
        DB::commit();

        return new RoleResource($role);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/admin/permission/role/duplicate",
     *     operationId="admin/permission/role/duplicate",
     *     tags={"AdminRoleController"},
     *     summary="Duplicate a role",
     *     description="Duplicate a role",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws Exception
     */
    // </editor-fold>
    // API Call: PUT
    // http://127.0.0.1:8000/api/v1/admin/permission/role/duplicate
    // Bearer token needed */
    public function duplicate(RoleDuplicateRequest $request): RoleResource
    {
        DB::beginTransaction();

        try {
            $actualRole = Role::select('id', 'company_id', 'display_name', 'description', 'key', 'is_admin')
                ->with('permissionGroups:key')
                ->where('id', $request->id)
                ->first();
            $duplicateRole = new Role();
            $duplicateRole->company_id = $actualRole?->company_id;
            $duplicateRole->title = UtilityHelper::generateUniqueWord(
                'roles', 'title', 'Duplicated ' . $actualRole?->display_name, ' ');
            $duplicateRole->display_name = $duplicateRole->title;
            $duplicateRole->description = $actualRole?->description;
            $duplicateRole->key = $actualRole?->key . '_DUPLICATE_' . now()->timestamp;
            $duplicateRole->template_role_id = null;
            $duplicateRole->is_admin = $actualRole?->is_admin;
            $duplicateRole = Role::create($duplicateRole->toArray());
            $this->appendPermissionGroups(
                $duplicateRole, $actualRole?->permissionGroups->pluck('key')->toArray());
        } catch (Exception $ex) {
            DB::rollBack();
            Log::error('ERROR::' . __CLASS__ . '::' . __FUNCTION__
                . '::' . $ex->getMessage() . '::' . $ex->getTraceAsString());

            throw ValidationException::withMessages([
                'ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::' . Role::class . '::' . $ex->getMessage(),
            ]);
        }
        DB::commit();

        return new RoleResource($duplicateRole);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Delete(
     *     path="/api/v1/admin/permission/role/delete",
     *     operationId="admin/permission/role/delete",
     *     tags={"AdminRoleController"},
     *     summary="Delete a role",
     *     description="Delete a role",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws Exception
     */
    // </editor-fold>
    // API Call: DELETE
    // http://127.0.0.1:8000/api/v1/permission/role/delete
    // Bearer token needed */
    public function delete(RoleDeleteRequest $request): RoleResource
    {
        try {
            $role = Role::find($request->id);
            if ($role->template_role_id === null) {
                // If the role is not a CP dependant role it can be deleted but the actual
                // users need to be reassigned to the new role
                if ($role->users->count() > 0) {
                    if (!$request->has('role_id')) {
                        throw ValidationException::withMessages([
                            'role_id' => config('genericMessages.error.FIELD_REQUIRED'),
                        ]);
                    }
                    $newRole = Role::select('id', 'key')
                        ->where('id', $request->role_id)
                        ->with('users:id,email', 'company')
                        ->first();
                    foreach ($role->users as $user) {
                        RoleUserService::updateUserRoleByCompany($role->company, $newRole->id, $user);
                    }
                }
                $role->delete();
            } else {
                // If the role is a dependant CP role it will be rolled back to its original values
                // If the title already exists for that company it will add a (1) at the end of the recovered title
                $templateRole = $role->templateRole;
                $templateRole->load('permissionGroups');
                $roleTitle = Role::select('title')
                    ->whereRaw("lower(title) = '" . strtolower($templateRole->title) . "'")
                    ->where('company_id', app('asCompanyId'))
                    ->first();
                $date = now();
                $role->title = $roleTitle ? $templateRole->title . ' (1)' : $templateRole->title;
                $role->display_name = $templateRole->display_name;
                $role->description = $templateRole->description;
                $role->created_at = $date;
                $role->updated_at = $date;
                $role->save();
                PermissionRole::where('role_id', $role->id)->delete();
                foreach ($templateRole->permissionGroups as $permissionGroup) {
                    PermissionRole::updateOrCreate([
                        'role_id' => $role->id,
                        'permission_group_id' => $permissionGroup->id,
                    ]);
                }
            }
        } catch (QueryException $ex) {
            $searchForMessage = 'violates foreign key constraint';
            if (Str::contains($ex->getMessage(), $searchForMessage)) {
                throw ValidationException::withMessages([
                    config('genericMessages.error.REGISTER_HAS_DEPENDANT_VALUES'),
                ]);
            }

            throw $ex;
        }

        return new RoleResource($role);
    }

    private function appendPermissionGroups(Role $role, ?array $permissionGroups): void
    {
        PermissionRole::select('id')->where('role_id', $role->id)->delete();
        if ($permissionGroups !== null) {
            $permissionGroupIds = PermissionGroup::select('id')
                ->whereIn('key', $permissionGroups)
                ->get()
                ->pluck('id')
                ->toArray();
            foreach ($permissionGroupIds as $permissionGroupId) {
                PermissionRole::create([
                    'role_id' => $role->id,
                    'permission_group_id' => $permissionGroupId,
                ]);
            }
            $role->updated_at = now();
            $role->save();
            $role->load('permissionGroups');
        }
    }
}
