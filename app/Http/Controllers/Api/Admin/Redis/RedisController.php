<?php

namespace App\Http\Controllers\Api\Admin\Redis;

use App\Http\Controllers\Controller;
use App\Services\Redis\RedisService;
use Illuminate\Http\JsonResponse;

class RedisController extends Controller
{
    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/admin/redis/flush",
     *     operationId="flush",
     *     tags={"RedisController"},
     *     summary="Flush Redis",
     *     description="Flush Redis",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/api/v1/admin/redis/flush
    // BODY
    // needs Admin Bearer Token
    public function flush(): JsonResponse
    {
        RedisService::flushAllRedis();

        return response()->json();
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/admin/redis/flush/permissions_and_roles",
     *     operationId="flushPermissionsAndRoles",
     *     tags={"RedisController"},
     *     summary="Flush Redis Permissions and Roles",
     *     description="Flush Redis Permissions and Roles",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/api/v1/admin/redis/flush/permissions_and_roles
    // BODY
    // needs Admin Bearer Token
    public function flushPermissionsAndRoles(): JsonResponse
    {
        //RedisService::flushAllRedisPermissionsAndRoles();

        return response()->json();
    }
}
