<?php

namespace App\Http\Controllers\Api\Admin\User;

use App\Enums\Review\ReviewStatus;
use App\Enums\User\ManagementUserFilters;
use App\Enums\UserStatus;
use App\Helpers\UtilityHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\User\AdminCreateMSPUsersCsvRequest;
use App\Http\Requests\Admin\User\AdminUserChangeEmailRequest;
use App\Http\Requests\Admin\User\AdminUserChangeStatusRequest;
use App\Http\Requests\Admin\User\AdminUserCompaniesRequest;
use App\Http\Requests\Admin\User\AdminUserCountRequest;
use App\Http\Requests\Admin\User\AdminUserGetByEmailRequest;
use App\Http\Requests\Admin\User\AdminUserLoginHistoryRequest;
use App\Http\Requests\Admin\User\AdminUserRequest;
use App\Http\Requests\Admin\User\UserDeleteRequest;
use App\Http\Requests\Admin\User\UserResetTFARequest;
use App\Http\Resources\Admin\User\AdminUserCompaniesResource;
use App\Http\Resources\Admin\User\AdminUserResource;
use App\Http\Resources\Admin\User\LoginHistoryResource;
use App\Http\Resources\Notification\UserCountsNotificationResource;
use App\Jobs\CreateMSPUsersCsvJob;
use App\Models\Company\CompanyClaimer;
use App\Models\Review\Review;
use App\Models\User;
use App\Services\Analytics\AnalyticService;
use App\Services\AppConfig;
use App\Services\AuthService;
use App\Services\Notification\AdminUserNotificationService;
use App\Services\UserService;
use App\Services\UtilityService;
use App\Services\ValidationService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

class AdminUserController extends Controller
{
    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/admin/user/filters",
     *     operationId="admin/user/showAllFilters",
     *     tags={"AdminUserController"},
     *     summary="Get users list filters",
     *     description="Get users list filters",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/admin/user
    // needs Bearer Token
    public function showAllFilters(): JsonResponse
    {
        $users = User::select('users.id', 'users.company_id')->with('company:id,name')->get();
        $filters = [];
        $result = [];
        if ($users->count() > 0) {
            $booleanItems = [
                [
                    'id' => '1',
                    'name' => 'Yes',
                ],
                [
                    'id' => '0',
                    'name' => 'No',
                ],
            ];
            $filters['status'] = ManagementUserFilters::STATUS;
            $filters['status']['items'] = [
                ['id' => UserStatus::Active, 'name' => UserStatus::getKey(UserStatus::Active)],
                ['id' => UserStatus::Inactive, 'name' => UserStatus::getKey(UserStatus::Inactive)],
            ];
            $filters['is_private'] = ManagementUserFilters::IS_PRIVATE;
            $filters['is_private']['items'] = $booleanItems;

            $filters['is_profile_complete'] = ManagementUserFilters::IS_PROFILE_COMPLETE;
            $filters['is_profile_complete']['items'] = $booleanItems;

            $result = [
                'filters' => $filters,
            ];
        }

        return response()->json($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/admin/user",
     *     operationId="admin/user/showAll",
     *     tags={"AdminUserController"},
     *     summary="Get users list",
     *     description="Get users list",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/admin/user
    // OPTIONAL PARAMS
    /*
     * ?paged=PAGED&page=PAGE&order_by=COLUMN_NAME&sort=SORT_VALUE&search_word=SEARCH_WORD
     * */
    // needs Bearer Token
    public function showAll(AdminUserRequest $request): AnonymousResourceCollection
    {
        $query = $this->prepareSearchQuery();
        $validatedInput = $request->validated();

        $query->when(Arr::has($validatedInput, 'status'), function ($query) use ($request) {
            $query->where('users.status', $request->status);
        })->when(Arr::has($validatedInput, 'is_private'), function ($query) use ($request) {
            $query->where('users.is_private', $request->is_private);
        })->when(Arr::has($validatedInput, 'is_profile_complete'), function ($query) use ($request) {
            $query->where('users.is_profile_complete', $request->is_profile_complete);
        })->when(Arr::has($validatedInput, 'companies'), function ($query) use ($request) {
            $query->whereIn('users.company_id', $request->companies);
        })->when(Arr::has($validatedInput, 'search_word'), function ($query) use ($request) {
            $completeSearchWord = '%' . strtolower($request->input('search_word')) . '%';
            $completeNameLikeParameter = "%{$completeSearchWord}%";
            $query->whereRaw("lower(concat(trim(users.first_name),' ',trim(users.last_name))) LIKE ?", [$completeNameLikeParameter]);
            $query->orWhereRaw("(lower(users.email) LIKE '" . $completeSearchWord . "')");
            $searchWords = explode(' ', $completeSearchWord);
            foreach ($searchWords as $searchWord) {
                $nameLikeParameter = "%{$searchWord}%";
                $handleLikeParameter = '%' . str_replace('@', '', $searchWord) . '%';
                $query->orWhereRaw('lower(users.first_name) LIKE ?', [$nameLikeParameter]);
                $query->orWhereRaw('lower(users.last_name) LIKE ?', [$nameLikeParameter]);
                $query->orWhereRaw('lower(users.email) LIKE ?', [$nameLikeParameter]);
                $query->orWhereRaw('lower(users.handle) LIKE ?', [$handleLikeParameter]);
            }
        });
        $result = UtilityHelper::getSearchRequestQueryResults($request, $query);

        return AdminUserResource::collection($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/admin/user/{user}/details",
     *     operationId="admin/user/{user}/details",
     *     tags={"AdminUserController"},
     *     summary="Get the details of an specifict user",
     *     description="Get the details of an specifict user",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=false
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/admin/user/{user}/details
    // needs Bearer Token
    public function userDetails(User $user): AdminUserResource
    {
        $result = $this->prepareSearchQuery()->with('avatar')->where('users.id', $user->id)->first();

        return new AdminUserResource($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/admin/user/count",
     *     operationId="admin/user/showUserCount",
     *     tags={"AdminUserController"},
     *     summary="Get user count",
     *     description="Get user count",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws \Exception
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/admin/user/count
    // BODY
    /*
     * {
            "created_at": "01/25/2022",
            "end_at": "01/25/2022"
        }
     */
    // needs Bearer Token
    // get how many users are in the db by type. if created_at is sent in then filter by it.
    public function showUserCount(AdminUserCountRequest $request): JsonResponse
    {
        // select count(c.type), ct.label from channelprogram.users u
        // join channelprogram.companies c on c.id = u.company_id
        // join channelprogram.company_types ct on c.type = ct.id
        // where u.created_at >= '2022-03-29'::date and u.created_at <= '2022-03-29'::date
        // group by ct.label
        $query = User::query();
        $query->select([DB::raw('count(companies.type) as cnt'), 'company_types.label']);
        $query->join('companies', 'companies.id', '=', 'users.company_id');
        $query->join('company_types', 'companies.type', '=', 'company_types.id');
        if ($request->created_at !== null) {
            $createdDate = new \DateTime($request->created_at);
            $endDate = new \DateTime(Carbon::createFromDate($request->end_at)->endOfDay());

            $query->where('users.created_at', '>=', $createdDate)
                ->where('users.created_at', '<=', $endDate)
                ->count();
        }
        $query->groupBy('company_types.label');

        $results = $query->get();

        return Response()->json($results);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Delete(
     *     path="/api/v1/admin/user/delete",
     *     operationId="admin/user/delete",
     *     tags={"AdminUserController"},
     *     summary="Delete user",
     *     description="Deletes a record and returns no content",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *
     *         @OA\JsonContent(ref="#/components/schemas/UserDeleteRequest")
     *     ),
     *
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: DELETE
    // http://127.0.0.1:8000/api/v1/admin/user/delete
    /* BODY
    {
    	"id": "611515154484"
    }*/
    // needs Bearer Token
    public function delete(UserDeleteRequest $request): JsonResponse
    {
        try {
            UserService::deleteUser($request->id);

            return response()->json();
        } catch (\Throwable $ex) {
            throw ValidationException::withMessages([
                $ex->getMessage(),
            ]);
        }
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/admin/user/change-status",
     *     operationId="admin/user/changeStatus",
     *     tags={"AdminUserController"},
     *     summary="Change a user status",
     *     description="Changes a user status",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/admin/user/change-status
    /* BODY
    {
    	"id": "required|numeric"
        "status": "required|AllowedValues:UserStatus"
    }*/
    // needs Bearer Token
    public function changeStatus(AdminUserChangeStatusRequest $request): AdminUserResource
    {
        $loggedInUser = AuthService::getAuthUser();
        $chosenStatus = UserStatus::coerce($request->status);
        $user = User::find($request->id);
        $user->status = $chosenStatus;
        // if the status is inactive then remove them from any claimer company they might be part of
        if ($loggedInUser && $chosenStatus && $chosenStatus->key === UserStatus::getKey('inactive')) {
            $user->event_email_subscribed = false;

            CompanyClaimer::where('user_id', $request->id)->delete();
            Review::where('reviewer_user_id', $request->id)
                ->whereIn(
                    'status',
                    [
                        ReviewStatus::submitted,
                        ReviewStatus::underReview,
                    ]
                )
                ->update([
                    'status' => ReviewStatus::flagged,
                    'red_flagged' => true,
                    'red_flag_user_id' => $loggedInUser->id,
                    'red_flag_reason' => config('genericMessages.error.USER_IN_ACTIVE_REVIEW_MESSAGE'),
                    'red_flag_date' => now(),
                ]);
        }
        $user->save();

        return new AdminUserResource($user);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/admin/user/login-history",
     *     operationId="admin/user/loginHistory",
     *     tags={"AdminUserController"},
     *     summary="Get the list of IP addresses that a user has logged in",
     *     description="Get the list of IP addresses that a user has logged in",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/admin/user/login-history
    /* BODY
    {
    	"id": "required|numeric|exists:users"
    }*/
    // needs Bearer Token
    public function loginHistory(AdminUserLoginHistoryRequest $request): AnonymousResourceCollection
    {
        $resultsPerPage = $request->items_per_page ?? config('common.searchPagingLength');

        return LoginHistoryResource::collection(AnalyticService::getLoginListByUserId($request, $resultsPerPage));
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/admin/user/companies",
     *     operationId="admin/user/getCompanies",
     *     tags={"AdminUserController"},
     *     summary="Get the list of companies of the user",
     *     description="Get the list of companies of the user",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/admin/user/companies
    // needs Bearer Token
    public function getCompanies(AdminUserCompaniesRequest $request): AnonymousResourceCollection
    {
        $query = User::select('companies.id', 'companies.name', 'companies.friendly_url',
            'company_types.label', 'roles.display_name', 'roles.title', 'roles_users.created_at')
            ->join('roles_users', 'users.id', '=', 'roles_users.user_id')
            ->join('roles', 'roles.id', '=', 'roles_users.role_id')
            ->join('companies', 'companies.id', '=', 'roles.company_id')
            ->join('company_types', 'company_types.id', '=', 'companies.type')
            ->where('users.id', $request->id);
        $result = UtilityHelper::getSearchRequestQueryResults($request, $query);

        return AdminUserCompaniesResource::collection($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/admin/user/get-counts",
     *     operationId="admin/user/getUserCounts",
     *     tags={"AdminUserController"},
     *     summary="Get users counts",
     *     description="Get users counts",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/admin/user/get-counts
    // needs Bearer Token
    public function getUserCounts(): UserCountsNotificationResource
    {
        $results = AdminUserNotificationService::getLoggedInUsers();
        $userCountNotificationResource = new \stdClass();
        $userCountNotificationResource->logged_user_count = $results->count();
        $pingFrequency = AppConfig::loadAppConfigByKey('STILL_ACTIVE_PING_FREQUENCY_IN_MINUTES', 15)
            ->value;
        $activeUsers = $results->where('updated_at', '>', now()->subMinutes($pingFrequency));
        $userCountNotificationResource->active_user_count = $activeUsers->count();

        return new UserCountsNotificationResource($userCountNotificationResource);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/admin/user/create-msp-with-csv",
     *     operationId="admin/user/createMSPUsersWithCSV",
     *     tags={"AdminUserController"},
     *     summary="Create MSP users with a CSV",
     *     description="Creates an invitation between companies for conecting in a PRM to all csv emails",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: Post
    // http://127.0.0.1:8000/api/v1/admin/user/create-msp-with-csv
    // needs Bearer Token
    // BODY
    /*
     * {
            "send_email": required|boolean
            "file": "***CSV FILE*****",
        }
     */
    public function createMSPUsersWithCSV(AdminCreateMSPUsersCsvRequest $request): ?JsonResponse
    {
        $csvData = UtilityService::csvToCollection(
            $request->file('file'),
            array_keys(config('common.msp_users.register.csv_headers'))
        );
        $emails = $csvData->pluck(config('common.msp_users.register.csv_headers.emailAddress'))->filter()->unique()->toArray();
        $invalidEmails = ValidationService::blackListDomainListValidation($emails, false);
        $errors = array_map(function ($email) {
            return [
                'code' => 'AUC002',
                'reason' => 'Domain not accepted',
                'value' => $email,
            ];
        }, $invalidEmails);
        // Iterate through rows, keeping only the first occurrence of each value in the specified column
        $uniqueValues = [];
        $processing = [];
        $csvData = $csvData->map(function ($row) use (&$uniqueValues, &$errors, &$processing, $invalidEmails) {
            $value = $row[config('common.msp_users.register.csv_headers.emailAddress')];
            if (in_array($value, $invalidEmails)) {
                return null;
            }
            if (filter_var($value, FILTER_VALIDATE_EMAIL)) {
                if (!in_array($value, $uniqueValues, true)) {
                    $uniqueValues = array_merge($uniqueValues, [$value]);
                    $processing[] = $value;

                    return $row;
                }
                $errors[] = ['code' => 'AUC001', 'reason' => 'Duplicated email', 'value' => $value];
            } else {
                $errors[] = ['code' => 'AUC002', 'reason' => 'Invalid email', 'value' => $value];
            }

            return null;
        })->filter();

        dispatch(new CreateMSPUsersCsvJob($csvData, $request->send_email, $errors));

        return response()->json([
            'message' => 'MSP Create Users csv file is being processed. Check Server Logs for more details.',
            'processing' => $processing,
            'errors' => $errors,
        ]);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/admin/user/reset-two-factor-authentication",
     *     operationId="admin/user/resetTwoFactorAuthentication",
     *     tags={"AdminUserController"},
     *     summary="Reset Two Factor Authentication",
     *     description="Reset Two Factor Authentication",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: Put
    // http://127.0.0.1:8000/api/v1/admin/user/reset-two-factor-authentication
    // needs Bearer Token
    // BODY
    /*
     * {
            "id": required|user_id
        }
     */
    public function resetTwoFactorAuthentication(UserResetTFARequest $request): ?JsonResponse
    {
        try {
            UserService::resetTFA($request->id);

            return response()->json();
        } catch (\Throwable $ex) {
            throw ValidationException::withMessages([
                $ex->getMessage(),
            ]);
        }
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/admin/user/getByEmail",
     *     operationId="admin/user/getByEmail",
     *     tags={"AdminUserController"},
     *     summary="Get user by email",
     *     description="Get userby email",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/admin/user/getbyemail
    // PARAMS
    /*
     * ?email=
     * */
    // needs Bearer Token
    public function getUserByEmail(AdminUserGetByEmailRequest $request): AnonymousResourceCollection
    {
        $user = User::select('id', 'company_id')->where('email', $request->email)->get();

        return AdminUserResource::collection($user);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/admin/user/change-Email",
     *     operationId="admin/user/change-Email",
     *     tags={"AdminUserController"},
     *     summary="Change a user email",
     *     description="Changes a user email",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/admin/user/change-email
    /* BODY
    {
    	"id": "required|numeric"
        "email": "required|email:dns"
    }*/
    // needs Bearer Token
    public function changeEmail(AdminUserChangeEmailRequest $request): AdminUserResource
    {
        $user = User::find($request->id);
        $user->email = $request->email;

        $user->save();

        return new AdminUserResource($user);
    }

    private function prepareSearchQuery(): Builder
    {
        $query = User::select('users.id', 'users.first_name', 'users.last_name', 'users.email', 'users.created_at',
            'users.email_verified_at', 'users.hubspot_contact_id', 'users.handle', 'users.friendly_url',
            'users.two_factor_secret', 'users.event_email_subscribed', 'users.user_profile_types_id',
            'users.is_private', 'users.is_profile_complete', 'users.created_at', 'users.last_logged_in_at',
            'users.status', 'user_profile_types.value as user_profile_type_value',
            'users.verification_code_verified', 'users.verification_mode', 'companies.name as company_name',
            'companies.id as company_id', 'company_types.label as company_type_label',
            'companies.friendly_url as company_friendly_url')
            ->leftjoin('user_profile_types', 'users.user_profile_types_id', '=', 'user_profile_types.id')
            ->leftjoin('companies', 'users.company_id', '=', 'companies.id')
            ->leftjoin('company_types', 'companies.type', '=', 'company_types.id');

        return $query;
    }
}
