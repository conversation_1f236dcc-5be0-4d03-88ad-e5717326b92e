<?php

namespace App\Http\Controllers\Api\Admin\UserContent;

use App\Enums\Lookup\LookupOptionsEnum;
use App\Enums\StatusScope\StatusScopeEnum;
use App\Enums\StatusScope\StatusScopeFiltersEnum;
use App\Events\MediaUpdatedEvent;
use App\Helpers\MediaHelper;
use App\Helpers\UtilityHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\UserContent\AdminUserContentApproveCompanyRequest;
use App\Http\Requests\Admin\UserContent\AdminUserContentApproveProductRequest;
use App\Http\Requests\Admin\UserContent\AdminUserContentCompaniesRequest;
use App\Http\Requests\Admin\UserContent\AdminUserContentFiltersRequest;
use App\Http\Requests\Admin\UserContent\AdminUserContentProductsRequest;
use App\Http\Requests\Admin\UserContent\AdminUserContentRejectEntityRequest;
use App\Http\Requests\Admin\UserContent\AdminUserContentRejectionReasonsRequest;
use App\Http\Requests\Profiles\UploadProfileImageRequest;
use App\Http\Resources\Company\CompanyResource;
use App\Http\Resources\LookupOptionResource;
use App\Http\Resources\Product\ProductSimpleResource;
use App\Models\Company\Company;
use App\Models\Company\CompanyContactInformation;
use App\Models\Lookup\LookupOption;
use App\Models\Product;
use App\Models\StatusScope\StatusScope;
use App\Services\AuthService;
use App\Services\MediaService;
use App\Services\UserContent\UserContentService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;

class AdminUserContentController extends Controller
{
    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/admin/user-content/filters",
     *     operationId="adminUserContent/showFilters",
     *     tags={"AdminUserContentController"},
     *     summary="List all available filters for the entity",
     *     description="List all available filters for the entity",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *
     *         @OA\JsonContent(ref="#/components/schemas/CompanyResource")
     *     ),
     *
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/admin/user-content/filters
    // Bearer token needed
    public function showFilters(AdminUserContentFiltersRequest $request): JsonResponse
    {
        $filters = [
            "status" => StatusScopeFiltersEnum::STATUS,
        ];

        // Getting statuses
        $filters["status"]["items"] = [];
        $statuses = StatusScopeEnum::getKeys();
        foreach ($statuses as $status) {
            $filters["status"]["items"][StatusScopeEnum::getValue($status)] = ucwords(strtolower($status));
        }

        // Getting requester names
        if ($request->has("entity")) {
            $entityClass = match ($request->entity) {
                "company" => Company::class,
                "product" => Product::class,
                default => null
            };
            if (!empty($entityClass)) {
                $statuses = StatusScope::select(["model_type", "status", "author_id", "owner_id"])
                    ->where("model_type", $entityClass)
                    ->whereIn("status", [StatusScopeEnum::pending, StatusScopeEnum::declined])
                    ->groupBy(["model_type", "status", "author_id", "owner_id"])
                    ->with([
                        "author",
                        "owner",
                    ])
                    ->get();
                $filters["created_by"] = StatusScopeFiltersEnum::CREATED_BY;
                $filters["created_by"]["items"] = [];
                foreach ($statuses as $status) {
                    $filters["created_by"]["items"]["{$status->author_id}_{$status->owner_id}"] =
                        "{$status->author->first_name} {$status->author->last_name} " .
                        "({$status->owner->name})";
                }
            }
        }

        return response()->json([
            'filters' => $filters,
        ]);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/admin/user-content/count-pending",
     *     operationId="adminUserContent/showPendingContentCount",
     *     tags={"AdminUserContentController"},
     *     summary="Get the number of pending entities created by users",
     *     description="Get the number of pending entities created by users",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *
     *         @OA\JsonContent(ref="#/components/schemas/CompanyResource")
     *     ),
     *
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/admin/user-content/count-pending
    // Bearer token needed
    public function showPendingContentCount(): JsonResponse
    {
        $totals = UserContentService::adminGetAllPendingContentCount();

        return response()->json($totals);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/admin/user-content/reject-options",
     *     operationId="adminUserContent/showAllRejectOptions",
     *     tags={"AdminUserContentController"},
     *     summary="List all lookup option values to reject an entity created by users",
     *     description="List all lookup option values to reject an entity created by users",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *
     *         @OA\JsonContent(ref="#/components/schemas/CompanyResource")
     *     ),
     *
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/admin/user-content/reject-options
    // Bearer token needed
    public function showAllRejectOptions(AdminUserContentRejectionReasonsRequest $request): AnonymousResourceCollection
    {
        $lookupOptionKey = match ($request->entity) {
            "company" => LookupOptionsEnum::STATUS_SCOPE_COMPANY_REJECTING_REASONS,
            "product" => LookupOptionsEnum::STATUS_SCOPE_PRODUCT_REJECTING_REASONS,
            default => throw ValidationException::withMessages([
                config("genericMessages.error.STATUS_SCOPE_INVALID_ENTITY"),
            ])
        };
        $lookupOption = LookupOption::where("name", $lookupOptionKey)
            ->with([
                "lookupOptionValues" => function ($q) {
                    $q->whereNotIn("value_key", ["PRODUCT_COMPANY_IS_DECLINED"]);
                },
            ])
            ->first();

        return LookupOptionResource::collection($lookupOption->lookupOptionValues);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/admin/user-content/reject",
     *     operationId="adminUserContent/rejectEntities",
     *     tags={"AdminUserContentController"},
     *     summary="Reject an entity created by users",
     *     description="Reject an entity created by users",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *
     *         @OA\JsonContent(ref="#/components/schemas/CompanyResource")
     *     ),
     *
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/admin/user-content/reject
    // Bearer token needed
    public function rejectEntities(AdminUserContentRejectEntityRequest $request): JsonResponse
    {
        $loggedUser = AuthService::getAuthUser();
        $response = UserContentService::rejectEntities(
            $loggedUser,
            $request->entity,
            $request->entity_ids,
            $request->status_reason_key,
            $request->reason_description ?? null,
            $request->duplicate_id ?? null
        );

        return response()->json([
            "entity" => $request->entity,
            "entity_ids" => $response->pluck("id")->transform(function ($id) {
                return '' . $id;
            })->toArray(),
        ]);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/admin/user-content/companies/show-all",
     *     operationId="adminUserContent/showAllCompanies",
     *     tags={"AdminUserContentController"},
     *     summary="Get companies created by users",
     *     description="Get companies created by users",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *
     *         @OA\JsonContent(ref="#/components/schemas/CompanyResource")
     *     ),
     *
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/admin/user-content/companies/show-all
    // Bearer token needed
    public function showAllCompanies(AdminUserContentCompaniesRequest $request): AnonymousResourceCollection
    {
        $searchWord = $request->has("search_word") ? $request->search_word : null;
        $createdBy = $request->has("created_by") ? $request->created_by : null;
        $status = $request->has("status") ? $request->status : null;
        $query = UserContentService::getSelectEntitiesQuery(
            Company::class,
            $searchWord,
            "companies.name",
            $status,
            $createdBy
        )->with([
            "enumType", "avatar",
        ]);
        $result = UtilityHelper::getSearchRequestQueryResults($request, $query);

        return CompanyResource::collection($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/admin/user-content/products/show-all",
     *     operationId="adminUserContent/showAllProducts",
     *     tags={"AdminUserContentController"},
     *     summary="Get products created by users",
     *     description="Get products created by users",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/admin/user-content/products/show-all
    // Bearer token needed
    public function showAllProducts(AdminUserContentProductsRequest $request): AnonymousResourceCollection
    {
        $searchWord = $request->has("search_word") ? $request->search_word : null;
        $createdBy = $request->has("created_by") ? $request->created_by : null;
        $status = $request->has("status") ? $request->status : null;
        $query = UserContentService::getSelectEntitiesQuery(
            Product::class,
            $searchWord,
            "products.name",
            $status,
            $createdBy,
            function ($q, $tableName) {
                $q->leftJoin('companies as vendor', "{$tableName}.company_id", '=', 'vendor.id');
            }
        )->with([
            "categories.parentCategory",
            "company" => function ($q) {
                $q->withoutScope();
            },
            "company.statusRelation",
            "company.enumType",
        ]);
        $result = UtilityHelper::getSearchRequestQueryResults($request, $query);

        return ProductSimpleResource::collection($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/admin/user-content/companies/{company_id}/approve",
     *     operationId="adminUserContent/approveCompany",
     *     tags={"AdminUserContentController"},
     *     summary="Approves a company created by users",
     *     description="Approves a company created by users",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *
     *         @OA\JsonContent(ref="#/components/schemas/CompanyResource")
     *     ),
     *
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/admin/user-content/companies/{company_id}/approve
    // Bearer token needed
    public function approveCompany(AdminUserContentApproveCompanyRequest $request, string $company_id): CompanyResource
    {
        $loggedUser = AuthService::getAuthUser();
        DB::beginTransaction();

        try {
            $company = Company::allStatuses()
                ->where("id", $company_id)
                ->with(["statusRelation.owner"])
                ->first();
            // Updating company's data
            $company->update([
                "name" => $request->name,
                "profile_company_website_url" => $request->profile_company_website_url,
                "description" => $request->description,
            ]);
            if ($request->has("linkedin") && !empty($request->linkedin)) {
                $contactInfo = CompanyContactInformation::firstOrCreate(["company_id" => $company->id]);
                $additionalWebsites = array_merge(
                    $contactInfo->additional_websites ?? [],
                    ["linkedin" => $request->linkedin],
                );
                $contactInfo->update([
                    "additional_websites" => $additionalWebsites,
                ]);
            }
            $company->setStatus(
                StatusScopeEnum::approved,
                $company->statusRelation->owner,
                $loggedUser
            );
            DB::commit();

            $company->refresh()->load([
                "statusRelation",
                "statusRelation.owner",
                "statusRelation.author",
            ]);

            return new CompanyResource($company);
        } catch (Exception $error) {
            DB::rollBack();
            Log::warning(__CLASS__ . "::" . __FUNCTION__ . "::Error approving new company: " . $error->getMessage());

            throw $error;
        }
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/admin/user-content/companies/{company_id}/avatar/store",
     *     operationId="adminUserContent/updateCompanyAvatar",
     *     tags={"AdminUserContentController"},
     *     summary="Store a new avatar for a company",
     *     description="Store a new avatar for a company",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/admin/user-content/companies/{company_id}/avatar/store
    // Bearer Token needed
    public function updateCompanyAvatar(UploadProfileImageRequest $req, string $company_id): JsonResponse
    {
        try {
            $company = Company::allStatuses()
                ->where("id", $company_id)
                ->with(["statusRelation.owner"])
                ->first();

            $collectionName = 'profile-' . strtolower($req->image_type);
            $customProperties = MediaHelper::setCustomProperties($req);
            $media = $company->addMedia($req->image)
                ->addCustomHeaders(['ACL' => 'public-read'])
                ->withCustomProperties($customProperties)
                ->toMediaCollection($collectionName, 'backBlazePublic');
            $responseData = [
                'src' => Storage::temporaryUrlForDisk($media->getPath(), MediaService::getExpirationTime(), $media->disk),
                'alt' => $media->getCustomProperty('alt'),
                'title' => $media->getCustomProperty('title'),
                'description' => $media->getCustomProperty('description'),
                'id' => '' . $media->id,
                'type' => $media->getCustomProperty('image_type'),
            ];
            MediaUpdatedEvent::dispatch($media);

            return response()->json($responseData, 201);
        } catch (Exception $error) {
            DB::rollBack();
            Log::warning(__CLASS__ . "::" . __FUNCTION__ . "::Error updating company avatar: " . $error->getMessage());

            throw $error;
        }
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/admin/user-content/products/{product_id}/approve",
     *     operationId="adminUserContent/approveProduct",
     *     tags={"AdminUserContentController"},
     *     summary="Approves a product created by users",
     *     description="Approves a product created by users",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/admin/user-content/products/{product_id}/approve
    // Bearer token needed
    public function approveProduct(AdminUserContentApproveProductRequest $request, string $product_id): ProductSimpleResource
    {
        $loggedUser = AuthService::getAuthUser();
        DB::beginTransaction();

        try {
            $product = Product::allStatuses()
                ->where("id", $product_id)
                ->with(["statusRelation.owner"])
                ->first();
            // Updating product's data
            $product->update([
                "name" => $request->name,
                "url" => $request->url,
                "description" => $request->description,
            ]);
            $product->categories()->sync([$request->category_id]);
            // Updating product status
            $product->setStatus(
                StatusScopeEnum::approved,
                $product->statusRelation->owner,
                $loggedUser
            );
            DB::commit();

            $product->refresh()->load([
                "statusRelation",
                "statusRelation.owner",
                "statusRelation.author",
            ]);

            return new ProductSimpleResource($product);
        } catch (Exception $error) {
            DB::rollBack();
            Log::warning(__CLASS__ . "::" . __FUNCTION__ . "::Error approving new product: " . $error->getMessage());

            throw $error;
        }
    }
}
