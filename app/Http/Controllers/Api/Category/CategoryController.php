<?php

namespace App\Http\Controllers\Api\Category;

use App\Enums\Review\ReviewStatus;
use App\Helpers\PaginateHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Category\CategoryShowAllRequest;
use App\Http\Requests\Category\CategoryShowProductsRequest;
use App\Http\Requests\Category\PopularCategoriesRequest;
use App\Http\Resources\Category\CategoryProductsPageResource;
use App\Http\Resources\Category\CategoryResource;
use App\Http\Resources\Category\CategoryVendorsPageResource;
use App\Http\Resources\Category\PopularCategoryResource;
use App\Models\Category\Category;
use App\Models\Company\Company;
use App\Models\Product;
use App\Services\CategoryService;
use App\Services\Company\CompanyService;
use App\Services\ImageService;
use App\Services\ProductService;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

class CategoryController extends Controller
{
    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/categories",
     *     operationId="categories/showAll",
     *     tags={"CategoryController"},
     *     summary="Get all categories",
     *     description="Get all categories",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/categories
    // parent_id=PARENT_ID
    // load_my_stack_categories=BOOLEAN
    // is_hidden=BOOLEAN
    // Bearer Token NOT needed
    public function showAll(CategoryShowAllRequest $request): AnonymousResourceCollection
    {
        $defaultCategories = !$request->has('default_categories') || (bool)$request->default_categories;

        $company = Company::with('enumType:id,value')
            ->select('id', 'type')
            ->find($request->as_company_id);

        $query = CategoryService::getMainCategoryQuery($request->is_hidden ?? false)
            ->with('companyTypes:company_types.id,company_types.value');

        CategoryService::applyCategoryFilters($query, $request, $defaultCategories);

        CategoryService::applyCompanyTypeCategoriesFilter(
            $query,
            $company,
            $request->company_type_categories ?? 'include'
        );

        if ($request->has('load_my_stack_categories')) {
            CategoryService::applyMyStackCategoriesFilter($query, $defaultCategories);
        }

        return CategoryResource::collection($query->get());
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/categories/show-parents",
     *     operationId="categories/showParents",
     *     tags={"CategoryController"},
     *     summary="Get all visible parent categories",
     *     description="Get all visible parent categories",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/categories/show-parents
    // Bearer Token NOT needed
    public function showParents(): AnonymousResourceCollection
    {
        $categoriesQuery = CategoryService::getMainCategoryQuery();
        $categoriesQuery->whereNull('parent_id');

        return CategoryResource::collection($categoriesQuery->get());
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/categories/show-featured",
     *     operationId="categories/showFeatured",
     *     tags={"CategoryController"},
     *     summary="Get all visible featured categories",
     *     description="Get all visible featured categories",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/categories/show-featured
    // Bearer Token NOT needed
    public function showFeatured(): AnonymousResourceCollection
    {
        $categoriesQuery = CategoryService::getMainCategoryQuery();
        $categoriesQuery->where('featured', true);

        return CategoryResource::collection($categoriesQuery->get());
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/categories/{category}/vendors",
     *     operationId="categories/showVendors",
     *     tags={"CategoryController"},
     *     summary="Get all the vendors for a category and the listo of the sub-categories",
     *     description="Get all the vendors for a category and the listo of the sub-categories",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/categories/{category}/vendors
    // Bearer Token NOT needed
    public function showVendors(Category $category)
    {
        $category->load(['subCategories' => function ($query) {
            $query->selectRaw('*, (select count(*) as number_of_products from product_categories where category_id = categories.id)');
            $query->orderBy(DB::raw('lower(name)'));
        }]);

        $companiesFromProductCategoriesSql = 'companies.id in (Select distinct company_id from products where id in (
            Select product_id from product_categories where category_id in (
                Select id from categories where id=? or parent_id=?
            )
        ))';

        $companies = Company::select(['companies.*', 'company_profile_types.order'])
            ->with([
                'companyProfileType',
                'products' => function ($query) {
                    $query->select([
                        'products.*', ProductService::createRatingFieldSelect(),
                    ])->withCount(['reviews as total_reviews']);
                },
            ])
            ->withCount(['productReviews' => function ($query) {
                $query->where('status', '=', ReviewStatus::approved)
                    ->whereNotNull('reviewer_user_id');
            }])
            ->join('company_profile_types', 'companies.company_profile_types_id', '=', 'company_profile_types.id')
            ->leftJoin('company_categories', 'companies.id', '=', 'company_categories.company_id')
            ->where('company_categories.category_id', $category->id)
            ->orWhereRaw($companiesFromProductCategoriesSql, [$category->id, $category->id])
            ->orderBy('company_profile_types.order')
            ->orderBy('companies.name')
            ->distinct()
            ->get();

        $companies = PaginateHelper::paginate(request(), $companies);
        $pageResults = $companies->getCollection();
        $pageResults = CompanyService::appendProductsCategoriesToCollection($pageResults);
        $pageResults = ImageService::appendCompanyAvatars($pageResults);
        $companies->setCollection($pageResults);
        $results = collect();
        $results->put('category', $category);
        $results->put('companies', $companies);
        $results->put('subCategories', $category->subCategories);

        return new CategoryVendorsPageResource($results);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/categories/{category}/products",
     *     operationId="categories/showProducts",
     *     tags={"CategoryController"},
     *     summary="Get all products for a subcategory",
     *     description="Get all products for a subcategory",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/categories/{category}/products
    // ?subcategory_id={category}
    // &order_by={ORDER_BY}
    // Bearer Token NOT needed
    public function showProducts(CategoryShowProductsRequest $request, Category $category)
    {
        $subCategory = Category::findOrFail($request->subcategory_id);

        if ($category->id !== $subCategory->parent_id) {
            throw ValidationException::withMessages([
                config('genericMessages.error.SUBCATEGORY_MUST_BE_RELATED_TO_CATEGORY'),
            ]);
        }

        $productsQuery = Product::with(
            'company',
            'reviews',
            'reviews.answers',
            'reviews.answers.question',
            'reviews.answers.option'
        )
            ->select(['products.*', ProductService::createRatingFieldSelect()])
            ->withCount(['reviews as total_reviews'])
            ->join('product_categories', 'products.id', '=', 'product_categories.product_id')
            ->join('companies', 'products.company_id', '=', 'companies.id')
            ->join('company_profile_types', 'company_profile_types_id', '=', 'company_profile_types.id')
            ->where('product_categories.category_id', $request->subcategory_id);

        ProductService::setOrdering($productsQuery, $request->order_by);
        $products = $productsQuery->paginate(config('common.searchPagingLength'));
        $pageResults = $products->getCollection();
        $parents = $products->pluck('company');
        $parents = ImageService::appendCompanyAvatars($parents);
        $pageResults = ProductService::appendImages($pageResults);
        $pageResults = ProductService::appendVideos($pageResults);
        $products->setCollection($pageResults);
        $results = collect();
        $results->put('products', $products);
        $results->put('parents', $parents);

        return new CategoryProductsPageResource($results);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/categories/with-subcategories",
     *     operationId="categories/withSubcategories",
     *     tags={"CategoryController"},
     *     summary="Get all categories with the corresponding children",
     *     description="Get all categories with the corresponding children",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/categories/with-subcategories
    // Bearer Token NOT needed
    public function withSubcategories(): AnonymousResourceCollection
    {
        $categories = Category::whereNull('parent_id')
            ->where('is_hidden', false)
            ->with('subCategories')
            ->orderBy('name')
            ->get();

        return CategoryResource::collection($categories);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/categories/popular",
     *     operationId="categories/popular",
     *     tags={"CategoryController"},
     *     summary="Get popular categories with the corresponding children",
     *     description="Get popular categories with the corresponding children",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/categories/popular
    // Bearer Token NOT needed
    public function popular(PopularCategoriesRequest $request): AnonymousResourceCollection
    {
        $endDate = now();
        $startDate = now()->subWeek();
        $categories = CategoryService::getPopularCategories(
            collect(),
            $startDate,
            $endDate,
            $request->num_of_categories ?? config('common.popularCategories')
        );

        return PopularCategoryResource::collection($categories);
    }
}
