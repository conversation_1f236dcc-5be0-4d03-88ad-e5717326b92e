<?php

namespace App\Http\Controllers\Api\Company;

use App\Enums\AffiliateBrands\AffiliateFilters;
use App\Enums\AffiliateBrands\AffiliateFiltersSort;
use App\Enums\Company\CompanyProfileTypes;
use App\Enums\Company\CompanyType as CompanyTypeEnum;
use App\Helpers\CSVHelper;
use App\Helpers\UtilityHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Company\Affiliate\CompanyAffiliateDeleteRequest;
use App\Http\Requests\Company\Affiliate\CompanyAffiliateFiltersRequest;
use App\Http\Requests\Company\Affiliate\CompanyAffiliateRequest;
use App\Http\Requests\Company\Affiliate\CompanyAffiliateResendBulkInviteRequest;
use App\Http\Requests\Company\Affiliate\CompanyAffiliatesStoreImportFromCSVRequest;
use App\Http\Requests\Company\Affiliate\CompanyAffiliateUpdateRequest;
use App\Http\Requests\Company\Client\CompanyClientShowAllRequest;
use App\Http\Requests\Company\Invite\CompanyShowAllAffiliateInviteRequest;
use App\Http\Resources\AffiliateBrand\AffiliateBrandResource;
use App\Http\Resources\Company\Affiliate\CompanyAffiliateResource;
use App\Http\Resources\Company\Affiliate\CompanyBrandStackAdoptionDetailResource;
use App\Http\Resources\Company\Affiliate\CompanyBrandStackAdoptionResource;
use App\Http\Resources\Company\CompanyResource;
use App\Http\Resources\Company\CompanyTypeResource;
use App\Http\Resources\Company\Invite\CompanyInviteResource;
use App\Jobs\Company\SendInviteAffiliateEmails;
use App\Models\AffiliateBrand\AffiliateBrand;
use App\Models\Category\Category;
use App\Models\Company\Company;
use App\Models\Company\CompanyInvite;
use App\Models\Company\CompanyType;
use App\Services\AppConfig;
use App\Services\AuthService;
use App\Services\Company\CompanyService;
use App\Services\ImageService;
use App\Services\ValidationService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use stdClass;

class CompanyAffiliateController extends Controller
{
    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{friendly_url}/affiliates/",
     *     operationId="company/affiliates/showAll",
     *     tags={"CompanyAffiliateController"},
     *     summary="Get company affiliates",
     *     description="Returns the company affiliates",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/affiliates
    // Bearer token needed
    public function showAll(CompanyAffiliateRequest $request, Company $company): AnonymousResourceCollection
    {
        $result = $this->loadAffiliates($company, $request);
        if ($result instanceof LengthAwarePaginator) {
            $pageResults = $result->getCollection();
        } else {
            $pageResults = $result;
        }
        ImageService::appendCompanyAvatars($pageResults);
        if ($request->has('calculate_navistack_progress') && $request->calculate_navistack_progress) {
            CompanyService::calculateBrandStackAdoptionWithProgress($company->recommendedMyStack, $pageResults);
        }
        if ($result instanceof LengthAwarePaginator) {
            $result->setCollection($pageResults);
        } else {
            $result = $pageResults;
        }

        return CompanyAffiliateResource::collection($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Delete(
     *     path="/api/v1/company/{friendly_url}/affiliates/delete",
     *     operationId="company/affiliates/delete",
     *     tags={"CompanyAffiliateController"},
     *     summary="Delete relation between the affiliate and the corporate",
     *     description="Delete relation between the affiliate and the corporate",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/affiliates/delete
    // Bearer token needed
    public function deleteAffiliate(CompanyAffiliateDeleteRequest $request, Company $company): JsonResponse
    {
        /*CompanyService::validateCompanyIsMSP($company);
        $affiliateCompany = Company::find($request->affiliate_id);
        // first remove the invites from the company invite table
        CompanyInvite::where('child_company_id', $request->affiliate_id)
            ->where('parent_company_id', $affiliateCompany->parent_id)
            ->delete();
        // now remove the affiliate relation for the requested company
        $affiliateCompany->parent_id = null;
        $affiliateCompany->affiliate_brand_id = null;
        $affiliateCompany->show_affiliate_popup = true;
        $affiliateCompany->save();*/
        // TODO garrett needs to define how is the delete flow going to work
        // conversation done with Drew, Alvaro and Garret the 12/12/2024

        return response()->json();
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{friendly_url}/affiliates/filters",
     *     operationId="company/affiliates/showAllFilters",
     *     tags={"CompanyAffiliateController"},
     *     summary="Get filters for the affiliates list",
     *     description="Get filters for the affiliates list",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/affiliates/filters
    // PARAMS
    // ?is_sort=required|boolean
    // needs Bearer Token
    public function showAllFilters(CompanyAffiliateFiltersRequest $request, Company $company): JsonResponse
    {
        CompanyService::validateCompanyIsMSP($company);
        $filters = [];
        $filters['status'] = AffiliateFilters::subscription_status;
        $filters['status']['items'] = [
            'enabled' => 'Enabled',
            'disabled' => 'Disabled',
        ];
        if ($company->affiliates->count() > 0) {
            $items = collect([]);
            $company->affiliates->map(function ($affiliate) use (&$items) {
                if ($affiliate->address) {
                    $items->push(['id' => $affiliate->address, 'name' => $affiliate->address]);
                }
            });
            if ($items->count() > 0) {
                $filters['locations'] = AffiliateFilters::locations;
                $filters['locations']['items'] = $items->unique('id')->toArray();
            }
        }

        if ($request->has('is_sort') && $request->is_sort) {
            return response()->json([
                'filters' => $filters,
                'sorts' => AffiliateFiltersSort::asArray(),
            ]);
        }

        return response()->json([
            'filters' => $filters,
        ]);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{friendly_url}/affiliates/calculate-brand-stack-adoption",
     *     operationId="company/affiliates/calculateBrandStackAdoption",
     *     tags={"CompanyAffiliateController"},
     *     summary="Get company Brand Stack Adoption Percentage",
     *     description="Returns the company Brand Stack Adoption Percentage",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/affiliates/calculate-brand-stack-adoption
    // Bearer token needed
    public function calculateBrandStackAdoption(Company $company): CompanyBrandStackAdoptionResource
    {
        CompanyService::validateCompanyIsMSP($company);
        $company->load('recommendedMyStack', 'affiliates.myStack', 'affiliates.contractsWithoutAddons');
        $affiliates = CompanyService::calculateBrandStackAdoption($company->recommendedMyStack, $company->affiliates);
        $navistackProgress = $affiliates->pluck('navistack_progress');
        $companyBrandStackAdoption = new stdClass();
        $companyBrandStackAdoption->company_id = $company->id;
        $companyBrandStackAdoption->brand_stack_adoption = 0;
        if ($navistackProgress) {
            $companyBrandStackAdoption->brand_stack_adoption = $navistackProgress->count() > 0
                ? round(array_sum($navistackProgress->toArray()) / $navistackProgress->count())
                : 0;
        }
        $companyBrandStackAdoption->registered_affiliates = $company->affiliates->count();
        $companyBrandStackAdoption->active_vendor_contracts = 0;
        $company->affiliates->map(function ($affiliate) use ($companyBrandStackAdoption) {
            $companyBrandStackAdoption->active_vendor_contracts += $affiliate->addonsContracts->count();
        });

        return new CompanyBrandStackAdoptionResource($companyBrandStackAdoption);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{friendly_url}/affiliates/brand-stack-adoption-detail",
     *     operationId="company/affiliates/brandStackAdoptionDetail",
     *     tags={"CompanyAffiliateController"},
     *     summary="Get company Brand Stack Adoption Detail",
     *     description="Returns the company Brand Stack Adoption Detail",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/affiliates/brand-stack-adoption-detail
    // Bearer token needed
    public function brandStackAdoptionDetail(Company $company): AnonymousResourceCollection
    {
        CompanyService::validateCompanyIsMSP($company);
        $company->load('recommendedMyStack', 'affiliates.myStack');
        $categories = Category::whereIn('id', $company->recommendedMyStack->pluck('pivot')->pluck('category_id'))
            ->get()->groupBy('parent_id');
        $affiliates = $company->affiliates->sortBy('name');
        ImageService::appendCompanyAvatarsURLs($affiliates);
        $affiliatePivots = $company->affiliates->map(function ($affiliate) {
            return $affiliate->myStack->pluck('pivot');
        })->flatten();
        $result = $this->makeResult($affiliates, $categories, $affiliatePivots);
        $this->calculateMainAffiliatesUsingIt($result);

        return CompanyBrandStackAdoptionDetailResource::collection($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/{friendly_url}/affiliates/update-brand",
     *     operationId="company/affiliates/createOrUpdateBrand",
     *     tags={"CompanyAffiliateController"},
     *     summary="Update or Create new affiliate brand",
     *     description="Update or Create new affiliate brand",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/affiliates/update-brand
    // BODY
    /*
    	{
            "affiliate_brand_id":"sometimes|required|numeric|exists:affiliate_brands,id",
            "affiliate_brand_name":"sometimes|required|string|min:2|max:255|profanity",
            "is_corporate_location":"required|boolean",
            "name":"sometimes|required|string|max:191|unique:companies,name",
            "profile_vendor_handle" : "sometimes|required|string|no_white_spaces|min:5|max:50|unique",
            "affiliate_id" : "nullable|string|max:255",
            "address" : "nullable|string|max:255",
        }
    needs Admin Bearer Token*/
    public function createOrUpdateBrand(CompanyAffiliateUpdateRequest $request, Company $company)
    {
        if ($request->has('name')) {
            if (strtolower($request->get('name')) !== strtolower($company->name)) {
                CompanyService::updateCompanyFriendlyUrl($company, $request);
            }
        }
        $loggedUser = AuthService::getAuthUser();
        $updatedData = $request->validated();
        $affiliateBrand = null;
        if ($request->has('affiliate_brand_id')) {
            $affiliateBrand = AffiliateBrand::findOrFail($request->affiliate_brand_id);
        } else if ($request->has('affiliate_brand_name')) {
            $affiliateBrand = AffiliateBrand::where('name', $request->affiliate_brand_name)->first();
            if (empty($affiliateBrand)) {
                $affiliateBrand = AffiliateBrand::create([
                    'name' => $request->affiliate_brand_name,
                    'is_active' => true,
                    'author_id' => $loggedUser?->id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
        if ($affiliateBrand) {
            $updatedData['affiliate_brand_id'] = $affiliateBrand->id;
        }
        if ($request->has('affiliate_brand_id') || $request->has('affiliate_brand_name')) {
            if ($request->is_corporate_location && $affiliateBrand->main_company_id === null) {
                $affiliateBrand->main_company_id = $company->id;
                $affiliateBrand->save();
                $companyType = CompanyType::firstWhere('value', \App\Enums\Company\CompanyType::FRANCHISE_CORPORATE_MSP);
                $updatedData['type'] = $companyType->id;
            } else {
                $updatedData['parent_id'] = $affiliateBrand->main_company_id;
            }
        }
        $updatedData['show_affiliate_popup'] = false;
        $company->update($updatedData);
        $company->load('enumType');
        $result['company'] = new CompanyResource($company);
        $result['company_type'] = new CompanyTypeResource($company->enumType);
        $result['affiliate_brand'] = new AffiliateBrandResource($affiliateBrand);

        return response()->json($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/{friendly_url}/affiliates/add/csv",
     *     operationId="company/addAffiliatesInvitationsFromCSV/add/csv",
     *     tags={"CompanyClientController"},
     *     summary="Loop thru csv file and add affiliates-invitations",
     *     description="Loop thru csv file and add affiliates-invitations",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/affiliates/invite/csv
    // BODY - form-data
    /*
    	csv_import: CSV FILE
    */
    // Bearer token needed*/
    public function addAffiliatesInvitationsFromCSV(
        CompanyAffiliatesStoreImportFromCSVRequest $request, Company $company): JsonResponse
    {
        $location = __CLASS__ . '::' . __FUNCTION__ . '::';
        CompanyService::validateCompanyIsMSP($company);
        $loggedUser = AuthService::getAuthUser();
        Log::debug($location . 'STARTING CSV LOADING For company_id = ' . $company->id);
        $csvFile = $request->file('csv_import');
        $responseMessage = [];
        if (($handle = fopen($csvFile, 'r')) !== false) {
            Log::debug($location . 'parsing and validating csv');
            $columns = strtolower(preg_replace('/[^A-Za-z|]/', '',
                implode('|', CSVHelper::fgetcsv($handle))));
            if ($columns !== 'email') {
                throw ValidationException::withMessages([
                    config('genericMessages.error.INVALID_AFFILIATES_INVITATION_CSV_FILE'),
                ]);
            }
            $emails = [];
            $errors = [];
            Log::debug($location . 'Preparing emails to be sent');
            while (($data = CSVHelper::fgetcsv($handle)) !== false) {
                $email = trim($data[0]);
                if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    $emails[] = $email;
                } else {
                    $errors[] = $email;
                }
            }
            if (count($errors) > 0) {
                Log::debug($location . 'Invalid emails found: ' . count($errors));
            }
            if (count($errors) > 0) {
                Log::debug($location . 'Sending ' . count($emails) . ' emails');
            }
            if (count($emails) > 0) {
                $processedEmailList = ValidationService::processEmailsList($company, $emails,
                    false, $company->enumType->value);
                if (count($processedEmailList->ok_emails) > 0) {
                    dispatch(new SendInviteAffiliateEmails($processedEmailList->ok_emails, $loggedUser, $company));
                }
                $message = (count($processedEmailList->ok_emails) > 1) ? ' users invited. ' : ' user invited. ';
                $totalErrors = array_merge($errors, $processedEmailList->not_ok_emails);
                $errorMessage = (count($totalErrors) > 1) ? ' issues found. ' : ' issue found. ';
                $responseMessage['success_message'] = count($processedEmailList->ok_emails) . $message
                    . count($totalErrors) . $errorMessage;
                $responseMessage['success_affiliates'] = $processedEmailList->ok_emails;
                $responseMessage['errors_found'] = $totalErrors;
            } else {
                Log::debug($location . config('genericMessages.error.INVALID_CSV_FILE_ONLY_HEADERS'));

                throw ValidationException::withMessages([
                    config('genericMessages.error.INVALID_CSV_FILE_ONLY_HEADERS'),
                ]);
            }
        }

        return response()->json($responseMessage);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{friendly_url}/affiliates/generate-csv",
     *     operationId="/api/v1/company/{friendly_url}/affiliates/generate-csv",
     *     tags={"CompanyClientController"},
     *     summary="Export company affiliates list as a CSV file",
     *     description="Export company affiliates list as a CSV file",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/affiliates/generate-csv
    // OPTIONAL PARAMS
    /*
       ?order_by=COLUMN_NAME&sort=SORT_VALUE
        &search_word=SEARCH_WORD
        &number_of_endpoints=ARRAY<numbers>
        &locations=ARRAY<strings>
     *
     * */
    // needs Bearer Token
    public function generateCSV(CompanyClientShowAllRequest $request, Company $company)
    {
        $affiliates = $this->loadAffiliates($company, $request);
        $columns = [
            'name', 'address', 'friendly_url', 'brand_stack', 'navistack_progress', 'vendor_contract_status',
        ];
        $data = [];
        foreach ($affiliates as $affiliate) {
            $data[] = [
                $affiliate->name,
                $affiliate?->address ?? '',
                $affiliate->friendly_url,
                $affiliate->brand_stack ?? '',
                $affiliate->navistack_progress ?? '',
                $affiliate->vendor_contract_status ?? '',
            ];
        }
        $fileName = 'affiliates-' . time() . '.csv';

        return UtilityHelper::getCSVFileResponse($columns, $data, $fileName);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{friendly_url}/affiliates/invites",
     *     operationId="company/affiliates/showAllInvites",
     *     tags={"CompanyAffiliateController"},
     *     summary="Get company affiliates pending invites",
     *     description="Returns the company affiliates pending invites",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/affiliates/invites
    // Bearer token needed
    public function showAllInvites(CompanyShowAllAffiliateInviteRequest $request, Company $company): AnonymousResourceCollection
    {
        $company = CompanyService::validateCompanyIsMSP($company);
        $sort = $request->sort ?? 'asc';
        $query = $company->invites($request->status, $request->company_type)
            ->with('role:id,company_id,title,display_name,description,key,template_role_id,is_admin,created_at,updated_at')
            ->when($request->has('search_word'), function ($query) use ($request) {
                $query->whereRaw("(lower(email) like '%" . strtolower($request->search_word) . "%')");
            })
            ->when($request->query('start_date') && $request->query('end_date'), function ($query) use ($request) {
                $startDate = Carbon::parse($request->query('start_date'))->startOfDay();
                $endDate = Carbon::parse($request->query('end_date'))->endOfDay();
                $query->where(function ($q) use ($startDate, $endDate) {
                    $q->where(function ($q1) use ($startDate, $endDate) {
                        $q1->whereNotNull('updated_at')
                            ->whereBetween('updated_at', [$startDate, $endDate]);
                    })->orWhere(function ($q2) use ($startDate, $endDate) {
                        $q2->whereNull('last_sent_at')
                            ->whereBetween('created_at', [$startDate, $endDate]);
                    });
                });
            })
            ->when($request->has('order_by'), function ($query) use ($request, $sort) {
                $query->orderBy($request->order_by, $sort);
            });

        $result = UtilityHelper::getSearchRequestQueryResults($request, $query);

        return CompanyInviteResource::collection($result);
    }

    /**
     * @throws ValidationException
     */
    private function loadAffiliates(Company $company, $request)
    {
        CompanyService::validateCompanyIsMSP($company);
        $query = Company::select('companies.id', 'companies.name', 'companies.description', 'companies.address',
            'companies.type', 'companies.company_profile_types_id', 'companies.friendly_url',
            'companies.subdomain', 'companies.parent_id', 'companies.affiliate_id', 'companies.affiliate_brand_id',
            'companies.created_by', 'companies.updated_by', )
            ->with('avatar:id,model_id,model_type,collection_name,custom_properties,disk,mime_type,name,file_name')
            ->withCount('addonsContracts as total_contracts')
            ->where('parent_id', $company->id)
            ->leftJoin('company_profile_types as cpt', 'companies.company_profile_types_id', '=', 'cpt.id')
            ->selectRaw('companies.*, case when cpt.value = \''
                . CompanyProfileTypes::MSPBusinessPremium
                . '\' then \'active\' else \'inactive\' end as "vendor_contract_status"')
            ->when($request->has('search_word'), function ($query) use ($request) {
                $query->whereRaw("(lower(name) like '%" . strtolower($request->search_word) . "%')");
            })->when($request->has('locations'), function ($query) use ($request) {
                $query->whereIn('address', $request->locations);
            });
        if ($request->has('status')) {
            $query->whereHas('companyProfileType', function ($q) use ($request) {
                if ($request->status === 'enabled') {
                    $q->where('value', CompanyProfileTypes::MSPBusinessPremium);
                } else {
                    $q->whereNot('value', CompanyProfileTypes::MSPBusinessPremium);
                }
            });
        }

        return UtilityHelper::getSearchRequestQueryResults($request, $query);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/{friendly_url}/affiliates/invite/resendBulkInviteAffiliate",
     *     operationId="company/affiliates/resendBulkInviteAffiliate",
     *     tags={"CompanyAffiliateController"},
     *     summary="Resend a Invite for affiliate by email to be affiliates of the corporation",
     *     description="Resend a Invite for affiliate by email to be affiliates of the corporation",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/affiliates/invite/resendBulkInviteAffiliate
    // needs Bearer Token
    public function resendBulkInviteAffiliate(Company $company, CompanyAffiliateResendBulkInviteRequest $request): JsonResponse
    {
        try {
            $company = CompanyService::validateCompanyIsMSP($company);
            $invites = $request->input('invites');

            $loggedUser = AuthService::getAuthUser();

            $notSendedEmails = [];

            foreach ($invites as $inviteId) {
                $invite = CompanyInvite::find($inviteId);

                $last_sent = $invite->last_sent_at ?? $invite->created_at;
                if (
                    in_array($invite->type, [CompanyTypeEnum::FranchiseMsp, CompanyTypeEnum::MSP_LOCATION]) &&
                    $last_sent->diffInMinutes(now()) > AppConfig::loadAppConfigByKey('INVITE_RESEND_MIN_INTERVAL_MINUTES', 120)->value
                ) {
                    dispatch(new SendInviteAffiliateEmails([$invite->email], $loggedUser, $company));

                    $invite->last_sent_at = now();
                    $invite->save();
                } else {
                    $notSendedEmails[] = $invite->email;
                }
            }
            if (count($notSendedEmails) > 0) {
                return response()->json(['message' => config('genericMessages.error.INVITE_NOT_SENT'), 'emails' => $notSendedEmails], 429);
            }

            return response()->json(['message' => 'Invite resended'], 200);
        } catch (\Throwable $th) {
            return response()->json(['message' => config('genericMessages.error.INVITE_NOT_SENT') . $th->getMessage()], 500);
        }
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/{friendly_url}/affiliates/invite/resendInviteAllAffiliate",
     *     operationId="company/affiliates/resendInviteAllAffiliate",
     *     tags={"CompanyAffiliateController"},
     *     summary="Resend a Invite for affiliate by email to be affiliates of the corporation",
     *     description="Resend a Invite for affiliate by email to be affiliates of the corporation",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/affiliates/invite/resendInviteAllAffiliate
    // needs Bearer Token
    public function resendInviteAllAffiliate(Company $company): JsonResponse
    {
        try {
            $company = CompanyService::validateCompanyIsMSP($company);
            $allInvites = $company->invites(0,'MSP_LOCATION')->select(
                'company_invites.id',
                'company_invites.email',
                'company_invites.last_sent_at',
                'company_invites.created_at',
                'company_invites.type')->get();

            $loggedUser = AuthService::getAuthUser();

            $notSendedEmails = [];

            foreach ($allInvites as $invite) {
                $last_sent = $invite->last_sent_at ? $invite->last_sent_at : $invite->created_at;
                if (
                    in_array($invite->type, [CompanyTypeEnum::FranchiseMsp, CompanyTypeEnum::MSP_LOCATION]) &&
                    $last_sent->diffInMinutes(now()) > AppConfig::loadAppConfigByKey('INVITE_RESEND_MIN_INTERVAL_MINUTES', 120)->value
                ) {
                    dispatch(new SendInviteAffiliateEmails([$invite->email], $loggedUser, $company));

                    $invite->last_sent_at = now();
                    $invite->save();
                } else {
                    $notSendedEmails[] = $invite->email;
                }
            }
            if (count($notSendedEmails) > 0) {
                return response()->json(['message' => config('genericMessages.error.INVITE_NOT_SENT'), 'emails' => $notSendedEmails], 429);
            }

            return response()->json(['message' => 'Invite resended'], 200);
        } catch (\Throwable $th) {
            return response()->json(['message' => config('genericMessages.error.INVITE_NOT_SENT') . $th->getMessage()], 500);
        }
    }

    private function calculateMainAffiliatesUsingIt(Collection $result): void
    {
        $result->transform(function ($mainCategory) {
            foreach ($mainCategory->affiliates as $mainAffiliate) {
                $count = 0;
                foreach ($mainCategory->sub_categories as $subCategory) {
                    foreach ($subCategory->affiliates as $subAffiliate) {
                        if ($mainAffiliate->id === $subAffiliate->id && $subAffiliate->using_it) {
                            $count++;
                        }
                    }
                }
                if ($count === $mainCategory->sub_categories->count()) {
                    $mainCategory->affiliates_using_it_count++;
                    $mainAffiliate->using_it = true;
                }
            }

            return $mainCategory;
        });
    }

    private function makeResult(Collection $affiliates, Collection $categories, Collection $affiliatePivots): Collection
    {
        $result = collect();
        $affiliatesCount = $affiliates->count();
        foreach ($categories as $key => $items) {
            $mainCategory = $this->makeCategory($key, $affiliatesCount);
            $mainCategory->sub_categories = collect();
            $mainCategory->affiliates = collect();
            foreach ($affiliates as $affiliate) {
                $mainCategory->affiliates->push($this->makeAffiliate($affiliate));
            }
            foreach ($items as $category) {
                $subCategory = $this->makeCategory($category->id, $affiliatesCount);
                foreach ($affiliates as $affiliate) {
                    $tempAffiliate = $this->makeAffiliate($affiliate);
                    if ($affiliatePivots->where('company_id', $affiliate->id)
                        ->where('category_id', $category->id)
                        ->first()) {
                        $subCategory->affiliates_using_it_count++;
                        $tempAffiliate->using_it = true;
                    }
                    $subCategory->affiliates->push($tempAffiliate);
                }
                $subCategory->affiliates = $subCategory->affiliates->sortBy(function ($affiliate) {
                    return strtolower($affiliate->name);
                });
                $mainCategory->sub_categories->push($subCategory);
            }
            $result->push($mainCategory);
        }

        return $result;
    }

    private function makeAffiliate(Company $affiliate): stdClass
    {
        $tempAffiliate = new stdClass();
        $tempAffiliate->id = '' . $affiliate->id;
        $tempAffiliate->name = $affiliate->name;
        $tempAffiliate->friendly_url = $affiliate->friendly_url;
        $tempAffiliate->avatar = $affiliate->avatar;
        $tempAffiliate->using_it = false;

        return $tempAffiliate;
    }

    private function makeCategory(string $key, int $affiliatesCount): stdClass
    {
        $category = new stdClass();
        $category->id = $key;
        $category->affiliates_using_it_count = 0;
        $category->affiliates_count = $affiliatesCount;
        $category->affiliates = collect();

        return $category;
    }
}
