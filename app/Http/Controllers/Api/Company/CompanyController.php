<?php

namespace App\Http\Controllers\Api\Company;

use App\Enums\ActivityLogAction;
use App\Enums\AppConfigEnum;
use App\Enums\Company\CompanyProfileTypes;
use App\Enums\Company\CompanyType as CompanyTypeEnum;
use App\Enums\ModelType;
use App\Enums\NavigationFavoritesSection;
use App\Enums\ProfileImageType;
use App\Enums\User\CompanyUsersAndClaimersFilters;
use App\Enums\UserStatus;
use App\Helpers\CSVHelper;
use App\Helpers\UtilityHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\AdminSearchRequest;
use App\Http\Requests\Blog\BlogShowAllRequest;
use App\Http\Requests\Company\CompanyCategoryStoreRequest;
use App\Http\Requests\Company\CompanyCSVRequest;
use App\Http\Requests\Company\CompanyDeleteRequest;
use App\Http\Requests\Company\CompanyHomePageAvatarsRequest;
use App\Http\Requests\Company\CompanySearchByValueRequest;
use App\Http\Requests\Company\CompanySearchRequest;
use App\Http\Requests\Company\CompanyShowAllProductsRequest;
use App\Http\Requests\Company\CompanyStoreRequest;
use App\Http\Requests\Company\CompanyTopTrendingRequest;
use App\Http\Requests\Company\CompanyUpdateAffiliatePopupRequest;
use App\Http\Requests\Company\CompanyUpdateBannerFlagRequest;
use App\Http\Requests\Company\CompanyUpdateHideExpensesRequest;
use App\Http\Requests\Company\CompanyUpdateRequest;
use App\Http\Requests\Company\StoreVendorOrProductRequest;
use App\Http\Resources\Blog\BlogResource;
use App\Http\Resources\Category\CategoryResource;
use App\Http\Resources\Company\CompanyHomePageAvatarsResource;
use App\Http\Resources\Company\CompanyProductResource;
use App\Http\Resources\Company\CompanyResource;
use App\Http\Resources\Company\CompanyResourceForFriendlyURL;
use App\Http\Resources\Company\CompanyResourceForSideBar;
use App\Http\Resources\Company\CompanyTopTrendingResource;
use App\Http\Resources\Company\CompanyTypeResource;
use App\Http\Resources\Permission\Role\RoleResource;
use App\Http\Resources\UserResource;
use App\Models\AffiliateBrand\AffiliateBrand;
use App\Models\Category\Category;
use App\Models\Company\Company;
use App\Models\Company\CompanyCategory;
use App\Models\Company\CompanyClaimer;
use App\Models\Company\CompanyType;
use App\Models\Contract\Contract;
use App\Models\Country;
use App\Models\MyStack\CustomerStack;
use App\Models\MyStack\MyStack;
use App\Models\NavigationFavorites;
use App\Models\Permission\Role\Role;
use App\Models\Profile\CompanyProfileType;
use App\Models\State;
use App\Models\Subscription\CompaniesSubscriptionHistory;
use App\Models\User;
use App\Services\ActivityLogs\ActivityLogsService;
use App\Services\AppConfig;
use App\Services\AuthService;
use App\Services\BlogService;
use App\Services\Company\CompanyClaimerResponseService;
use App\Services\Company\CompanyProfileTypeService;
use App\Services\Company\CompanyService;
use App\Services\Company\CompanyTypeService;
use App\Services\ImageService;
use App\Services\ProductService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class CompanyController extends Controller
{
    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company",
     *     operationId="company/showAll",
     *     tags={"CompanyController"},
     *     summary="Get companies",
     *     description="Returns the companies",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *
     *         @OA\JsonContent(ref="#/components/schemas/CompanyResource")
     *     ),
     *
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company
    // Bearer token needed
    public function showAll(): AnonymousResourceCollection
    {
        return CompanyResource::collection(Company::orderby('name', 'ASC')->with('state', 'enumType', 'companyClaimers', 'categories')
            ->paginate(config('common.searchPagingLength')));
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/companytypes",
     *     operationId="company/companytypes",
     *     tags={"CompanyController"},
     *     summary="Get companies types",
     *     description="Returns the companies types",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/companytypes
    // Bearer token needed
    public function companyTypes(): AnonymousResourceCollection
    {
        return CompanyTypeResource::collection(CompanyType::orderby('order', 'ASC')->get());
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{company}",
     *     operationId="company/showById",
     *     tags={"CompanyController"},
     *     summary="Get company by id",
     *     description="Returns the company",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *
     *         @OA\JsonContent(ref="#/components/schemas/CompanyResource")
     *     ),
     *
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{company}
    // NO Bearer token needed
    public function showById(Company $company): CompanyResource
    {
        $company->load([
            'users' => function ($query) {
                $query->select([
                    'id', 'company_id', 'first_name', 'last_name',
                    'handle', 'friendly_url', 'is_profile_complete', 'show_on_vendor_related_section',
                ])
                    ->public()
                    ->IsVisibleOnVendorProfile();
            },
            'enumType',
            'companyClaimers',
            'categories',
            'companyProfileType',
            'likes',
            'blogs',
            'blogs.categories',
            'products',
            'products.categories',
        ]);
        $company->products = ProductService::appendImages($company->products);
        $company->products = ProductService::appendVideos($company->products);
        $company->users = ImageService::appendUsersAvatars($company->users);
        $company->blogs = BlogService::appendImagesToCollection($company->blogs);

        return new CompanyResource($company);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{company}/subscription",
     *     operationId="company/subscription",
     *     tags={"CompanyController"},
     *     summary="Get the company subscription",
     *     description="Get the company subscription",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *
     *         @OA\JsonContent(ref="#/components/schemas/CompanyCategoryStoreRequest")
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/{company}/subscription
    // needs Bearer Token*/
    public function subscription(Company $company): JsonResponse
    {
        $activeSubscription = CompanyProfileTypeService::getCompanyActiveSubscription($company);

        return response()->json($activeSubscription);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/handle/{profile_vendor_handle}",
     *     operationId="company/showByProfileVendorHandle",
     *     tags={"CompanyController"},
     *     summary="Get company by profile_vendor_handle",
     *     description="Returns the company found by profile_vendor_handle",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *
     *         @OA\JsonContent(ref="#/components/schemas/CompanyResource")
     *     ),
     *
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/handle/{profile_vendor_handle}
    // NO Bearer token needed
    public function showByProfileVendorHandle($profileVendorHandle): CompanyResource
    {
        $company = Company::with([
            'users' => function ($query) {
                $query->select([
                    'id', 'company_id', 'first_name', 'last_name',
                    'handle', 'friendly_url', 'is_profile_complete', 'show_on_vendor_related_section',
                ])
                    ->public()
                    ->IsVisibleOnVendorProfile();
            },
            'enumType',
            'companyClaimers',
            'categories',
            'companyProfileType',
            'likes',
            'products',
            'products.categories',
        ])
            ->where('profile_vendor_handle', $profileVendorHandle)
            ->first();
        if (!$company) {
            abort(404, config('genericMessages.success.NO_RESULTS'));
        }
        $company->products = ProductService::appendImages($company->products);
        $company->products = ProductService::appendVideos($company->products);
        $company->users = ImageService::appendUsersAvatars($company->users);

        return new CompanyResource($company);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{searchBy}/{searchValue}",
     *     operationId="company/showByFriendlyUrl",
     *     tags={"CompanyController"},
     *     summary="Get company by friendly_url or subdomain",
     *     description="Returns the company found by friendly_url or subdomain",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *
     *         @OA\JsonContent(ref="#/components/schemas/CompanyResource")
     *     ),
     *
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{searchBy}/{searchValue}
    // for example: http://127.0.0.1:8000/api/v1/company/friendly_url/{companyName}
    // another example: http://127.0.0.1:8000/api/v1/company/subdomain/{companyName}
    // NO Bearer token needed
    public function showByFriendlyUrl(CompanySearchByValueRequest $request): CompanyResourceForFriendlyURL
    {
        $value = $request->route()?->parameter('value');
        $searchBy = $request->route()?->parameter('search_by');
        $requestedFields = $request->requested_fields ?? [];

        $company = CompanyService::loadCompanyByFriendlyUrl($value, $searchBy, $requestedFields);

        if (!empty($requestedFields)) {
            $this->loadRequestedCompanyData($company, $requestedFields);
        } else {
            $this->loadDefaultCompanyData($company);
        }

        return new CompanyResourceForFriendlyURL($company);
    }

    private function loadDefaultCompanyData($company): void
    {
        $company->products = ProductService::appendImages($company->products);
        $company->products = ProductService::appendVideos($company->products);
        $company->users = ImageService::appendUsersAvatars($company->users);
        CompanyService::calculateRatings(Collection::make([$company]));

        $company->is_affiliate_brand_main_company = $company->isAffiliateBrandMainCompany();

        if ($company->parent_id) {
            $company->load('parentCompany');
            $company->parentCompany->avatar = ImageService::findAvatarInDB(
                $company->parentCompany,
                ProfileImageType::CompanyAvatar,
                false
            );
        }

        if ($company->enumType->value === \App\Enums\Company\CompanyType::MSP_CLIENT) {
            $company->load('clientParent.enumType');
        }

        if (!$company->vendor) {
            $company->focuses = CompanyService::getFocuses($company);
        }
    }

    private function loadRequestedCompanyData($company, array $fields): void
    {
        if (in_array('is_mdf', $fields)) {
            $company->is_mdf = CompanyService::checkMDFIsAvailable($company->id);
        }

        if (in_array('focuses', $fields) && !$company->vendor) {
            $company->focuses = CompanyService::getFocuses($company);
        }

        $shouldLoadProducts = in_array('products', $fields);
        $shouldLoadRatings = in_array('rating', $fields);

        if ($shouldLoadRatings || $shouldLoadProducts) {
            $company->load('products');
        }

        if ($shouldLoadRatings) {
            CompanyService::calculateRatings(Collection::make([$company]));
        }

        if (in_array('is_affiliate_brand_main_company', $fields)) {
            $company->is_affiliate_brand_main_company = $company->isAffiliateBrandMainCompany();
        }

        if ($shouldLoadProducts) {
            $company->products = ProductService::appendImages($company->products);
            $company->products = ProductService::appendVideos($company->products);
        }

        if (in_array('users', $fields)) {
            $company->users = ImageService::appendUsersAvatars($company->users);
        }

        if (in_array('claimers', $fields) && $company->claimers->isNotEmpty()) {
            ImageService::appendUsersAvatars($company->claimers);
        }

        if (in_array('followers_count', $fields)) {
            $loggedUser = AuthService::getAuthUser();
            $company->followers_count = $company->usersFollowingMeCount();
            $company->user_is_following_me = $loggedUser
                ? $company->userIsFollowingMe($loggedUser->id)
                : false;
        }
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{search_by}/{value}/customer-is-accessible",
     *     operationId="company/CustomerIsAccessible",
     *     tags={"CompanyController"},
     *     summary="Check if the customer can access to this friendly_url or subdomain",
     *     description="Returns true or false if the customer is accessible to the friendly_url or subdomain",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *
     *         @OA\JsonContent(ref="#/components/schemas/CompanyResource")
     *     ),
     *
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{search_by}/{value}/customer-is-accessible
    // for example: http://127.0.0.1:8000/api/v1/company/friendly_url/{companyName}/customer-is-accessible
    // NO Bearer token needed
    public function customerIsAccessible(CompanySearchByValueRequest $request): JsonResponse
    {
        $company = CompanyService::loadCompanyByFriendlyUrl(
            $request->value,
            $request->search_by
        );
        $user = AuthService::getAuthUser();
        $isAccessible = CompanyService::companyIsAccessible($user, app('asCompanyId'), $company->id);

        return response()->json(['accessible' => $isAccessible]);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{searchBy}/{searchValue}/blogs",
     *     operationId="company/showBlogs",
     *     tags={"CompanyController"},
     *     summary="Get blogs for a particular company",
     *     description="Get blogs for a particular company",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{searchBy}/{searchValue}/blogs
    // PARAMS
    /*
       ?paged=sometimes|required|boolean send 1 or 0
       &page=sometimes|required|numeric
       &items_per_page=sometimes|required|numeric|min:1
       &search_word=sometimes|required|string|min: . config('common.searchMinForCompany')
       &status[]=sometimes|required|array|AllowedValues

    / no Bearer Token NEEDED*/
    public function showBlogs(BlogShowAllRequest $request, $searchBy, $searchValue): AnonymousResourceCollection
    {
        // This line is called, so it validates that the company can be used
        $company = CompanyService::loadCompanyByFriendlyUrl($searchValue, $searchBy);

        return BlogResource::collection(
            BlogService::findBlogsByModelFriendlyUrl(
                $request,
                'companies',
                $searchValue,
                $company,
                $searchBy
            )
        );
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{friendly_url}/claimers",
     *     operationId="company/showClaimers",
     *     tags={"CompanyController"},
     *     summary="Get claimers for a particular company",
     *     description="Get claimers for a particular company",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/claimers
    // Bearer Token NEEDED*/
    public function showClaimers(Company $company): AnonymousResourceCollection
    {
        $company->load('claimers.avatar');

        return UserResource::collection($company->claimers);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{friendly_url}/roles",
     *     operationId="company/showRoles",
     *     tags={"CompanyController"},
     *     summary="Get roles for a particular company",
     *     description="Get roles for a particular company",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/roles
    // Bearer Token NEEDED*/
    public function showRoles(Company $company): AnonymousResourceCollection
    {
        $company->load('roles');

        return RoleResource::collection($company->roles->sortBy('title'));
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/{company}/users/filters",
     *     operationId="company/showFiltersForUsers",
     *     tags={"UserController"},
     *     summary="Show all available filters for Users and Claimers from a provided company",
     *     description="Show all available filters for Users and Claimers from a provided company",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{company}/users/filters
    // Bearer Token needed*/
    public function showFiltersForUsers(Request $request, Company $company): JsonResponse
    {
        $filters = [
            'status' => CompanyUsersAndClaimersFilters::STATUS,
        ];

        // Getting statuses
        $filters['status']['items'] = [];
        $statuses = UserStatus::getKeys();
        foreach ($statuses as $status) {
            $filters['status']['items'][UserStatus::getValue($status)] = $status;
        }
        // Getting companies
        $roles = Role::where('company_id', $company->id)
            ->select(['id', 'title', 'company_id'])
            ->orderBy('title')
            ->get();
        if ($roles && $roles->count() > 0) {
            $filters['roles'] = CompanyUsersAndClaimersFilters::ROLES;
            $filters['roles']['items'] = [];
            foreach ($roles as $role) {
                $filters['roles']['items']['' . $role->id] = $role->title;
            }
        }

        return response()->json([
            'filters' => $filters,
        ]);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/{company}/users",
     *     operationId="company/showUsers",
     *     tags={"UserController"},
     *     summary="Searching Users and Claimers from a provided company",
     *     description="Searching Users and Claimers from a provided company",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{company}/users
    // Bearer Token needed*/
    public function showUsers(AdminSearchRequest $request, Company $company): AnonymousResourceCollection
    {
        $query = $company->allUsers();
        if ($request->has('search_word')) {
            $query->where(function ($q) use ($request) {
                $value = strtolower(trim($request->search_word));
                $q->orWhereRaw('lower(first_name) like ?', ['%' . $value . '%']);
                $q->orWhereRaw('lower(last_name) like ?', ['%' . $value . '%']);
                $q->orWhereRaw('lower(email) like ?', ['%' . $value . '%']);
            });
        }
        $result = UtilityHelper::getSearchRequestQueryResults($request, $query);

        return UserResource::collection($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/search",
     *     operationId="company/search",
     *     tags={"CompanyController"},
     *     summary="Search for companies that are claimed",
     *     description="Returns companies that are claimed",
     *
     *     @OA\RequestBody(
     *         required=true,
     *
     *         @OA\JsonContent(ref="#/components/schemas/CompanySearchRequest")
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/search
    // BODY
    /*{
    	"name": "Company 6",
        "company_type": "MSP",
        "company_types": [Existing company types],
        "type_is_of_vendor": true | false
        "distributors": "include" | "exclude" | "strict",
        "must_have_claimers": boolean,
        "use_fuzzy_search": boolean
    }*/
    // Bearer Token NOT needed
    public function search(CompanySearchRequest $request): AnonymousResourceCollection
    {
        $useFuzzy = $request->has('name') && $request->has('use_fuzzy_search') && (bool)$request->use_fuzzy_search;
        $searchTerms = $useFuzzy
            ? explode(' ', strtolower($request->name))
            : null;
        $query = Company::with('state', 'enumType', 'companyClaimers', 'categories')
            ->when((!$request->has('distributors') && !$request->has('must_have_claimers')) || $request->must_have_claimers, function ($query) {
                $query->whereHas('companyClaimers');
            })
            ->when($request->has('ignore_ids'), function ($query) use ($request) {
                $query->whereNotIn('id', explode(',', $request->ignore_ids));
            })
            ->when($request->has('name'), function ($query) use ($request, $useFuzzy, $searchTerms) {
                $query->where(function ($query) use ($request, $useFuzzy, $searchTerms) {
                    $searchText = strtolower(urldecode(($request->name)));
                    $searchText = str_replace("'", "''", $searchText);
                    // Applying search by full text
                    $query->whereRaw('companies.name ilike ?', ['%' . $searchText . '%']);
                    // Applying fuzzy search
                    if ($useFuzzy) {
                        foreach ($searchTerms as $term) {
                            $query->orWhereRaw("similarity(companies.name, '" . $term . "') > 0");
                        }
                    }
                });
            })
            ->when($request->has('company_type'), function ($query) use ($request) {
                $query->whereHas('enumType', function ($q) use ($request) {
                    $q->where('value', $request->company_type);
                });
            })
            ->when($request->has('company_types'), function ($query) use ($request) {
                $query->whereHas('enumType', function ($q) use ($request) {
                    $q->whereIn('value', $request->company_types);
                });
            })
            ->when($request->has('type_is_of_vendor'), function ($query) use ($request) {
                $query->whereHas('enumType', function ($q) use ($request) {
                    $q->where('type_is_of_vendor', $request->type_is_of_vendor);
                });
                // Include, exclude, or restrict the search to only distributors.
                $distributors = $request->distributors ?? 'exclude';
                if ($distributors !== 'include') {
                    switch ($distributors) {
                        case 'restrict':
                            $query->where('is_distributor', true);

                            break;
                        default:
                            $query->where(function ($q) {
                                $q->where('is_distributor', false)->orWhereNull('is_distributor');
                            });

                            break;
                    }
                }
            })
            ->when($useFuzzy, function ($query) use ($request) {
                $query->orderByRaw('similarity(companies.name, ?) DESC', [$request->name]);
            });

        return CompanyResource::collection($query->paginate(config('common.searchPagingLength')));
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/store",
     *     operationId="company/store",
     *     tags={"CompanyController"},
     *     summary="Store new company",
     *     description="Creates a new company",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *
     *         @OA\JsonContent(ref="#/components/schemas/CompanyStoreRequest")
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/store
    // BODY
    // needs Bearer Token
    public function store(CompanyStoreRequest $request): CompanyResource
    {
        $companyData = $request->all();
        $companyType = CompanyType::firstWhere('value', $request->company_type);
        $companyData['type'] = $companyType->id;
        if ($request->has('company_profile_types_id')) {
            $defaultCompanyProfileType = CompanyProfileType::find($request->company_profile_types_id);
        } else {
            $defaultCompanyProfileType = $companyType->defaultCompanyProfileType;
        }
        if ($request->has('state')) {
            $state = State::select('id')->where('name', $request->state)
                ->orWhere('abbreviation', $request->state)->first();
            if (!empty($state)) {
                $companyData['state_id'] = $state->id;
            }
        }
        if ($request->has('country')) {
            $country = Country::select('id')->where('short_code', $request->country)
                ->orWhere('title', $request->country)->first();
            if (!empty($country)) {
                $companyData['country_id'] = $country->id;
            }
        }
        $companyData['created_by'] = AuthService::getLoggedInUserId();
        $companyData['updated_by'] = AuthService::getLoggedInUserId();
        $company = Company::make($companyData);
        $company->company_profile_types_id = $defaultCompanyProfileType ? $defaultCompanyProfileType->id : null;
        $company->friendly_url = UtilityHelper::generateUniqueWord('companies', 'friendly_url',
            UtilityHelper::generateUniqueWord('users', 'friendly_url', $company->name)
        );
        if (!$request->has('profile_vendor_handle')) {
            $company->profile_vendor_handle =
                UtilityHelper::generateUniqueWord('companies', 'profile_vendor_handle',
                    UtilityHelper::generateUniqueWord('users', 'handle', $company->name));
        }
        $company->save();
        if ($request->company_type === \App\Enums\Company\CompanyType::FRANCHISE_CORPORATE_MSP
            && $request->has('affiliate_brand_id')) {
            AffiliateBrand::where('id', $request->affiliate_brand_id)->update(['main_company_id' => $company->id]);
        }
        CompaniesSubscriptionHistory::create([
            'company_id' => $company->id,
            'company_profile_types_id' => $defaultCompanyProfileType->id,
            'is_active' => $request->subscription_status ?? true,
            'started_at' => $request->subscription_start_date ?? now(),
            'ended_at' => $request->subscription_end_date ?? null,
        ]);
        if ($request->has('claimers')) {
            $claimers = User::whereIn('id', $request->claimers)->get();
            $roleId = $company->superAdminRole()->id;
            foreach ($claimers as $claimer) {
                CompanyClaimerResponseService::addClaimer(
                    $company, $claimer, $request->send_notification_email ?? false, $roleId);
            }
        }

        return new CompanyResource($company);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/company/{company_id}/update",
     *     operationId="company/update",
     *     tags={"CompanyController"},
     *     summary="Update company",
     *     description="Returns ok",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *
     *         @OA\JsonContent(ref="#/components/schemas/CompanyUpdateRequest")
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: PUT
    // http://127.0.0.1:8000/api/v1/company/{company_id}/update
    // BODY
    /*{
        "id" : "required|numeric|exists:company,id",
        "name" => ["sometimes", "string", "max:191", profanity, UniqueField],
        "description" : "string|max:4000",
        "company_type" : "sometimes|required|exists:company_types,value",
        "revenue" : "numeric",
        "subdomain" : "sometimes|required|string|min:3|max:255",
        "address" : "nullable|string|max:255",
        "address2" : "nullable|string|max:255",
        "city" : "nullable|string|max:255",
        "state_id" : "nullable|numeric|exists:states,id",
        "zip" : "nullable|string|max:10",
        "country_id" : "nullable|numeric|exists:countries,id",
        "phone" : "nullable|string|max:25",
        "industry" : "nullable|string|max:255",
        "employee_range" : "nullable|string|max:255",
        "founded" : "nullable|numeric|between:1900,$currentYear",
        "features" : "nullable|array",
        "profile_company_website_url" : "nullable|string|max:100",
        "profile_vendor_handle" : "sometimes|required|string|no_white_spaces|min:5|max:50|unique",
        "profile_company_friendly_name" : "nullable|string|max:50",
        "show_distributor_banner": "sometimes|required|boolean",
        "show_manage_clients_banner": "sometimes|required|boolean",
        "show_manage_affiliates_banner": "sometimes|required|boolean",
        "parent_id" : "nullable|numeric|exists:companies,id",
        "affiliate_brand_id": "sometimes|numeric|exists:affiliate_brands,id",
        "affiliate_id" : "nullable|string|max:255",
        "state" : "nullable|string|max:255",
        "country" : "nullable|string|max:255",
    }*/
    // needs Bearer Token
    public function update(CompanyUpdateRequest $request, Company $company): CompanyResource
    {
        // $company should exist because the ValidateRolesPermissionMiddleware call the company as an instance (AuthService->hasInheritedPermissions)
        $companyUpdate = Company::with('companyClaimers', 'enumType', 'companyProfileType')
            ->findOrFail($request->id);
        if ($request->has('profile_vendor_handle')) {
            $response = Gate::inspect('update-company-handle', $companyUpdate);
            if (!$response->allowed()) {
                abort(403, $response->message());
            }
        }
        if ($request->has('name')) {
            $response = Gate::inspect('update-company-name', $companyUpdate);
            if (!$response->allowed()) {
                abort(403, $response->message());
            }
            if (strtolower($request->get('name')) !== strtolower($companyUpdate->name)) {
                CompanyService::updateCompanyFriendlyUrl($companyUpdate, $request);
                CompanyService::updateCompanySubdomain($companyUpdate, $request);
            }
        }
        if ($request->has('subdomain')) {
            if (strtolower($request->get('subdomain')) !== strtolower($companyUpdate->subdomain)) {
                $request->merge(['subdomain' => strtolower($request->get('subdomain'))]);
            }
        }
        if ($request->has('state')) {
            $state = State::select('id')->where('name', $request->state)
                ->orWhere('abbreviation', $request->state)->first();

            $request->merge(['state_id' => $state?->id ?? null]);
        }

        if ($request->has('country')) {
            $country = Country::select('id')->where('short_code', $request->country)
                ->orWhere('title', $request->country)->first();

            $request->merge(['country_id' => $country?->id ?? null]);
        }
        if ($request->has('company_type')) {
            CompanyTypeService::updateCompanyType($companyUpdate, $request->company_type);
        }
        if ($request->has('affiliate_brand_id') && !$request->has('parent_id')
            && $companyUpdate->manage_affiliates) {
            AffiliateBrand::where('main_company_id', $companyUpdate->id)
                ->update(['main_company_id' => null]);
            AffiliateBrand::where('id', $request->affiliate_brand_id)
                ->update(['main_company_id' => $companyUpdate->id]);
        }
        $request->merge(['updated_by' => AuthService::getLoggedInUserId()]);
        $companyUpdate->update($request->all());

        return new CompanyResource($companyUpdate);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/company/update-banner-flag",
     *     operationId="company/updateBannerFlag",
     *     tags={"CompanyController"},
     *     summary="Update banner flags of a company",
     *     description="Update banner flags of a company",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: PUT
    // http://127.0.0.1:8000/api/v1/company/update-banner-flag
    // BODY
    /*{
        "id" : "required|numeric|exists:company,id",
        "show_manage_clients_banner": "sometimes|required|boolean",
        "show_manage_affiliates_banner": "sometimes|required|boolean",
        "show_distributor_banner": "sometimes|required|boolean"
    }*/
    // needs Bearer Token
    public function updateBannerFlag(CompanyUpdateBannerFlagRequest $request): CompanyResource
    {
        $companyUpdate = Company::findOrFail($request->id);
        $companyUpdate->update($request->validated());

        return new CompanyResource($companyUpdate);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/company/update-hide-expenses",
     *     operationId="company/updateHideExpenses",
     *     tags={"CompanyController"},
     *     summary="Update hide expenses of a company",
     *     description="Update hide expenses of a company",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: PUT
    // http://127.0.0.1:8000/api/v1/company/update-hide-expenses
    // needs Bearer Token
    public function updateHideExpenses(CompanyUpdateHideExpensesRequest $request): CompanyResource
    {
        $companyUpdate = Company::findOrFail($request->id);
        $companyUpdate->update($request->validated());

        return new CompanyResource($companyUpdate);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/company/update-affiliate-popup",
     *     operationId="company/updateAffiliatePopup",
     *     tags={"CompanyController"},
     *     summary="Update show affiliate popup of a company",
     *     description="Update show affiliate popup of a company",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: PUT
    // http://127.0.0.1:8000/api/v1/company/update-affiliate-popup
    // BODY
    /*{
        "id" : "required|numeric|exists:company,id",
        "show_affiliate_popup": "sometimes|required|boolean"
    }*/
    // needs Bearer Token
    public function updateAffiliatePopup(CompanyUpdateAffiliatePopupRequest $request): CompanyResource
    {
        $companyUpdate = Company::findOrFail($request->id);
        $companyUpdate->update($request->validated());

        return new CompanyResource($companyUpdate);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Delete(
     *     path="/api/v1/company/delete",
     *     operationId="company/delete",
     *     tags={"CompanyController"},
     *     summary="Delete existing company",
     *     description="Deletes a record and returns no content",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *
     *         @OA\JsonContent(ref="#/components/schemas/CompanyDeleteRequest")
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: DELETE
    // http://127.0.0.1:8000/api/v1/company/delete
    /* BODY
    {
    	"id": "611515154484"
    }*/
    // needs Bearer Token
    public function delete(CompanyDeleteRequest $request)
    {
        $company = Company::findOrFail($request->id);

        try {
            $company->delete();
        } catch (\Exception $e) {
            throw ValidationException::withMessages(['ERROR' => $e->getMessage()]);
        }

        return response()->json();
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{company}/product-categories",
     *     operationId="company/productCategories",
     *     tags={"CompanyController"},
     *     summary="Get categories that are used on this company products",
     *     description="categories that are used on this company products",
     *
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Not Found"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{company}/product-categories
    // No Bearer token needed
    public function productCategories(Company $company)
    {
        $company->load(['products.categories']);
        $categories = collect();
        $company->products->each(function ($product) use (&$categories) {
            if (!$product->categories->isEmpty()) {
                $categories->push($product->categories->values()->all());
            }
        });

        return CategoryResource::collection($categories->flatten()->unique('id')->sortBy('name'));
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/{company}/category/store",
     *     operationId="company/storeCategory",
     *     tags={"CompanyController"},
     *     summary="Store new category for the company",
     *     description="Creates a new category for the company",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *
     *         @OA\JsonContent(ref="#/components/schemas/CompanyCategoryStoreRequest")
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/{company}/category/store
    // BODY
    /*{
    	"category_id": "required|number|exists:categories",
    }
    needs Bearer Token*/
    public function storeCategory(CompanyCategoryStoreRequest $request, Company $company): JsonResponse
    {
        $company->load('categories');
        if ($company->categories->where('id', $request->category_id)->count() > 0) {
            throw ValidationException::withMessages([
                config('genericMessages.error.CATEGORY_ALREADY_ASSIGNED'),
            ]);
        }
        if ($company->categories->count() > 2) {
            throw ValidationException::withMessages([
                config('genericMessages.error.MAX_VENDOR_CATEGORIES_REACHED'),
            ]);
        }
        CompanyCategory::create(['company_id' => $company->id, 'category_id' => $request->category_id]);

        return response()->json();
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Delete(
     *     path="/api/v1/company/{company}/category/delete/{category}",
     *     operationId="company/deleteCategory",
     *     tags={"CompanyController"},
     *     summary="Delete category for the company",
     *     description="Delete a category from the company",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: DELETE
    // http://127.0.0.1:8000/api/v1/company/{company}/category/delete/{category}
    /*
    needs Bearer Token*/
    public function deleteCategory(Company $company, Category $category): JsonResponse
    {
        CompanyCategory::where('company_id', $company->id)
            ->where('category_id', $category->id)
            ->delete();

        return response()->json();
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/companies/add/bulk",
     *     operationId="company/addCompaniesFromCSV",
     *     tags={"CompanyController"},
     *     summary="Loop thru csv file and add companies to table",
     *     description="",
     *
     *     @OA\RequestBody(
     *         required=true,
     *
     *         @OA\JsonContent(ref="#/components/schemas/CompanyCSVRequest")
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/add/bulk
    // BODY - form-data
    /*
    	csv_import: CSV FILE
        claimer_id: a user_id
    */
    // ADMIN Bearer token needed*/
    public function addCompaniesFromCSV(CompanyCSVRequest $request): JsonResponse
    {
        Log::debug('Entering addCompaniesFromCSV');
        $claimerId = $request->claimer_id;
        $csvFile = $request->file('csv_import');
        $companyType = CompanyType::select('id', 'label', 'value', 'order', 'type_is_of_vendor',
            'default_company_profile_type_id')->where('value', 'VENDOR_ALL')->first();
        // loop through file
        if (($handle = fopen($csvFile, 'r')) !== false) {
            $defaultCompanyProfileType = $companyType->defaultCompanyProfileType;
            CSVHelper::fgetcsv($handle);  // read one line for nothing (skip header)
            while (($data = CSVHelper::fgetcsv($handle)) !== false) {
                $companyName = trim($data[1]);
                $companyDescription = trim($data[11]);
                $category = trim($data[9]);
                $address = '';
                $website = trim($data[12]);

                // not sure if these fields will be blank and don't want to add extra spaces to the address field
                if (trim($data[2]) !== '') {
                    $address = trim($data[2]) . ' ';
                }

                if (trim($data[4]) !== '') {
                    $address .= trim($data[4]) . ' ';
                }

                if (trim($data[5]) !== '') {
                    $address .= trim($data[5]) . ' ';
                }

                if (trim($data[6]) !== '') {
                    $address .= trim($data[6]) . ' ';
                }

                if (trim($data[7]) !== '') {
                    $address .= trim($data[7]);
                }

                $address = trim($address);

                try {
                    Log::debug('addCompaniesFromCSV checking record: ' . $companyName);

                    // check company doesn't exist and if not, create a basic of it
                    $company = CompanyService::getCompany($companyName, $companyType->value);

                    // update the company fields that we need to
                    $company->description = $companyDescription;
                    $company->address = $address;
                    $company->company_profile_types_id = $defaultCompanyProfileType?->id;
                    $company->profile_company_website_url = $website;
                    $company->save();

                    ActivityLogsService::store(
                        ActivityLogAction::addedPlan,
                        null,
                        null,
                        ModelType::companies,
                        $company->id,
                        ['company_profile_types_id' => '' . $company->company_profile_types_id]
                    );

                    // see if category given exists
                    $categoryFound = Category::where('name', '=', $category)->first();

                    if ($categoryFound !== null) {
                        // make sure this company and category don't already exist
                        $companyCategoryFound = CompanyCategory::where('company_id', '=', $company->id)
                            ->where('category_id', '=', $categoryFound->id)
                            ->first();

                        if ($companyCategoryFound === null) {
                            CompanyCategory::create([
                                'company_id' => $company->id,
                                'category_id' => $categoryFound->id,
                            ]);
                        }
                    }

                    // add claimer, if the company doesn't already have one
                    $companyClaimer = CompanyClaimer::where('company_id', '=', $company->id)->first();

                    if ($companyClaimer === null) {
                        $companyClaimer = CompanyClaimer::create([
                            'company_id' => $company->id,
                            'user_id' => $claimerId,
                        ]);
                        ActivityLogsService::store(
                            ActivityLogAction::addedClaimer,
                            null,
                            null,
                            ModelType::companies,
                            $company->id,
                            ['company_claimer_id' => '' . $companyClaimer->id]
                        );
                    }
                } catch (\Exception $exception) {
                    // log error but continue to next line
                    Log::error('Error happened in addCompaniesFromCSV on ' . $companyName);
                    Log::error($exception);
                }
            }
        }

        return response()->json();
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/top-trending",
     *     operationId="company/topTrending",
     *     tags={"CompanyController"},
     *     summary="Get top trending companies",
     *     description="Returns the top trending companies",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/top-trending
    // Bearer token NOT needed
    public function topTrending(CompanyTopTrendingRequest $request): AnonymousResourceCollection
    {
        $endDate = now()->endOfDay();
        $startDate = now()->subDays((int)AppConfig::loadAppConfigByKey(AppConfigEnum::TOP_TRENDING_INITIAL_DAYS,
            config('common.topTrendingInitialDays'))->value)->startOfDay();
        $listOfDontShowComp = AppConfig::loadAppConfigByKey('DONT_SHOW_COMPANIES_ON_STACK_CHART_OR_TOP_TRENDING');
        $companiesIds = CompanyService::getCompaniesIdsFromStringList($listOfDontShowComp->value);
        $results = CompanyService::getTopTrendingResults(collect(), $startDate, $endDate,
            $request->num_of_companies ?? (int)AppConfig::loadAppConfigByKey(
                AppConfigEnum::TOP_TRENDING_COMPANIES,
                config('common.topTrendingCompanies'))->value, $companiesIds);
        $companies = Company::with('productsCategories')
            ->whereIn('id', $results->pluck('id')->toArray())->get();
        $results->transform(function ($element) use ($companies) {
            $found = $companies->where('id', $element->id)->first();
            if (!empty($found)) {
                $element->name = $found->name;
                $element->friendly_url = $found->friendly_url;
                $element->subdomain = $found->subdomain;
                $element->products_categories = $found->productsCategories;
                $element->is_distributor = $found->is_distributor;
                $element->manage_clients = $found->manage_clients;
                $element->manage_expenses = $found->manage_expenses;
                $element->manage_affiliates = $found->manage_affiliates;
            }

            return $element;
        });
        ImageService::appendCompanyAvatars($results);

        return CompanyTopTrendingResource::collection($results);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/avatars",
     *     operationId="company/showHomePageAvatars",
     *     tags={"CompanyController"},
     *     summary="Gets the name and avatar of all vendors who have an avatar and whose company_profile_type is not Vendor Free.",
     *     description="Gets the name and avatar of all vendors who have an avatar and whose company_profile_type is not Vendor Free.",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *
     *         @OA\JsonContent(ref="#/components/schemas/CompanyResource")
     *     ),
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/avatars
    // OPTIONAL PARAMS
    /*
        ?num_of_companies=sometimes|numeric|min:1
    */
    // Bearer token NOT needed
    public function showHomePageAvatars(CompanyHomePageAvatarsRequest $request): AnonymousResourceCollection
    {
        $max = $request->num_of_companies ?? 12;
        $result = Company::whereHas('companyClaimers')
            ->whereHas('enumType', function ($q) {
                $q->whereIn('value', ['VENDOR_ALL']);
            })
            ->whereHas('enumType', function ($q) {
                $q->where('type_is_of_vendor', true);
            })
            ->whereHas('companyProfileType', function ($q) {
                $q->whereNot('value', CompanyProfileTypes::VendorFree);
            })
            ->whereHas('avatar')
            ->inRandomOrder()
            ->limit($max)->get();
        ImageService::appendAvatars($result);

        return CompanyHomePageAvatarsResource::collection($result->flatten()->unique('id'));
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/sidebar/{company}",
     *     operationId="company/showSideBarUsingFriendlyUrl",
     *     tags={"CompanyController"},
     *     summary="Get company sidebar using friendly_url",
     *     description="Returns the company sidebar using friendly_url",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *
     *         @OA\JsonContent(ref="#/components/schemas/CompanyResource")
     *     ),
     *
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/sidebar/{company}
    // NO Bearer token needed
    public function showSideBarUsingFriendlyUrl(Company $company): CompanyResourceForSideBar
    {
        $company->load('enumType', 'companyProfileType', 'parentCompany', 'country');
        $company->is_affiliate_brand_main_company = $company->isAffiliateBrandMainCompany();
        if ($company->parent_id) {
            ImageService::appendCompanyAvatars(collect([$company->parentCompany]));
        }

        $selectColumns = ['key', 'order'];

        $favoriteAdminNavigations = NavigationFavorites::select($selectColumns)
            ->where('user_id', AuthService::getLoggedInUserId())
            ->where('section', NavigationFavoritesSection::ADMIN)
            ->get()->toArray();

        $favoriteUserNavigations = NavigationFavorites::select($selectColumns)
            ->where('user_id', AuthService::getLoggedInUserId())
            ->where('section', NavigationFavoritesSection::USER)
            ->where('company_id', $company->id)
            ->get()->toArray();

        $company->favorite_navigation = [
            'admin' => $favoriteAdminNavigations,
            'user' => $favoriteUserNavigations,
        ];

        return new CompanyResourceForSideBar($company);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company{company}/products",
     *     operationId="company/showAllProducts",
     *     tags={"CompanyController"},
     *     summary="Get company produdcts",
     *     description="Returns the company products",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{company}/products
    // Bearer token needed
    public function showAllProducts(Company $company, CompanyShowAllProductsRequest $request)
    {
        $includeProductContracts = $request->has('include_product_contracts') && $request->include_product_contracts;

        $isMspClient = $company->enumType->value === CompanyTypeEnum::MSP_CLIENT;
        if ($isMspClient) {
            $query = CustomerStack::select('customer_stack.product_id',
                'products.name as product_name',
                DB::raw('null as vendor_id'),
                DB::raw('null as vendor_name'),
            )
                ->join('client_products as products', 'products.id', 'customer_stack.product_id')
                ->groupBy('products.id', 'product_id', 'products.id')->distinct()
                ->when($request->has('search_word'), function ($q) use ($request) {
                    $searchWord = '%' . $request->input('search_word') . '%';
                    $q->where('products.name', 'ilike', $searchWord);
                })
                ->where('customer_stack.company_id', $company->id);

            $queryMyContracts = Contract::select('contracts.client_product_id as product_id',
                'products.name as product_name',
                'vendor_companies.id as vendor_id',
                'vendor_companies.name as vendor_name',
            )->join('client_products as products', 'products.id', 'contracts.client_product_id')
                ->leftJoin('companies as vendor_companies', 'vendor_companies.id', 'contracts.client_vendor_id')
                ->where('contracts.owner_id', $company->id)
                ->groupBy('products.id', 'product_id', 'products.id', 'vendor_companies.id', 'contracts.client_product_id')->distinct()
                ->when($request->has('search_word'), function ($q) use ($request) {
                    $searchWord = '%' . $request->input('search_word') . '%';
                    $q->where('products.name', 'ilike', $searchWord);
                });

            if ($includeProductContracts) {
                $query->union($queryMyContracts);
            }
        } else {
            $query = MyStack::select('my_stack.product_id',
                'products.name as product_name',
                'vendor_companies.id as vendor_id',
                'vendor_companies.name as vendor_name',
            )
                ->join('products', 'products.id', 'my_stack.product_id')
                ->join('companies as vendor_companies', 'vendor_companies.id', 'products.company_id')
                ->where('my_stack.company_id', $company->id)
                ->groupBy('products.id', 'product_id', 'products.id', 'vendor_companies.id')->distinct()
                ->when($request->has('search_word'), function ($q) use ($request) {
                    $searchWord = '%' . $request->input('search_word') . '%';
                    $q->where('products.name', 'ilike', $searchWord);
                });

            $queryMyContracts = Contract::select('contracts.product_id',
                'products.name as product_name',
                'vendor_companies.id as vendor_id',
                'vendor_companies.name as vendor_name',
            )
                ->join('products', 'products.id', 'contracts.product_id')
                ->join('companies as vendor_companies', 'vendor_companies.id', 'products.company_id')
                ->groupBy('products.id', 'product_id', 'products.id', 'vendor_companies.id')->distinct()
                ->where('contracts.owner_id', $company->id)
                ->when($request->has('search_word'), function ($q) use ($request) {
                    $searchWord = '%' . $request->input('search_word') . '%';
                    $q->where('products.name', 'ilike', $searchWord);
                });

            if ($includeProductContracts) {
                $query->union($queryMyContracts);
            }
        }

        if ($request->has('paged') && $request->paged) {
            $result = $query->paginate($request->items_per_page ?? config('common.searchPagingLength'));
        } else {
            $result = $query->get();
        }

        return CompanyProductResource::collection($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/{company}/vendor-or-product/store",
     *     operationId="company/{company}/vendor-or-product/store",
     *     tags={"CompanyController"},
     *     summary="Store a company or a product for a company",
     *     description="Store a company or a product for a company",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/{company}/vendor-or-product/store
    // needs Bearer Token
    public function storeVendorOrProduct(
        StoreVendorOrProductRequest $request,
        Company $company
    ): CompanyResource {
        $response = Gate::inspect('add-company-product', $company);
        if (!$response->allowed()) {
            abort(403, $response->message());
        }
        $loggedUser = AuthService::getAuthUser();
        $vendor = CompanyService::createCompanyOrProduct(
            $company,
            $loggedUser,
            $request->validated()
        );

        return new CompanyResource($vendor);
    }
}
