<?php

namespace App\Http\Controllers\Api\Company;

use App\Enums\Company\CompanyProfileTypes;
use App\Facades\Helpers\UtilityHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\CompanyDashboard\CategoryDetailCountRequest;
use App\Http\Requests\CompanyDashboard\ContractTimelineRequest;
use App\Http\Requests\CompanyDashboard\ExportStackBreakdownRequest;
use App\Http\Requests\CompanyDashboard\StackBreakdownRequest;
use App\Http\Resources\Company\Dashboard\StackBreakdownCategoryResource;
use App\Models\Company\Company;
use App\Models\MyStack\MyStack;
use App\Services\AuthService;
use App\Services\Company\CompanyService;
use App\Services\Contract\ContractService;
use App\Services\Plaid\PlaidExpensesService;
use App\Services\Plaid\PlaidService;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\StreamedResponse;

class CompanyDashboardController extends Controller
{
    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{company}/dashboard/feature-count",
     *     operationId="company/{company}/dashboard/feature-count",
     *     tags={"CompanyDashboardController"},
     *     summary="Get company dashboard feature counts",
     *     description="Get company dashboard feature counts",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{company}/dashboard/feature-counts
    // OPTIONAL PARAMS
    // needs Bearer Token
    public function featureCounts(Company $company): array
    {
        $this->validateIfUserBelongToCompany($company);

        $contracts = ContractService::loadContracts(
            $company->enumType->value,
            $company->id,
            now()->subYears(10),
            now()->addYears(10),
        );

        return [
            'accounts_synced' => $company->plaidBankAcounts()->count(),
            'contracts' => $contracts->count(),
            'customers' => $company->clients()->count(),
            'navi_stacks' => $company->myStack()->count(),
        ];
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{company}/dashboard/quick-overview",
     *     operationId="company/{company}/dashboard/quick-overview",
     *     tags={"CompanyDashboardController"},
     *     summary="Get company dashboard quick overview data",
     *     description="Get company dashboard quick overview data",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{company}/dashboard/quick-overview
    // OPTIONAL PARAMS
    // needs Bearer Token
    public function quickOverviewData(Company $company): array
    {
        $this->validateIfUserBelongToCompany($company);

        $currentMonth = Carbon::now();
        $prevMonth = Carbon::now()->subMonth();

        $currentMonthStart = $currentMonth->copy()->startOfMonth();
        $currentMonthEnd = $currentMonth->copy()->endOfMonth();

        $prevMonthStart = $prevMonth->copy()->startOfMonth();
        $prevMonthEnd = $prevMonth->copy()->endOfMonth();

        $contractCurrentTotal = $company->contracts()
            ->join('contract_next_payment_dates', 'contract_next_payment_dates.contract_id', '=', 'contracts.id')
            ->whereBetween('contract_next_payment_dates.date', [$currentMonthStart, $currentMonthEnd])
            ->sum('contract_next_payment_dates.cost');

        $contractPrevTotal = $company->contracts()
            ->join('contract_next_payment_dates', 'contract_next_payment_dates.contract_id', '=', 'contracts.id')
            ->whereBetween('contract_next_payment_dates.date', [$prevMonthStart, $prevMonthEnd])
            ->sum('contract_next_payment_dates.cost');

        $currentExpenseAmount = PlaidExpensesService::getAllExpenses($company, [$currentMonthStart, $currentMonthEnd])
            ->sum('amount');

        $previousExpenseAmount = PlaidExpensesService::getAllExpenses($company, [$prevMonthStart, $prevMonthEnd])
            ->sum('amount');

        $upComingExpenses = PlaidExpensesService::getUpcomingExpenses($company, [$currentMonthStart, $currentMonthEnd]);
        $upcomingDate = PlaidExpensesService::getUpcomingExpensesDate($company);
        $upcomingDaysDiff = ceil(Carbon::now()->diffInDays(Carbon::parse($upcomingDate)));
        $contracts = $company->contracts()
            ->where(function ($query) use ($currentMonthEnd) {
                $query->whereBetween('end_date', [Carbon::now(), $currentMonthEnd])
                    ->orWhereBetween('notice_date', [Carbon::now(), $currentMonthEnd]);
            })
            ->get();

        $nextPaymentContractsCount = $company->contracts()
            ->join('contract_next_payment_dates', 'contract_next_payment_dates.contract_id', '=', 'contracts.id')
            ->whereBetween('contract_next_payment_dates.date', [Carbon::now(), $currentMonthEnd])
            ->count();

        return [
            'upcoming_renewal' => $contracts->count() + $nextPaymentContractsCount,
            'customers' => $company->clients()->count(),
            'upcoming_expenses' => [
                'amount' => $upComingExpenses->sum('last_amount'),
                'days' => $upcomingDaysDiff,
            ],
            'expenses' => [
                'amount' => $currentExpenseAmount,
                'percentage_diff' => PlaidExpensesService::getPercentageDiff($previousExpenseAmount, $currentExpenseAmount),
            ],
            'contract_costs' => [
                'amount' => $contractCurrentTotal,
                'percentage_diff' => PlaidExpensesService::getPercentageDiff($contractPrevTotal, $contractCurrentTotal),
            ],
            'customers_and_contracts' => [
                'customers' => $company->contracts()->distinct('contracts.company_id')->count(),
                'contracts' => $company->contracts()->count(),
            ],
            'connected_portals' => $company->partnerIFollow()->where('company_partners.status', 'accepted')->count(),
            'navi_stack' => [
                'products' => $company->myStack()->count(),
                'vendors' => $company->myStack()->distinct('companies.id')->count(),
            ],
        ];
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/plaid/{company}/dashboard/stack-breakdown",
     *     operationId="plaid/company/dashboard/stack-breakdown",
     *     tags={"CompanyDashboardController"},
     *     summary="Get stack breakdown info",
     *     description="Get stack categories breakdown with contract info",
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{company}/dashboard/stack-breakdown
    // BODY
    /*{}
    / Bearer Token NEEDED*/
    public function stackBreakdown(Company $company, StackBreakdownRequest $request)
    {
        $this->validateIfUserBelongToCompany($company);

        if (in_array($company->companyProfileType->value,
            [CompanyProfileTypes::MSPBusinessBasic, CompanyProfileTypes::DirectBasic], true)) {
            return $this->stackBreakdownOnlyCategories($company, $request);
        }

        return $this->stackBreakdownExpenses($company, $request);
    }

    private function stackBreakdownOnlyCategories($company, $request)
    {
        $this->validateIfUserBelongToCompany($company);

        $getCategories = function ($selectColumn) use ($company) {
            return MyStack::select([
                $selectColumn . '.name',
                $selectColumn . '.color',
                $selectColumn . '.id',
                \DB::raw('COUNT(my_stack.product_id) as product_count'),
            ])
                ->join('categories', 'categories.id', '=', 'my_stack.category_id')
                ->join('categories as parent_categories', 'parent_categories.id', '=', 'categories.parent_id')
                ->where('my_stack.company_id', $company->id)
                ->groupBy($selectColumn . '.name', $selectColumn . '.color', $selectColumn . '.id');
        };

        $stackBreakDownCategories = $getCategories('parent_categories')->get();

        $sortCollection = function ($collection) use ($request) {
            $descendingSort = $request->sort === 'DESC';
            $orderBy = $request->order_by;

            if (!in_array($orderBy, ['name', 'product_count'])) {
                return $collection;
            }

            return $collection->sortBy(fn ($model) => $model->{$orderBy}, SORT_REGULAR, $descendingSort)->values();
        };

        $totalProductCount = $stackBreakDownCategories->sum('product_count');

        // Add the total product count to each category for percentage calculation in the resource
        $stackBreakDownCategories->each(function ($category) use ($totalProductCount, $getCategories, $sortCollection) {
            $category->totalProductCount = $totalProductCount;
            $category->sub_categories = $sortCollection(
                $getCategories('categories')
                    ->where('categories.parent_id', $category->id)
                    ->get()
            );
        });

        $sortedCategories = $sortCollection($stackBreakDownCategories);

        // Return the collection as a resource
        return StackBreakdownCategoryResource::collection($sortedCategories);
    }

    private function stackBreakdownExpenses($company, $request)
    {
        $this->validateIfUserBelongToCompany($company);

        PlaidService::validateIfUserBelongToCompany($company);

        $dateRange = [$request->start_date, $request->end_date];

        $getCategories = function ($categoryColumn) use ($dateRange, $company) {
            $contractCostsSubquery = \DB::table('contract_next_payment_dates')
                ->select('contract_id', \DB::raw('SUM(cost) as total_cost'))
                ->whereBetween('date', $dateRange)
                ->groupBy('contract_id');

            return $company->plaidTransactions()
                ->select([
                    'categories.name',
                    'categories.color',
                    'categories.id',
                    \DB::raw('ROUND(SUM(plaid_transactions.amount), 2) as amount'),
                    \DB::raw('COUNT(DISTINCT plaid_transactions.product_id) as product_count'),
                    \DB::raw('ROUND(COALESCE(SUM(contract_costs.total_cost), 0), 2) as contract_costs'),
                ])
                ->stackOnly()
                ->notIgnored()
                ->join('categories', 'categories.id', '=', $categoryColumn)
                ->leftJoin('contracts', 'contracts.id', '=', 'plaid_transactions.contract_id')
                ->leftJoinSub($contractCostsSubquery, 'contract_costs', function ($join) {
                    $join->on('contracts.id', '=', 'contract_costs.contract_id');
                })
                ->whereBetween('plaid_transactions.date', $dateRange)
                ->groupBy('categories.name', 'categories.color', 'categories.id');
        };

        $stackBreakDownCategories = $getCategories('plaid_transactions.category_id')->get();

        $sortCollection = function ($collection) use ($request) {
            $descendingSort = $request->sort === 'DESC';
            $orderBy = $request->order_by;

            if (!in_array($orderBy, ['name', 'product_count', 'contract_cost', 'amount'])) {
                return $collection;
            }

            return $collection->sortBy(fn ($model) => $model->{$orderBy}, SORT_REGULAR, $descendingSort)->values();
        };

        $totalAmount = $stackBreakDownCategories->sum('amount');

        $stackBreakDownCategories->transform(function ($model) use (
            $getCategories,
            $totalAmount,
            $sortCollection
        ) {
            $model->color = $model->color ?? '#000000';
            $model->percentage = (string)round(($model->amount / $totalAmount) * 100, 2);
            $model->sub_categories = $sortCollection(
                $getCategories('plaid_transactions.sub_category_id')
                    ->where('plaid_transactions.category_id', $model->id)
                    ->get()
            );

            return $model;
        });

        return $sortCollection($stackBreakDownCategories);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{company}/dashboard/contract-timeline",
     *     operationId="company/{company}/dashboard/contract-timeline",
     *     tags={"CompanyDashboardController"},
     *     summary="Get contract timeline information",
     *     description="Get contract payment dates, notice periods, and expiration dates for the current month",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{company}/dashboard/contract-timeline
    // BODY
    /*{}
    / Bearer Token NEEDED*/
    public function contractTimeline(Company $company, ContractTimelineRequest $request): array
    {
        $this->validateIfUserBelongToCompany($company);

        PlaidService::validateIfUserBelongToCompany($company);
        $data = $request->validated();
        $startDate = Carbon::now();
        $endDate = Carbon::createFromFormat('m-Y', $data['month'] . '-' . $data['year'])?->endOfMonth();

        $handleCommonGroupFields = function ($item) {
            return [
                'contract_id' => '' . $item->contract_id,
                'contract_name' => $item->contract_name,
                'company_name' => $item->company->name,
                'company_avatar' => $item->company->avatar_source,
                'product_name' => $item->product_name ?? $item->client_product_name ?? null,
            ];
        };
        // Get payment dates for current month
        $paymentDates = $company->contracts()
            ->join('contract_next_payment_dates', 'contract_next_payment_dates.contract_id', '=', 'contracts.id')
            ->leftJoin('products', 'products.id', '=', 'contracts.product_id')
            ->leftJoin('client_products', 'client_products.id', '=', 'contracts.client_product_id')
            ->whereBetween('contract_next_payment_dates.date', [$startDate, $endDate])
            ->select(
                'contracts.id as contract_id',
                'contracts.name as contract_name',
                'products.name as product_name',
                'client_products.name as client_product_name',
                'contract_next_payment_dates.date',
                'contract_next_payment_dates.cost',
                'contracts.company_id'
            )
            ->orderBy('contract_next_payment_dates.date')
            ->get()
            ->map(function ($item) use ($handleCommonGroupFields) {
                return array_merge($handleCommonGroupFields($item), [
                    'date' => $item->date,
                    'cost' => $item->cost,
                ]);
            });

        // Get notice periods for current month
        $noticePeriods = $company->contracts()
            ->leftJoin('products', 'products.id', '=', 'contracts.product_id')
            ->leftJoin('client_products', 'client_products.id', '=', 'contracts.client_product_id')
            ->whereBetween('notice_date', [$startDate, $endDate])
            ->select(
                'contracts.id as contract_id',
                'contracts.name as contract_name',
                'products.name as product_name',
                'client_products.name as client_product_name',
                'contracts.notice_date',
                'contracts.company_id'
            )
            ->orderBy('contracts.notice_date')
            ->get()
            ->map(function ($item) use ($handleCommonGroupFields) {
                return array_merge($handleCommonGroupFields($item), [
                    'notice_date' => $item->notice_date,
                ]);
            });

        $expirationDates = $company->contracts()
            ->leftJoin('products', 'products.id', '=', 'contracts.product_id')
            ->leftJoin('client_products', 'client_products.id', '=', 'contracts.client_product_id')
            ->whereBetween('end_date', [$startDate, $endDate])
            ->select(
                'contracts.id as contract_id',
                'contracts.name as contract_name',
                'products.name as product_name',
                'client_products.name as client_product_name',
                'contracts.end_date',
                'contracts.company_id'
            )
            ->with('company:id,name,friendly_url')
            ->orderBy('contracts.end_date')
            ->get()
            ->map(function ($item) use ($handleCommonGroupFields) {
                return array_merge($handleCommonGroupFields($item), [
                    'end_date' => $item->end_date,
                ]);
            });

        // Group all events by date
        $groupedByDate = [];
        $handleGroupedDate = function ($item, $date, $key) use (&$groupedByDate) {
            if (!isset($groupedByDate[$date])) {
                $groupedByDate[$date] = [
                    'date' => $date,
                    'payment_dates' => [],
                    'notice_periods' => [],
                    'expiration_dates' => [],
                ];
            }
            $groupedByDate[$date][$key][] = $item;
        };

        // Process payment dates
        foreach ($paymentDates as $payment) {
            $date = Carbon::parse($payment['date'])->toISOString();
            $handleGroupedDate($payment, $date, 'payment_dates');
        }

        // Process notice periods
        foreach ($noticePeriods as $notice) {
            $date = Carbon::parse($notice['notice_date'])->toISOString();
            $handleGroupedDate($notice, $date, 'notice_periods');
        }

        // Process expiration dates
        foreach ($expirationDates as $expiration) {
            $date = Carbon::parse($expiration['end_date'])->toISOString();
            $handleGroupedDate($expiration, $date, 'expiration_dates');
        }

        // Sort by date and convert to array
        ksort($groupedByDate);

        return array_values($groupedByDate);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{company}/dashboard/export-stack-breakdown",
     *     operationId="company/{company}/dashboard/export-stack-breakdown",
     *     tags={"CompanyDashboardController"},
     *     summary="Export stack breakdown information",
     *     description="Export stack breakdown information to CSV",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{company}/dashboard/export-stack-breakdown
    // BODY
    /*{}
    / Bearer Token NEEDED*/
    public function exportStackBreakdown(Company $company, ExportStackBreakdownRequest $request): StreamedResponse
    {
        $this->validateIfUserBelongToCompany($company);

        $validated = $request->validated();

        $data = [];
        $stackBreakdownsExpenses = $company->plaidTransactions()
            ->select('date', 'merchant_name', 'amount', 'description', 'category_id', 'sub_category_id', 'product_id', 'productable_type', 'contract_id')
            ->notIgnored()
            ->stackOnly()
            ->where('plaid_transactions.date', '>=' , now()->subDays($validated['days'])->startOfDay())
            ->orderByDesc('date')
            ->with(['category:id,name', 'subCategory:id,name', 'product:id,name', 'contract:id,name,cost'])
            ->get();

        $columns = [
            'Date', 'Transaction Name', 'Description', 'Amount', 'Contract Cost', 'Product', 'Contract',
            'Stack Category', 'Stack Sub Category',
        ];

        foreach ($stackBreakdownsExpenses as $expense) {
            $data[] = [
                $expense->date, $expense->merchant_name, $expense->description, $expense->amount, $expense->contract->cost ?? '0',
                $expense->product?->name ?? '',  $expense->contract?->name, $expense->category?->name ?? '', $expense->subCategory?->name ?? '',
            ];
        }

        $fileName = 'stack-breakdown-expenses-' . time() . '.csv';

        return UtilityHelper::getCSVFileResponse($columns, $data, $fileName);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{company}/dashboard/category-detail-count",
     *     operationId="dashboard/category-detail-count",
     *     tags={"CompanyDashboardController"},
     *     summary="Get counts of contracts, products, and expenses by category",
     *     description="Get counts of contracts, products, and expenses by category",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Parameter(
     *         name="category_ids",
     *         in="query",
     *         description="Array of category IDs",
     *         required=false,
     *
     *         @OA\Schema(
     *             type="array",
     *
     *             @OA\Items(type="integer")
     *         )
     *     ),
     *
     *     @OA\Parameter(
     *         name="sub_category_ids",
     *         in="query",
     *         description="Array of sub-category IDs",
     *         required=false,
     *
     *         @OA\Schema(
     *             type="array",
     *
     *             @OA\Items(type="integer")
     *         )
     *     ),
     *
     *     @OA\Parameter(
     *         name="start_date",
     *         in="query",
     *         description="Start date for filtering expenses (YYYY-MM-DD)",
     *         required=false,
     *
     *         @OA\Schema(
     *             type="string",
     *             format="date"
     *         )
     *     ),
     *
     *     @OA\Parameter(
     *         name="end_date",
     *         in="query",
     *         description="End date for filtering expenses (YYYY-MM-DD)",
     *         required=false,
     *
     *         @OA\Schema(
     *             type="string",
     *             format="date"
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{company}/dashboard/category-detail-count
    // ?category[]=1&category[]=2&sub_category[]=3&sub_category[]=4&start_date=2023-01-01&end_date=2023-12-31
    // needs Bearer Token
    public function categoryDetailCount(Company $company, CategoryDetailCountRequest $request): array
    {
        $this->validateIfUserBelongToCompany($company);

        $categoryIds = $request->input('category', []);
        $subCategoryIds = $request->input('sub_category', []);

        $contractCounts = $company->contracts()
            ->join('categories', 'categories.id', '=', 'contracts.category_id')
            ->select('categories.id as category_id', DB::raw('count(*) as count'))
            ->when(!empty($categoryIds), function ($query) use ($categoryIds) {
                $query->whereIn('categories.parent_id', $categoryIds);
            })
            ->when(!empty($subCategoryIds), function ($query) use ($subCategoryIds) {
                $query->whereIn('contracts.category_id', $subCategoryIds);
            })->count();

        $productCounts = $company->myStack()
            ->join('categories', 'categories.id', '=', 'my_stack.category_id')
            ->when(!empty($categoryIds), function ($query) use ($categoryIds) {
                $query->whereIn('categories.parent_id', $categoryIds);
            })
            ->when(!empty($subCategoryIds), function ($query) use ($subCategoryIds) {
                $query->whereIn('my_stack.category_id', $subCategoryIds);
            })->count();

        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        $expenseCounts = $company->plaidTransactions()
            ->when(!empty($categoryIds), function ($query) use ($categoryIds) {
                $query->whereIn('plaid_transactions.category_id', $categoryIds);
            })
            ->when(!empty($subCategoryIds), function ($query) use ($subCategoryIds) {
                $query->whereIn('plaid_transactions.sub_category_id', $subCategoryIds);
            })
            ->when($startDate && $endDate, function ($query) use ($startDate, $endDate) {
                $query->whereBetween('plaid_transactions.date', [$startDate, $endDate]);
            })
            ->notIgnored()
            ->count();

        return [
            'contracts_count' => $contractCounts ?? 0,
            'products_count' => $productCounts ?? 0,
            'expenses_count' => $expenseCounts ?? 0,
        ];
    }

    /**
     * @throws ValidationException
     */
    public static function validateIfUserBelongToCompany(Company $company): void
    {
        $user = AuthService::getAuthUser();
        $companyAccessible = CompanyService::companyIsAccessible($user, app('asCompanyId'), $company->id);
        $userBelongToCompany = CompanyService::loggedUserBelongToCompany($company);
        if (!$companyAccessible && !$userBelongToCompany) {
            throw ValidationException::withMessages([
                config('genericMessages.error.ROLE_NOT_BELONGS_TO_COMPANY'),
            ]);
        }
    }
}
