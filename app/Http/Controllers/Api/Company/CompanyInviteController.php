<?php

namespace App\Http\Controllers\Api\Company;

use App\Enums\Company\CompanyInviteTypeEnum;
use App\Enums\Company\CompanyType;
use App\Enums\Company\CompanyTypeCast;
use App\Enums\CSV\CSVColumnsEnum;
use App\Helpers\CSVHelper;
use App\Helpers\UtilityHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Company\Affiliate\CompanyAffiliateResendBulkInviteRequest;
use App\Http\Requests\Company\Invite\CompanyDeleteInviteRequest;
use App\Http\Requests\Company\Invite\CompanyInviteClientRequest;
use App\Http\Requests\Company\Invite\CompanyInviteMultipleRolesCSVRequest;
use App\Http\Requests\Company\Invite\CompanyInviteMultipleRolesRequest;
use App\Http\Requests\Company\Invite\CompanyInviteRequest;
use App\Http\Requests\Company\Invite\CompanyInviteValidationRequest;
use App\Http\Requests\Company\Invite\CompanyShowAllInviteRequest;
use App\Http\Resources\Company\Client\CompanyInvitePendingResource;
use App\Http\Resources\Company\Client\CompanyInviteResendResource;
use App\Http\Resources\Company\Client\CompanyInviteResource;
use App\Http\Resources\Company\Client\CompanyOpenInviteResource;
use App\Http\Resources\Company\Client\showAllPendingCustomerFiltersResource;
use App\Http\Resources\Company\Client\showAllPendingUsersFiltersResource;
use App\Http\Resources\Company\Invite\ProcessedInviteResource;
use App\Jobs\Company\SendInviteAffiliateEmails;
use App\Jobs\Company\SendInviteClientEmails;
use App\Jobs\Company\SendInviteMspCustomerUserEmails;
use App\Jobs\Company\SendInviteMspUserEmails;
use App\Jobs\Company\SendInviteVendorUserEmails;
use App\Models\Company\Company;
use App\Models\Company\CompanyInvite;
use App\Models\User;
use App\Services\AppConfig;
use App\Services\AuthService;
use App\Services\Company\CompanyClientService;
use App\Services\Company\CompanyService;
use App\Services\Permission\RoleUserService;
use App\Services\ValidationService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Throwable;

class CompanyInviteController extends Controller
{
    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{friendly_url}/invite",
     *     operationId="company/showAllInvites",
     *     tags={"CompanyInviteController"},
     *     summary="Get all company invites",
     *     description="Returns the company invites",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/invite
    // Bearer token needed
    public function showAllInvites(CompanyShowAllInviteRequest $request, Company $company): AnonymousResourceCollection
    {
        $query = $company->invites($request->status, $request->company_type)
            ->select('company_invites.id', 'company_invites.email', 'company_invites.created_at',
                'company_invites.role_id', 'company_invites.last_sent_at', 'company_invites.updated_at')
            ->with('role:id,key,title,display_name')
            ->when($request->has('search_word'), function ($query) use ($request) {
                $query->whereRaw("(lower(email) like '%" . strtolower($request->search_word) . "%')");
            })
            ->when($request->has('roles'), function ($query) use ($request) {
                $roles = is_array($request->roles) ? $request->roles : [$request->roles];

                $query->whereHas('role', function ($q) use ($roles) {
                    $q->whereIn('display_name', $roles);
                });
            })
            ->when($request->has('start_date') && $request->has('end_date'), function ($query) use ($request) {
                $startDate = Carbon::parse($request->start_date)->startOfDay();
                $endDate = Carbon::parse($request->end_date)->endOfDay();
                $query->where(function ($q) use ($startDate, $endDate) {
                    $q->where(function ($q1) use ($startDate, $endDate) {
                        $q1->whereNotNull('updated_at')
                            ->whereBetween('updated_at', [$startDate, $endDate]);
                    })->orWhere(function ($q2) use ($startDate, $endDate) {
                        $q2->whereNull('updated_at')
                            ->whereBetween('created_at', [$startDate, $endDate]);
                    });
                });
            });
        $result = UtilityHelper::getSearchRequestQueryResults($request, $query, 0, 'company_invites');

        return CompanyInviteResource::collection($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{friendly_url}/invite/showPendingCustomersInvites",
     *     operationId="company/showPendingCustomersInvites",
     *     tags={"CompanyInviteController"},
     *     summary="Returns the company pending customer invites",
     *     description="Returns the company pending customer invites",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/invite/showPendingCustomersInvites
    // Bearer token needed
    public function showPendingCustomersInvites(Company $company): JsonResource|JsonResponse
    {
        try {
            $perPage = request()->get('items_per_page', 10);
            $roles = request()->get('roles');
            $customer = request()->get('customer');
            $startDate = request()->get('start_date');
            $endDate = request()->get('end_date');
            $searchWord = request()->get('search_word');

            $sortBy = request()->get('order_by', 'created_at');
            $sortDir = request()->get('sort', 'desc');

            $query = CompanyInvite::select('id', 'email', 'created_at', 'last_sent_at', 'type', 'child_company_id', 'role_id')
                ->with('childCompany:id,name')
                ->with('role:id,key,title,display_name')
                ->where([
                    ['parent_company_id', '=', $company->id],
                    ['activated', '=', false],
                    ['type', '=', CompanyType::MSP_CLIENT],
                ])
                ->when(request()->get('search_word'), function ($query) use ($searchWord) {
                    $query->whereRaw("(lower(email) like '%" . strtolower($searchWord) . "%')");
                })
                ->when(request()->get('start_date') && request()->get('end_date'), function ($query) use ($startDate, $endDate) {
                    $startDate = Carbon::parse(request()->get('start_date'))->startOfDay();
                    $endDate = Carbon::parse(request()->get('end_date'))->endOfDay();
                    $query->where(function ($q) use ($startDate, $endDate) {
                        $q->where(function ($q1) use ($startDate, $endDate) {
                            $q1->whereNotNull('updated_at')
                                ->whereBetween('updated_at', [$startDate, $endDate]);
                        })->orWhere(function ($q2) use ($startDate, $endDate) {
                            $q2->whereNull('updated_at')
                                ->whereBetween('created_at', [$startDate, $endDate]);
                        });
                    });
                });

            if (!empty($roles)) {
                $roles = is_array($roles) ? $roles : [$roles];

                $query->whereHas('role', function ($q) use ($roles) {
                    $q->whereIn('display_name', $roles);
                });
            }

            if (!empty($customer)) {
                $customer = is_array($customer) ? $customer : [$customer];

                $query->whereHas('childCompany', function ($q) use ($customer) {
                    $q->whereIn('name', $customer);
                });
            }

            if (in_array($sortBy, ['email', 'child_company_id', 'last_sent_at'])) {
                $query->orderBy($sortBy, $sortDir);
            } elseif ($sortBy === 'role') {
                $query->join('roles', 'roles.id', '=', 'company_invites.role_id')
                    ->orderBy('roles.display_name', $sortDir)
                    ->select('company_invites.*');
            }

            $result = $query->paginate($perPage);

            return CompanyInvitePendingResource::collection($result);
        } catch (\Throwable $th) {
            return response()->json(['message' => config('genericMessages.error.INVITE_NOT_SENT') . $th->getMessage()], 500);
        }
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{friendly_url}/invite/showAllPendingCustomerFilters",
     *     operationId="company/showAllPendingCustomerFilters",
     *     tags={"CompanyInviteController"},
     *     summary="Returns the company pending customer invites filters",
     *     description="Returns the company pending customer invites filters",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/invite/showAllPendingCustomerFilters
    // Bearer token needed
    public function showAllPendingCustomerFilters(Company $company)
    {
        $pendingInvites = CompanyInvite::select('id', 'email', 'created_at', 'last_sent_at', 'type', 'child_company_id', 'role_id')
            ->with('childCompany:id,name')
            ->with('role:id,key,title,display_name')
            ->where([
                ['parent_company_id', '=', $company->id],
                ['activated', '=', false],
                ['type', '=', CompanyType::MSP_CLIENT],
            ])->get();

        if ($pendingInvites->count() > 0) {
            $childCompanyItems = [];
            $roleItems = [];

            $addedChildCompanyIds = [];
            $addedRoleIds = [];

            foreach ($pendingInvites as $invite) {
                if (isset($invite['childCompany'])) {
                    $id = (string)$invite['childCompany']['id'];
                    if (!in_array($id, $addedChildCompanyIds, true)) {
                        $childCompanyItems[] = [
                            'id' => (string)$invite['childCompany']['name'],
                            'name' => $invite['childCompany']['name'],
                        ];
                        $addedChildCompanyIds[] = $id;
                    }
                }

                if (isset($invite['role'])) {
                    $id = (string)$invite['role']['display_name'];
                    if (!in_array($id, $addedRoleIds, true)) {
                        $addedRoleIds[] = $id;
                        $roleItems[] = [
                            'id' => (string)$invite['role']['display_name'],
                            'name' => $invite['role']['display_name'],
                        ];
                    }
                }
            }
        }

        return new showAllPendingCustomerFiltersResource([
            'typeItems' => $childCompanyItems,
            'roleItems' => $roleItems,
        ]);

        return response()->json($response);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{friendly_url}/invite/showAllPendingUsersFilters",
     *     operationId="company/showAllPendingUsersFilters",
     *     tags={"CompanyInviteController"},
     *     summary="Returns the company pending users invites filters",
     *     description="Returns the company pending users invites filters",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/invite/showAllPendingUsersFilters
    // Bearer token needed
    public function showAllPendingUsersFilters(Company $company)
    {
        $pendingInvites = CompanyInvite::select('id', 'type', 'role_id')
            ->with('role:id,key,title,display_name')
            ->where([
                ['parent_company_id', '=', $company->id],
                ['activated', '=', false],
            ])->get();

        if ($pendingInvites->count() > 0) {
            $typeItems = [];
            $roleItems = [];
            $addedChildCompanyIds = [];
            $addedRoleIds = [];

            foreach ($pendingInvites as $invite) {
                if (isset($invite['type'])) {
                    $id = (string)$invite['type'];
                    if (!in_array($id, $addedChildCompanyIds, true)) {
                        $typeItems[] = [
                            'id' => (string)$invite['type'],
                            'name' => CompanyTypeCast::COMPANY_TYPE_CAST_ARRAY[$invite->type],
                        ];
                        $addedChildCompanyIds[] = $id;
                    }
                }

                if (isset($invite['role'])) {
                    $id = (string)$invite['role']['display_name'];
                    if (!in_array($id, $addedRoleIds, true)) {
                        $addedRoleIds[] = $id;
                        $roleItems[] = [
                            'id' => (string)$invite['role']['display_name'],
                            'name' => $invite['role']['display_name'],
                        ];
                    }
                }
            }
        }

        return new showAllPendingUsersFiltersResource([
            'typeItems' => $typeItems,
            'roleItems' => $roleItems,
        ]);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{friendly_url}/invite/affiliate/resend/{invite}",
     *     operationId="company/resend",
     *     tags={"CompanyInviteController"},
     *     summary="Get all company invites",
     *     description="Resend affiliate invites",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/invite/affiliate/resend/{invite}
    // Bearer token needed
    public function resendInviteAffiliate(Company $company, string $invite): JsonResponse
    {
        $loggedUser = AuthService::getAuthUser();

        $invite = CompanyInvite::find($invite);

        if (!$invite) {
            return response()->json(['message' => 'No pending invite found for this email.'], 404);
        }

        $last_sent = $invite->last_sent_at ? $invite->last_sent_at : $invite->created_at;
        if ($last_sent->diffInMinutes(now()) < AppConfig::loadAppConfigByKey('INVITE_RESEND_MIN_INTERVAL_MINUTES', 120)->value) {
            return response()->json(['message' => 'This action was performed recently. Please try again later.'], 429);
        }

        dispatch(new SendInviteAffiliateEmails([$invite->email], $loggedUser, $company));

        $invite->last_sent_at = now();
        $invite->save();

        return response()->json(['message' => 'Invitation resent successfully.']);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{friendly_url}/invite/resendBulkInviteAffiliate",
     *     operationId="company/resendBulkInviteAffiliate",
     *     tags={"CompanyInviteController"},
     *     summary="Resend bulk invite affiliate",
     *     description="Resend bulk invite affiliate",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/invite/resendBulkInviteAffiliate
    // Bearer token needed
    public function resendBulkInviteAffiliate(Company $company, CompanyAffiliateResendBulkInviteRequest $request): JsonResource
    {
        $loggedUser = AuthService::getAuthUser();
        $successCount = 0;
        $errors = [];

        $invites = $request->input('invites');

        foreach ($invites as $inviteId) {
            $invite = CompanyInvite::find($inviteId);

            if (!$invite) {
                $errors[] = "Invite ID {$inviteId} not found.";

                continue;
            }

            if (in_array($invite->type, [CompanyType::FranchiseMsp, CompanyType::MSP_LOCATION])) {
                $errors[] = "Invite ID {$inviteId} is not a user invite.";

                continue;
            }

            $minInterval = AppConfig::loadAppConfigByKey('INVITE_RESEND_MIN_INTERVAL_MINUTES', 120)->value;
            $last_sent = $invite->last_sent_at ? $invite->last_sent_at : $invite->created_at;
            if ($last_sent->diffInMinutes(now()) < $minInterval) {
                $errors[] = "Invite ID {$inviteId} was sent recently. Try again later.";

                continue;
            }

            dispatch(new SendInviteAffiliateEmails([$invite->email], $loggedUser, $company));

            $invite->last_sent_at = now();
            $invite->save();

            $successCount++;
        }

        return new CompanyInviteResendResource([
            'resent_count' => $successCount,
            'errors' => $errors,
        ]);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{friendly_url}/invite/resendInviteAllAffiliate",
     *     operationId="company/resendInviteAllAffiliate",
     *     tags={"CompanyInviteController"},
     *     summary="Get all company invites",
     *     description="Resend affiliate invites",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/invite/resendInviteAllAffiliate
    // Bearer token needed
    public function resendInviteAllAffiliate(Company $company): JsonResponse
    {
        try {
            $loggedUser = AuthService::getAuthUser();

            $allInvites = $company->invites()
                ->select(
                    'company_invites.id',
                    'company_invites.email',
                    'company_invites.last_sent_at',
                    'company_invites.created_at',
                    'company_invites.type')->get();

            foreach ($allInvites as $invite) {
                $last_sent = $invite->last_sent_at ? $invite->last_sent_at : $invite->created_at;
                if (
                    in_array($invite->type, [CompanyType::FranchiseMsp, CompanyType::MSP_LOCATION]) &&
                    $last_sent->diffInMinutes(now()) > AppConfig::loadAppConfigByKey('INVITE_RESEND_MIN_INTERVAL_MINUTES', 120)->value
                ) {
                    dispatch(new SendInviteAffiliateEmails([$invite->email], $loggedUser, $company));

                    $invite->last_sent_at = now();
                    $invite->save();
                }
            }

            return response()->json(['message' => config('genericMessages.success.INVITE_SENT')], 200);
        } catch (\Throwable $th) {
            return response()->json(['message' => config('genericMessages.error.INVITE_NOT_SENT') . $th->getMessage()], 500);
        }
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{friendly_url}/invite/resendInviteAllUser",
     *     operationId="company/resendInviteAllUser",
     *     tags={"CompanyInviteController"},
     *     summary="Resend all user invites",
     *     description="Resend all user invites",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/invite/resendInviteAllUser
    // Bearer token needed
    public function resendInviteAllUser(Company $company)
    {
        try {
            $loggedUser = AuthService::getAuthUser();

            $allInvites = $company->invites()
                ->select(
                    'company_invites.id',
                    'company_invites.email',
                    'company_invites.last_sent_at',
                    'company_invites.type',
                    'company_invites.created_at',
                    'company_invites.role_id')->get();

            foreach ($allInvites as $invite) {
                $last_sent = $invite->last_sent_at ? $invite->last_sent_at : $invite->created_at;
                if (
                    !in_array($invite->type, [CompanyType::FranchiseMsp, CompanyType::MSP_LOCATION]) &&
                    $last_sent->diffInMinutes(now()) > AppConfig::loadAppConfigByKey('INVITE_RESEND_MIN_INTERVAL_MINUTES', 120)->value
                ) {
                    $emailJob = $this->makeInvitationJob(
                        $company,
                        [$invite->email],
                        $loggedUser,
                        $invite->role_id
                    );

                    dispatch($emailJob);

                    $invite->last_sent_at = now();
                    $invite->save();
                }
            }

            return response()->json(['message' => config('genericMessages.success.INVITE_WERE_SENT')], 200);
        } catch (\Throwable $th) {
            return response()->json(['message' => config('genericMessages.error.INVITE_NOT_SENT') . $th->getMessage()], 500);
        }
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{friendly_url}/invite/resendBulkInviteUser",
     *     operationId="company/resendBulkInviteUser",
     *     tags={"CompanyInviteController"},
     *     summary="Get all company invites",
     *     description="Resend affiliate invites",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/invite/resendBulkInviteUser
    /*{
        'invites':'required|integer|array'
    }
    Bearer token needed*/
    public function resendBulkInviteUser(Company $company, CompanyAffiliateResendBulkInviteRequest $request): JsonResource
    {
        $loggedUser = AuthService::getAuthUser();
        $successCount = 0;
        $errors = [];

        $invites = $request->input('invites');

        foreach ($invites as $inviteId) {
            $invite = CompanyInvite::find($inviteId);

            if (!$invite) {
                $errors[] = "Invite ID {$inviteId} not found.";

                continue;
            }

            if (in_array($invite->type, [CompanyType::FranchiseMsp, CompanyType::MSP_LOCATION])) {
                $errors[] = "Invite ID {$inviteId} is not a user invite.";

                continue;
            }

            $minInterval = AppConfig::loadAppConfigByKey('INVITE_RESEND_MIN_INTERVAL_MINUTES', 120)->value;
            $last_sent = $invite->last_sent_at ? $invite->last_sent_at : $invite->created_at;
            if ($last_sent->diffInMinutes(now()) < $minInterval) {
                $errors[] = "Invite ID {$inviteId} was sent recently. Try again later.";

                continue;
            }

            $emailJob = $this->makeInvitationJob(
                $company,
                [$invite->email],
                $loggedUser,
                $invite->role_id
            );

            dispatch($emailJob);

            $invite->last_sent_at = now();
            $invite->save();

            $successCount++;
        }

        return new CompanyInviteResendResource([
            'resent_count' => $successCount,
            'errors' => $errors,
        ]);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{friendly_url}/invite/resendInviteUser",
     *     operationId="company/resendInviteUser",
     *     tags={"CompanyInviteController"},
     *     summary="Resend invite",
     *     description="Resend invite",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/invite/resendInviteUser
    // Bearer token needed
    public function resendInviteUser(Company $company, string $invite): JsonResponse
    {
        $loggedUser = AuthService::getAuthUser();

        $invite = CompanyInvite::find($invite);

        if (!$invite) {
            return response()->json(['message' => 'No pending invite found for this email.'], 404);
        }

        if (in_array($invite->type, [CompanyType::FranchiseMsp, CompanyType::MSP_LOCATION])) {
            return response()->json(['message' => 'This invite is not user.'], 404);
        }

        $last_sent = $invite->last_sent_at ? $invite->last_sent_at : $invite->created_at;
        if ($last_sent->diffInMinutes(now()) < AppConfig::loadAppConfigByKey('INVITE_RESEND_MIN_INTERVAL_MINUTES', 120)->value) {
            return response()->json(['message' => 'This action was performed recently. Please try again later.'], 429);
        }

        $emailJob = $this->makeInvitationJob(
            $company,
            [$invite->email],
            $loggedUser,
            $invite->role_id
        );

        dispatch($emailJob);

        $invite->last_sent_at = now();
        $invite->save();

        return response()->json(['message' => 'Invitation resent successfully.']);
    }

    protected function makeInvitationJob(Company $company, array $emails, User $loggedUser, int $roleId)
    {
        return match (true) {
            $company->enumType->type_is_of_vendor => new SendInviteVendorUserEmails($emails, $loggedUser, $company, $roleId),
            $company->enumType->value === CompanyType::MSP_CLIENT => new SendInviteMspCustomerUserEmails($emails, $loggedUser, $company, $roleId),
            default => new SendInviteMspUserEmails($emails, $loggedUser, $company, $roleId),
        };
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/{friendly_url}/invite/users",
     *     operationId="company/inviteUsers",
     *     tags={"CompanyInviteController"},
     *     summary="Invite a list of users by email to be users of the corporation",
     *     description="Invite a list of users by email to be users of the corporation",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/invite/users
    // needs Bearer Token
    public function inviteUsers(CompanyInviteRequest $request, Company $company): ProcessedInviteResource
    {
        $loggedUser = AuthService::getAuthUser();
        $processedEmailList = ValidationService::processEmailsList($company, $request->emails,
            true, $company->enumType->value);
        if (count($processedEmailList->ok_emails) > 0) {
            if ($company->enumType->type_is_of_vendor) {
                $invitationEmailJob = new SendInviteVendorUserEmails($processedEmailList->ok_emails, $loggedUser,
                    $company, $request->role_id);
            } else {
                if ($company->enumType->value === CompanyType::MSP_CLIENT) {
                    $invitationEmailJob = new SendInviteMspCustomerUserEmails($processedEmailList->ok_emails, $loggedUser,
                        $company, $request->role_id);
                } else {
                    $invitationEmailJob = new SendInviteMspUserEmails($processedEmailList->ok_emails, $loggedUser,
                        $company, $request->role_id);
                }
            }
            // Send the emails
            dispatch($invitationEmailJob);
        }

        return new ProcessedInviteResource($processedEmailList);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/{friendly_url}/invite/users-multiple-roles/store",
     *     operationId="company/inviteUsersMultipleRoles",
     *     tags={"CompanyInviteController"},
     *     summary="Invite a list of users with different roles by email to be users of the corporation",
     *     description="Invite a list of users with different roles by email to be users of the corporation",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/invite/users-multiple-roles/store
    // needs Bearer Token
    public function inviteUsersMultipleRoles(CompanyInviteMultipleRolesRequest $request, Company $company): ProcessedInviteResource
    {
        $loggedUser = AuthService::getAuthUser();
        $invites = collect($request->invites);
        $emails = $invites->pluck('email')->toArray();
        $processedEmailList = ValidationService::processEmailsList($company, $emails, true, $company->enumType->value);
        $invites->filter(function ($invite) use ($processedEmailList) {
            return in_array($invite['email'], $processedEmailList->ok_emails);
        })->groupBy('role_id')->each(function ($group) use ($company, $loggedUser) {
            $role_id = '';
            $emails = [];
            $group->each(function ($invite) use (&$role_id, &$emails) {
                $role_id = $invite['role_id'];
                $emails[] = $invite['email'];
            });
            if ($company->enumType->type_is_of_vendor) {
                $invitationEmailJob = new SendInviteVendorUserEmails(
                    $emails,
                    $loggedUser,
                    $company,
                    $role_id
                );
            } else {
                if ($company->enumType->value === CompanyType::MSP_CLIENT) {
                    $invitationEmailJob = new SendInviteMspCustomerUserEmails(
                        $emails,
                        $loggedUser,
                        $company,
                        $role_id
                    );
                } else {
                    $invitationEmailJob = new SendInviteMspUserEmails(
                        $emails,
                        $loggedUser,
                        $company,
                        $role_id
                    );
                }
            }
            // Send the emails
            dispatch($invitationEmailJob);
        });

        return new ProcessedInviteResource($processedEmailList);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/{friendly_url}/invite/users-multiple-roles/validate-csv",
     *     operationId="company/validateInvitesCSV",
     *     tags={"CompanyInviteController"},
     *     summary="Import and validate a list of contacts from a CSV file to be imported as users of a company",
     *     description="Import and validate a list of contacts from a CSV file to be imported as users of a company",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/invite/users-multiple-roles/validate-csv
    // needs Bearer Token
    public function validateInvitesCSV(CompanyInviteMultipleRolesCSVRequest $request, Company $company): JsonResponse
    {
        $source = __CLASS__ . '::' . __FUNCTION__ . '::';
        $csvFile = $request->file('csv_import');
        $cacheToken = hash('sha256', $company->id . uniqid());
        $fileName = $cacheToken . '.csv';
        $filePath = storage_path('app/shared/temp/');
        $csvFile->move($filePath, $fileName);

        // Reading data
        try {
            [
                'data' => $data,
                'invalid' => $invalid
            ] = CSVHelper::readCSVFile(
                'temp/',
                $fileName,
                CSVColumnsEnum::importInviteUsersToCompany
            );

            $invites = collect($data);
            $emails = $invites->pluck('Email')->toArray();
            // Validating email addresses
            $processedEmailList = ValidationService::processEmailsList($company, $emails, true, $company->enumType->value);
            // Validating roles
            $companyRoles = $company->roles()
                ->select('id', 'company_id', 'display_name')
                ->get()->transform(function ($item) {
                    $item->display_name = strtolower($item->display_name);

                    return $item;
                });
            $validInvites = [];
            $invalidInvites = [];
            $invites->each(function ($invite, $line) use ($companyRoles, $processedEmailList, &$validInvites, &$invalidInvites) {
                $validEmail = in_array($invite->Email, $processedEmailList->ok_emails);
                $role = $companyRoles->firstWhere('display_name', strtolower($invite->Role));
                if (!$validEmail) {
                    $invalidInvites[] = [
                        'line' => $line,
                        'email' => $invite->Email,
                        'role_name' => $invite->Role,
                        'reason' => $processedEmailList->not_ok_reason,
                    ];
                } else if (!$role) {
                    $invalidInvites[] = [
                        'line' => $line,
                        'email' => $invite->Email,
                        'role_name' => $invite->Role,
                        'reason' => 'Invalid Role',
                    ];
                } else {
                    $existingUser = User::firstWhere('email', $invite->Email);
                    $validInvites[] = [
                        'line' => $line,
                        'email' => $invite->Email,
                        'role_name' => $role->display_name,
                        'role_id' => '' . $role->id,
                        'user_name' => empty($existingUser) ? null : "{$existingUser->first_name} {$existingUser->last_name}",
                        'user_id' => empty($existingUser) ? null : '' . $existingUser->id,
                    ];
                }
            });

            return response()->json([
                'valid' => $validInvites,
                'invalid' => $invalidInvites,
            ]);
        } catch (Throwable $error) {
            Log::error($source . $error->getMessage());

            throw $error;
        } finally {
            $fullFilePath = $filePath . $fileName;
            if (file_exists($fullFilePath)) {
                unlink($fullFilePath);
            }
        }
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/invite/users-multiple-roles/template-csv",
     *     operationId="company/exportTemplateCSV",
     *     tags={"CompanyInviteController"},
     *     summary="Get Template CSV file based on column names",
     *     description="Get Template CSV file based on column names",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/invite/users-multiple-roles/template-csv
    // needs Bearer Token
    public function exportTemplateCSV()
    {
        $columns = CSVColumnsEnum::importInviteUsersToCompany;
        $fileName = 'inviteUsersToCompanyTemplate.csv';

        return UtilityHelper::getCSVFileResponse($columns, [], $fileName);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/{friendly_url}/invite/affiliates",
     *     operationId="company/inviteAffiliates",
     *     tags={"CompanyInviteController"},
     *     summary="Invite a list of users by email to be affiliates of the corporation",
     *     description="Invite a list of users by email to be affiliates of the corporation",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/invite/affiliates
    // needs Bearer Token
    public function inviteAffiliates(CompanyInviteRequest $request, Company $company): ProcessedInviteResource
    {
        $loggedUser = AuthService::getAuthUser();
        $companyTypeValue = $company->enumType->value === CompanyType::FRANCHISE_CORPORATE_MSP
            ? CompanyType::FranchiseMsp
            : CompanyType::MSP_LOCATION;
        $processedEmailList = ValidationService::processEmailsList($company, $request->emails,
            false, $companyTypeValue);
        if (count($processedEmailList->ok_emails) > 0) {
            dispatch(new SendInviteAffiliateEmails($processedEmailList->ok_emails, $loggedUser, $company));
        }

        return new ProcessedInviteResource($processedEmailList);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/{friendly_url}/invite",
     *     operationId="company/invite",
     *     tags={"CompanyInviteController"},
     *     summary="Invite a list of users by email to be company claimer",
     *     description="Invite a list of users by email to be company claimer",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/invite
    // needs Bearer Token
    public function inviteClients(CompanyInviteClientRequest $request, Company $company): ProcessedInviteResource
    {
        $loggedUser = AuthService::getAuthUser();
        $client = Company::find($request->id);
        $processedEmailList = ValidationService::processEmailsList($client, $request->emails,
            true, $client->enumType->value);
        if (count($processedEmailList->ok_emails) > 0) {
            dispatch(new SendInviteClientEmails($processedEmailList->ok_emails, $loggedUser, $company, $client,
                $request->role_id));
        }

        return new ProcessedInviteResource($processedEmailList);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/{friendly_url}/invite/open-invitation",
     *     operationId="company/getOpenInvitation",
     *     tags={"CompanyInviteController"},
     *     summary="Return the open invitation url",
     *     description="Return the open invitation url",
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/invite/open-invitation
    // NO Bearer Token needed
    public function getOpenInvitation(Company $company): JsonResponse
    {
        CompanyService::validateCompanyIsMSP($company);
        CompanyService::validateCompanyCanManageClients($company);

        $signature = CompanyClientService::encodeInviteSignature($company->id, CompanyInviteTypeEnum::open);
        $url = CompanyClientService::prepareMSPClientFEUrl($company->friendly_url, $signature);

        return response()->json([
            'company_id' => $company->id,
            'url' => $url,
        ]);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/validate-invitation",
     *     operationId="company/validateInvitation",
     *     tags={"CompanyInviteController"},
     *     summary="Validates the signature for an invitation",
     *     description="Validates the signature for an invitation",
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/validate-invitation
    // NO Bearer Token needed
    public function validateInvitation(CompanyInviteValidationRequest $request): JsonResource
    {
        $parts = CompanyClientService::decodeInviteSignature($request->signature);
        if (isset($parts->email, $parts->child_company_id)) {
            // Individual invitation
            $companyInvite = CompanyInvite::select('id', 'parent_company_id', 'child_company_id', 'email', 'role_id',
                'activated', 'activated_at', 'type', 'author_id', 'created_at', 'updated_at', 'user_invite')
                ->with('childCompany.enumType', 'parentCompany:id',
                    'parentCompany.whitelabeling:id,company_id,primary_color,accent_color',
                    'parentCompany.whitelabeling.logo', 'parentCompany.whitelabeling.favicon')
                ->when(isset($parts->type), static function ($q) use ($parts) {
                    $q->where('type', $parts->type);
                })
                ->where('email', $parts->email)
                ->where('parent_company_id', $parts->parent_company_id)
                ->where('child_company_id', $parts->child_company_id)
                ->first();
            $user = User::where('email', $parts->email)->first();
            if ($user && $companyInvite) {
                // if the user already exists in the system we activate the invite and assign
                // the user the corresponding invite role id
                $companyInvite->activated = true;
                $companyInvite->activated_at = now();
                $companyInvite->save();
                RoleUserService::updateUserRoleByCompany($companyInvite->childCompany, $companyInvite->role_id, $user);
            }

            return new CompanyInviteResource($companyInvite);
        } else {
            // Open invitation
            $company = Company::find($parts->parent_company_id);

            return new CompanyOpenInviteResource($company);
        }
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/validate-invitation/affiliate",
     *     operationId="company/validateInvitationAffiliate",
     *     tags={"CompanyInviteController"},
     *     summary="Validates the signature for an invitation for an affiliate",
     *     description="Validates the signature for an invitation for an affiliate",
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/validate-invitation/affiliate
    // NO Bearer Token needed
    public function validateInvitationAffiliate(CompanyInviteValidationRequest $request): CompanyInviteResource
    {
        $parts = CompanyClientService::decodeInviteSignature($request->signature);
        $companyInvite = CompanyInvite::select('id', 'parent_company_id', 'child_company_id', 'email',
            'activated', 'activated_at', 'type', 'author_id', 'created_at', 'updated_at', 'user_invite', 'role_id')
            ->with('childCompany.enumType', 'parentCompany.enumType',
                'parentCompany.affiliateBrandAsMainCompany:id,main_company_id',
                'invitedUser.company.enumType:id,type_is_of_vendor')
            ->when(isset($parts->type), static function ($q) use ($parts) {
                $q->where('type', $parts->type);
            })
            ->where('email', $parts->email)
            ->where('parent_company_id', $parts->parent_company_id)
            ->first();
        if (!$companyInvite) {
            throw ValidationException::withMessages([
                'signature' => config('genericMessages.error.INVALID_INVITATION_SIGNATURE'),
            ]);
        }
        if ($companyInvite->invitedUser && $companyInvite->invitedUser->company
            && $companyInvite->invitedUser->company->enumType->value === CompanyType::FranchiseMsp) {
            $companyInvite->affiliate_brand_id = $companyInvite->parentCompany?->affiliateBrandAsMainCompany?->id;
        }

        return new CompanyInviteResource($companyInvite);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Delete(
     *     path="/api/v1/company/{friendly_url}/invite/delete",
     *     operationId="company/invite/delete",
     *     tags={"CompanyInviteController"},
     *     summary="Delete an invite",
     *     description="Delete an invite",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: DELETE
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/invite/delete
    // Bearer token needed
    public function deleteInvite(CompanyDeleteInviteRequest $request): JsonResponse
    {
        CompanyInvite::whereIn('id', $request->ids)->delete();

        return response()->json();
    }
}
