<?php

namespace App\Http\Controllers\Api\Contract;

use App\Enums\Company\CompanyType as CompanyTypeEnum;
use App\Enums\Contract\ContractsFilters;
use App\Enums\Contract\ContractTypes;
use App\Enums\CSV\CSVColumnsEnum;
use App\Enums\DefaultEmail;
use App\Enums\ProfileImageType;
use App\Helpers\UtilityHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\AdminSearchRequest;
use App\Http\Requests\Contract\ContractDatesRequest;
use App\Http\Requests\Contract\ContractDeleteRequest;
use App\Http\Requests\Contract\ContractFilterRequest;
use App\Http\Requests\Contract\ContractGetPaymentCostRequest;
use App\Http\Requests\Contract\ContractImportCSVSummaryRequest;
use App\Http\Requests\Contract\ContractKPIsRequest;
use App\Http\Requests\Contract\ContractStoreMultipleFromCSVRequest;
use App\Http\Requests\Contract\ContractStoreRequest;
use App\Http\Requests\Contract\ContractUpdateRequest;
use App\Http\Requests\Contract\ProductContractStoreRequest;
use App\Http\Requests\Contract\ProductContractUpdateRequest;
use App\Http\Requests\Contract\SearchCompanyContractsRequest;
use App\Http\Resources\Category\CategoryAsFilterResource;
use App\Http\Resources\Category\CategoryResource;
use App\Http\Resources\Company\CompanyAsFilterResource;
use App\Http\Resources\Contract\ContractCategoryCostResource;
use App\Http\Resources\Contract\ContractFirstLoadResource;
use App\Http\Resources\Contract\ContractKPIsResource;
use App\Http\Resources\Contract\ContractProductSimpleResource;
use App\Http\Resources\Contract\ContractResource;
use App\Http\Resources\Contract\ContractSimpleResource;
use App\Http\Resources\MissingProductOrVendor\MissingProductOrVendorResource;
use App\Models\Category\Category;
use App\Models\Company\Company;
use App\Models\Contract\Contract;
use App\Models\Contract\ContractBillingTypeOptions;
use App\Models\MissingProductOrVendor;
use App\Models\Product;
use App\Notifications\MissingProductOrVendorNotification;
use App\Services\AppConfig;
use App\Services\AuthService;
use App\Services\Contract\ContractService;
use App\Services\ImageService;
use DateTime;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Illuminate\Validation\ValidationException;
use stdClass;

class ContractController extends Controller
{
    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/contracts/get-payment-cost",
     *     operationId="contracts/getPaymentCost",
     *     tags={"ContractController"},
     *     summary="Get value of the cost of a payment",
     *     description="Get value of the cost of a payment",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/contracts/get-payment-cost
    // Bearer token needed
    public function getPaymentCost(ContractGetPaymentCostRequest $request): JsonResponse
    {
        $paymentDetail = ContractService::calculatePaymentCost(
            $request->contract_agreement_id,
            $request->contract_billing_type_id,
            $request->billing_frequency,
            $request->cost ?? 0,
            $request->unit_cost ?? 0,
            $request->amount_purchased ?? 0,
        );

        return response()->json(['payment_detail' => $paymentDetail]);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/contracts/{company}/fixed-rate-count",
     *     operationId="contracts/getFixedRateContractsCount",
     *     tags={"ContractController"},
     *     summary="Return the names of all contracts with fixed exchange rate for a company",
     *     description="Return the names of all contracts with fixed exchange rate for a company",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/contracts/{company}/fixed-rate-count
    // Bearer token needed
    public function getFixedRateContractsCount(Company $company): JsonResponse
    {
        $contracts = $company->contracts()
            ->where('has_fixed_rate', true)
            ->whereNotNull('parent_id')
            ->pluck('name')
            ->toArray();

        return response()->json(['contract_names' => $contracts]);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/contracts/{company}/first-load",
     *     operationId="contracts/firstLoad",
     *     tags={"ContractController"},
     *     summary="Get all contracts for a company",
     *     description="Get all contracts for a company",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/contracts/{company}/first-load
    // Bearer token needed
    public function firstLoad(Company $company): ContractFirstLoadResource
    {
        $response = Gate::inspect('contracts', $company);
        if (!$response->allowed()) {
            abort(403, $response->message());
        }
        $response = Gate::inspect('contracts-calendar', $company);
        if (!$response->allowed()) {
            abort(403, $response->message());
        }
        $relations = [
            'contractBillingType.option',
            'contractType:id,key,name',
            'company.avatar',
            'category.parentCategory',
            'currency:id,key,symbol,name',
            'nextPaymentDates', 'notifications', 'agreement',
            'parent:id,name',
            'productContracts.company.avatar',
            'productContracts.nextPaymentDates',
            'productContracts.category',
            'productContracts.contractType',
            'productContracts.contractBillingType',
            'clientVendor:id,name,is_distributor',
            'clientProduct:id,name,category_id,description',
            'plaidBankAccount' => function ($query) use ($company) {
                $query->where('plaid_transactions.company_id',  $company->id);
            },
        ];
        $contracts = ContractService::loadContracts(
            $company->enumType->value,
            $company->id,
            now()->subYears(10),
            now()->addYears(10),
            $relations
        );

        $result = new stdClass();
        $result->subscriptionsListView = ContractService::getContractsThatMakePaymentInPeriod(
            $contracts,
            now()->subYears(10),
            now()->addYears(10)
        );

        $yearlyCostElements = ContractService::getContractsThatMakePaymentInPeriod(
            $contracts,
            now()->startOfYear(),
            now()->endOfYear()
        );
        $result->subscriptionsCalendarView = ContractService::prepareContractsAsIndependentElements(
            $yearlyCostElements->pluck('contracts')->flatten()
        );

        $startInterval = now();
        $endInterval = now()->addDays(180);
        $upcomingRenewals = UtilityHelper::cloneCollection($contracts);
        $result->upcomingRenewals = ContractService::prepareContractsAsIndependentElements($upcomingRenewals)
            ->filter(function ($contract) use ($startInterval, $endInterval) {
                return $contract->end_date >= $startInterval && $contract->end_date <= $endInterval
                    ? $contract
                    : null;
            })->sortBy('end_date');
        $result->filters = $this->prepareFilters($contracts);

        return new ContractFirstLoadResource($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/contracts/{company}/kpis",
     *     operationId="contracts/getKPIs",
     *     tags={"ContractController"},
     *     summary="Get the KPIs of all contracts for a company",
     *     description="Get the KPIs of all contracts for a company",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/contracts/{company}/kpis
    // Bearer token needed
    public function getKPIs(ContractKPIsRequest $request, Company $company): ContractKPIsResource
    {
        $response = Gate::inspect('contracts', $company);
        if (!$response->allowed()) {
            abort(403, $response->message());
        }
        $result = ContractService::getContractsKPIs($company, $request->audit ?? false);

        return new ContractKPIsResource($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/contracts/{company}",
     *     operationId="contracts/search",
     *     tags={"ContractController"},
     *     summary="Search contracts for a company",
     *     description="Get paginated search of contracts for a company",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/contracts/{company}
    // Bearer token needed
    public function searchCompanyContracts(Company $company, SearchCompanyContractsRequest $request): AnonymousResourceCollection
    {
        $isMspClient = $company->enumType->value === CompanyTypeEnum::MSP_CLIENT;

        $contractsQuery = $company->contracts()
            ->select('contracts.id', 'contracts.name', 'contracts.company_id', 'contracts.client_vendor_id')
            ->when($request->has('product_contract'), function ($query) use ($request, $isMspClient) {
                $productColumn = $isMspClient ? 'client_product_id' : 'product_id';
                if ($request->product_contract === 'include') {
                    return $query->whereNotNull($productColumn);
                }

                return $query->whereNull($productColumn);
            })->when($request->has('parent_contract'), function ($query) use ($request) {
                if ($request->parent_contract === 'include') {
                    return $query->whereNotNull('parent_id');
                }

                return $query->whereNull('parent_id');
            })->when($request->has('search_word'), function ($query) use ($request) {
                $query->where('contracts.name', 'ilike', '%' . $request->search_word . '%');
            });

        $company->load('vendor');

        if (!$company->enumType->type_is_of_vendor) {
            if ($company->enumType->value !== CompanyTypeEnum::MSP_CLIENT) {
                $contractsQuery->with([
                    'company:id,name,type',
                    'company.enumType:id,value',
                ]);
            } else if ($company->enumType->value === CompanyTypeEnum::MSP_CLIENT) {
                $contractsQuery->with([
                    'clientVendor:id,name',
                ]);
            }
        }

        $contracts = UtilityHelper::getSearchRequestQueryResults($request, $contractsQuery);

        return ContractSimpleResource::collection($contracts);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/contracts/{company}/products",
     *     operationId="contracts",
     *     tags={"ContractController"},
     *     summary="Search contract products for a company",
     *     description="Get paginated search of contracts's products for a company",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/contracts/{company}/products
    // Bearer token needed
    public function searchCompanyContractProducts(Company $company, AdminSearchRequest $request): AnonymousResourceCollection
    {
        $contractProductsId = $company->contracts()->pluck('product_id')->filter();

        $contractProductQuery = Product::select('products.id', 'products.name', 'products.company_id')
            ->join('companies', 'companies.id', '=', 'products.company_id')
            ->whereIn('products.id', $contractProductsId)
            ->where(function ($query) use ($request) {
                $query->where('products.name', 'ilike', '%' . $request->search_word . '%')
                    ->orWhere('companies.name', 'ilike', '%' . $request->search_word . '%');
            })
            ->with(['company:id,name,type', 'company.enumType:id,value']);

        $contractProducts = UtilityHelper::getSearchRequestQueryResults($request, $contractProductQuery);

        return ContractProductSimpleResource::collection($contractProducts);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/contracts/{company}/load-subscription-calendar-view",
     *     operationId="contracts/loadCalendarListView",
     *     tags={"ContractController"},
     *     summary="Get calendar view contracts for a company in a date range",
     *     description="Get calendar view contracts for a company in a date range",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     * @throws Exception
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/contracts/{company}/load-subscription-calendar-view
    // Bearer token needed
    public function loadSubscriptionCalendarView(
        ContractFilterRequest $request, Company $company): AnonymousResourceCollection
    {
        $response = Gate::inspect('contracts-calendar', $company);
        if (!$response->allowed()) {
            abort(403, $response->message());
        }
        $startDate = new Carbon(new DateTime($request->start_date));
        $endDate = new Carbon(new DateTime($request->end_date));
        $relations = [
            'contractBillingType.option', 'contractType', 'company.avatar', 'category.parentCategory',
            'nextPaymentDates', 'productContracts.company.avatar', 'productContracts.nextPaymentDates',
            'productContracts.contractType', 'productContracts.contractBillingType', 'agreement',
            'currency:id,key,symbol,name',
            'clientVendor:id,name,is_distributor',
            'clientProduct:id,name,category_id,description',
        ];
        $contracts = ContractService::loadContracts($company->enumType->value, $company->id, $startDate, $endDate,
            $relations, $request->search_word ?? '', [], $request->billing_types ?? [],
            $request->types ?? [], $request->vendors ?? [],
            $request->expiration_dates ?? []);
        // we need to append the images
        $vendors = $contracts->pluck('company')->flatten()->unique()->filter();
        $addons = $contracts->pluck('addOns')->flatten()->unique()->filter();
        $addonsVendors = $addons->pluck('company')->flatten()->unique()->filter();
        ImageService::appendAvatars($vendors->merge($addonsVendors));
        $filteredContracts = ContractService::getContractsThatMakePaymentInPeriod($contracts, $startDate, $endDate)
            ->pluck('contracts')->flatten()->unique()->filter();

        return ContractResource::collection($filteredContracts);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/contracts/{company}/load-subscription-list-view",
     *     operationId="contracts/loadSubscriptionListView",
     *     tags={"ContractController"},
     *     summary="Get list view contracts for a company in a date range",
     *     description="Get list view contracts for a company in a date range",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     * @throws Exception
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/contracts/{company}/load-subscription-list-view
    // Bearer token needed
    public function loadSubscriptionListView(
        ContractFilterRequest $request, Company $company)
    {
        $response = Gate::inspect('contracts', $company);
        if (!$response->allowed()) {
            abort(403, $response->message());
        }
        $startDate = new Carbon(new DateTime($request->start_date));
        $endDate = new Carbon(new DateTime($request->end_date));
        $relations = [
            'contractBillingType.option', 'contractType', 'company', 'category.parentCategory',
            'nextPaymentDates', 'notifications', 'agreement',
            'currency:id,key,symbol,name',
            'parent:id,name',
            'productContracts.company.avatar',
            'productContracts.nextPaymentDates',
            'productContracts.category',
            'productContracts.contractType',
            'productContracts.contractBillingType',
            'clientVendor:id,name,is_distributor',
            'clientProduct:id,name,category_id,description',
            'plaidBankAccount' => function ($query) use ($company) {
                $query->where('plaid_transactions.company_id',  $company->id);
            },
        ];
        $contracts = ContractService::loadContracts($company->enumType->value, $company->id, $startDate, $endDate,
            $relations, $request->search_word ?? '', [], $request->billing_types ?? [],
            $request->types ?? [], $request->vendors ?? [],
            $request->expiration_dates ?? [], $request->categories ?? [], $request->sub_categories ?? []);
        // we need to append the images
        $vendors = $contracts->pluck('company')->flatten()->unique()->filter();
        $productContracts = $contracts->pluck('productContracts')->flatten()->unique()->filter();
        $productContractsVendors = $productContracts->pluck('company')->flatten()->unique()->filter();
        ImageService::appendAvatars($vendors->merge($productContractsVendors));
        $contractsWithPaymentInPeriod = ContractService::getContractsThatMakePaymentInPeriod(
            $contracts, $startDate, $endDate, $request->order_by, $request->sort
        );

        return ContractCategoryCostResource::collection($contractsWithPaymentInPeriod)->values();
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/contracts/{company}/load-import-csv-summary",
     *     operationId="contracts/loadImportCSVSummary",
     *     tags={"ContractController"},
     *     summary="Get the summary for a given import CSV session",
     *     description="Get the summary for a given import CSV session",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     * @throws Exception
     */
    // </editor-fold>
    // API Call: GET
    // {{server}}/api/v1/contracts/{company}/load-import-csv-summary
    // Bearer token needed
    public function loadImportCSVSummary(ContractImportCSVSummaryRequest $request, Company $company)
    {
        $response = Gate::inspect('contracts', $company);
        if (!$response->allowed()) {
            abort(403, $response->message());
        }
        $relations = [
            'contractType', 'company.avatar',
            'parent:id,name',
            'product:id,name',
            'clientVendor:id,name,is_distributor',
            'clientProduct:id,name,category_id,description',
        ];
        $query = Contract::select('contracts.id', 'contracts.name', 'contracts.parent_id',
            'contracts.product_id', 'contracts.contract_type_id',
            'contracts.author_id', 'contracts.owner_id',
            'contracts.custom_properties', 'contracts.created_at', 'contracts.updated_at',
            'contracts.company_id', 'contracts.client_vendor_id', 'contracts.client_product_id')
            ->with($relations)
            ->where('owner_id', $company->id)
            ->whereJsonContains('custom_properties->batch', $request->cache_token)
            ->where(function ($query) {
                $query->whereHas('contractType', function ($q) {
                    $q->where(['key' => ContractTypes::PRODUCT_CONTRACT]);
                })->orWhereDoesntHave('productContracts');
            });
        $matched = $query->get();
        $tokenCacheKey = $request->cache_token;
        $cacheData = Cache::get($tokenCacheKey);
        $decodedData = empty($cacheData) || !isset($cacheData['data'])
            ? []
            : json_decode($cacheData['data'], true) ?? [];
        Cache::forget($tokenCacheKey);

        // NOTE: A Resource is used to format the data before it is cached.
        return array_merge([
            'matched' => ContractResource::collection($matched),
        ], $decodedData);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/contracts/{company}/export-to-csv",
     *     operationId="contracts/exportToCsv",
     *     tags={"ContractController"},
     *     summary="Get CSV file with contracts for a company",
     *     description="Get CSV file with contracts for a company",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/contracts/{company}/export-to-csv
    // needs Bearer Token
    public function exportToCsv(ContractFilterRequest $request, Company $company)
    {
        $response = Gate::inspect('contracts-exporting', $company);
        if (!$response->allowed()) {
            abort(403, $response->message());
        }
        $companyConfig = $company->getConfig();
        $startDate = new Carbon(new DateTime($request->start_date));
        $endDate = new Carbon(new DateTime($request->end_date));
        $relations = [
            'contractBillingType.option', 'contractType', 'company', 'category.parentCategory',
            'nextPaymentDates', 'notifications', 'agreement',
            'parent:id,name',
            'currency:id,key,symbol,name',
            'productContracts.company.avatar',
            'productContracts.nextPaymentDates',
            'productContracts.category',
            'productContracts.contractType',
            'productContracts.contractBillingType',
            'clientVendor:id,name,is_distributor',
            'clientProduct:id,name,category_id,description',
        ];
        $contracts = ContractService::loadContracts($company->enumType->value, $company->id, $startDate, $endDate,
            $relations, $request->search_word ?? '', [], $request->billing_types ?? [],
            $request->types ?? [], $request->vendors ?? [],
            $request->expiration_dates ?? [], $request->categories ?? []);
        $columns = CSVColumnsEnum::importContracts;
        $data = [];
        $contractBillingTypeOptions = ContractBillingTypeOptions::whereIn(
            'id', $contracts->pluck('custom_properties.per')->flatten()->unique()
        )->get();
        foreach ($contracts as $contract) {
            $cost = number_format(round((float)($contract->cost ?? 0), 2), 2);
            $costAfterDiscount = number_format(round((float)($contract->cost_after_discount ?? 0), 2), 2);
            $convertedCost = (bool)$contract->has_fixed_rate && !empty($contract->exchange_rate) && $contract->exchange_rate > 0
                ? number_format(round((float)($contract->cost_after_discount / $contract->exchange_rate), 2), 2)
                : '';
            $data[] = [
                '' . $contract->id,
                $contract->contractType?->name,
                $contract->name,
                $contract->parent?->name,
                $contract->company?->name,
                $contract->product?->name,
                $contract->clientCompany?->name,
                $contract->clientProduct?->name,
                !empty($contract->category?->parent_id) ? $contract->category?->parentCategory?->name : $contract->category?->name,
                !empty($contract->category?->parent_id) ? $contract->category?->name : null,
                $contract->agreement?->name,
                $contract->contractBillingType?->name,
                Arr::get($contract->custom_properties, 'tier_name', ''),
                $contract->currency?->key,
                $companyConfig->currency->key,
                $contract->has_fixed_rate ? 'yes' : 'no',
                $contract->exchange_rate,
                $cost,
                $contract->discount,
                $costAfterDiscount,
                $convertedCost,
                $contractBillingTypeOptions->where(
                    'id',
                    Arr::get($contract->custom_properties, 'per', ''))->first()?->name ?? '',
                Arr::get($contract->custom_properties, 'amount_purchased', ''),
                Arr::get($contract->custom_properties, 'unit_cost', ''),
                Arr::get($contract->custom_properties, 'details', ''),
                $contract->every . ' ' . $contract->recurrence,
                $contract->start_date,
                $contract->end_date,
                $contract->initial_payment_date,
                $contract->expiry_date,
                $contract->notice_date,
                $contract->notice_period,
                $contract->auto_renew ? 'yes' : 'no',
            ];
        }
        $fileName = 'subscriptions-' . time() . '.csv';

        return UtilityHelper::getCSVFileResponse($columns, $data, $fileName);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/contracts/template-csv",
     *     operationId="contracts/exportTemplateCsv",
     *     tags={"ContractController"},
     *     summary="Get Template CSV file based on column names",
     *     description="Get Template CSV file based on column names",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/contracts/template-csv
    // needs Bearer Token
    public function exportTemplateCsv(Company $company)
    {
        $columns = CSVColumnsEnum::importContracts;
        $fileName = 'subscriptionUploadTemplate.csv';

        return UtilityHelper::getCSVFileResponse($columns, [], $fileName);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/contracts/{company}/contract/{contract}",
     *     operationId="contracts/loadContract",
     *     tags={"ContractController"},
     *     summary="Get a particular contract for a company",
     *     description="Get a particular contract for a company",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/contracts/{company}/contract/{contract}
    // Bearer token needed
    public function loadContract(Company $company, Contract $contract): ContractResource
    {
        $response = Gate::inspect('contracts', $company);
        if (!$response->allowed()) {
            abort(403, $response->message());
        }
        if ($company->id !== $contract->owner_id) {
            throw ValidationException::withMessages(
                ['contract' => config('genericMessages.error.CONTRACT_NOT_BELONGS_TO_COMPANY')]
            );
        }
        $relations = ['contractBillingType.option', 'contractType', 'company.avatar', 'category.parentCategory', 'nextPaymentDates',
            'productContracts.company', 'productContracts.company.avatar', 'productContracts.nextPaymentDates', 'productContracts.category',
            'productContracts.contractType', 'productContracts.contractBillingType',
            'productContracts.clientVendor:id,name,is_distributor',
            'productContracts.clientProduct:id,name,category_id,description',
            'productContracts.product', 'productContracts.notifications',
            'productContracts.currency:id,key,symbol,name',
            'currency:id,key,symbol,name',
            'product', 'notifications', 'agreement',
            'clientVendor:id,name,is_distributor',
            'clientProduct:id,name,category_id,description',
            'plaidBankAccount' => function ($query) use ($company) {
                $query->where('plaid_transactions.company_id',  $company->id);
            },
        ];

        $contract->load($relations);

        if ($contract->company) {
            $contract->company->avatar = ImageService::findAvatarInDB(
                $contract->company, ProfileImageType::CompanyAvatar, false);
        }
        ContractService::appendPaymentCost($contract);

        return new ContractResource($contract);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/contracts/{company}/load-upcoming-renewals",
     *     operationId="contracts/loadUpcomingRenewals",
     *     tags={"ContractController"},
     *     summary="Get upcoming renewals contracts for a company in a date range",
     *     description="Get upcoming renewals contracts for a company in a date range",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     * @throws Exception
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/contracts/{company}/load-upcoming-renewals
    // Bearer token needed
    public function loadUpcomingRenewals(
        ContractDatesRequest $request, Company $company): AnonymousResourceCollection
    {
        $response = Gate::inspect('contracts', $company);
        if (!$response->allowed()) {
            abort(403, $response->message());
        }
        $startDate = new Carbon(new DateTime($request->start_date));
        $endDate = new Carbon(new DateTime($request->end_date));
        $response = ContractService::getUpcomingRenewals($company, $startDate, $endDate);

        return ContractResource::collection($response);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/contracts/{company}/load-total-costs-for-period",
     *     operationId="contracts/loadTotalCosts",
     *     tags={"ContractController"},
     *     summary="Get all total costs for contracts for a company in a date range",
     *     description="Get all total costs for contracts for a company in a date range",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     * @throws Exception
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/contracts/{company}/load-total-costs-for-period
    // Bearer token needed
    public function loadTotalCostsForPeriod(
        ContractDatesRequest $request, Company $company): AnonymousResourceCollection
    {
        $response = Gate::inspect('contracts-reporting', $company);
        if (!$response->allowed()) {
            abort(403, $response->message());
        }
        $startDate = new Carbon(new DateTime($request->start_date));
        $endDate = new Carbon(new DateTime($request->end_date));
        $relations = ['contractBillingType.option', 'contractType', 'company.avatar', 'category.parentCategory',
            'nextPaymentDates', 'productContracts.company.avatar', 'productContracts.nextPaymentDates',
            'productContracts.contractType', 'productContracts.contractBillingType', 'agreement',
            'clientVendor:id,name,is_distributor',
            'clientProduct:id,name,category_id,description',
        ];
        $contracts = ContractService::loadContracts($company->enumType->value, $company->id,
            $startDate, $endDate, $relations);
        // we need to append the images
        $vendors = $contracts->pluck('company')->flatten()->unique()->filter();
        ImageService::appendAvatars($vendors);

        return ContractCategoryCostResource::collection(
            ContractService::getContractsThatMakePaymentInPeriod($contracts, $startDate, $endDate)
        );
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/contracts/{company}/store",
     *     operationId="contracts/store",
     *     tags={"ContractController"},
     *     summary="Store contract for a company",
     *     description="Store contract for a company",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws Exception
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/contracts/{company}/store
    // Bearer token needed
    public function store(ContractStoreRequest $request, Company $company): ContractResource
    {
        DB::beginTransaction();

        try {
            $response = Gate::inspect('contracts', $company);
            if (!$response->allowed()) {
                abort(403, $response->message());
            }
            $loggedUser = AuthService::getAuthUser();
            $contractData = $request->validated();
            $contract = ContractService::storeVendorContract($contractData, $company, $loggedUser);
            DB::commit();

            return new ContractResource($contract);
        } catch (\Throwable $ex) {
            // If anything goes bad, rollback the complete transaction
            DB::rollBack();
            Log::error('ERROR::' . __CLASS__ . '::' . __FUNCTION__
                . '::' . $ex->getMessage() . '::' . $ex->getTraceAsString());

            throw ValidationException::withMessages([
                'ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::Contract not created, check logs',
            ]);
        }
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/contracts/{company}/product_contracts/store",
     *     operationId="contracts/product_contracts/store",
     *     tags={"ContractController"},
     *     summary="Store a product contract for an aggregator contract of a company",
     *     description="Store a product contract for an aggregator contract of a company",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws Exception
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/contracts/{company}/product_contracts/store
    // Bearer token needed
    public function storeProductContract(ProductContractStoreRequest $request, Company $company): ContractResource
    {
        DB::beginTransaction();

        try {
            $response = Gate::inspect('contracts', $company);
            if (!$response->allowed()) {
                abort(403, $response->message());
            }
            $loggedUser = AuthService::getAuthUser();
            $contract = Contract::find($request->parent_id);
            ContractService::storeChildren($loggedUser, $company, [$request->validated()], $contract);

            $contract->load([
                'company', 'currency',
                'clientVendor:id,name,is_distributor',
                'clientProduct:id,name,category_id,description',
                'productContracts',
                'productContracts.currency',
                'productContracts.nextPaymentDates',
                'productContracts.notifications',
                'productContracts.agreement',
            ]);
            // If everything went OK, save all changes to the DB
            DB::commit();

            return new ContractResource($contract);
        } catch (\Throwable $ex) {
            // If anything goes bad, rollback the complete transaction
            DB::rollBack();
            Log::error('ERROR::' . __CLASS__ . '::' . __FUNCTION__
                . '::' . $ex->getMessage() . '::' . $ex->getTraceAsString());

            throw ValidationException::withMessages([
                'ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::Contract not created, check logs',
            ]);
        }
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/contracts/{company}/store-multiple-from-csv",
     *     operationId="contracts/store-multiple-from-csv",
     *     tags={"ContractController"},
     *     summary="Store multiple product contracts for a given company based on a CSV importing result",
     *     description="Store multiple product contracts for a given company based on a CSV importing result",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws Exception
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/contracts/{company}/store-multiple-from-csv
    // Bearer token needed
    public function storeMultipleFromCSV(ContractStoreMultipleFromCSVRequest $request, Company $company)
    {
        $source = __CLASS__ . '::' . __FUNCTION__ . '::';
        Log::debug($source . 'Received data: ' . json_encode($request->all(), JSON_PRETTY_PRINT));
        $loggedUser = AuthService::getAuthUser();
        $contractsData = $request->contracts;
        $missingVendorsAndProducts = [];
        $contractsToStore = [];

        foreach ($contractsData as $vendorContract) {
            if (empty($vendorContract)) {
                continue;
            }
            $validProductContracts = [];
            $hasMissingProducts = false;
            // Getting all unmatched products
            foreach ($vendorContract['product_contracts'] as $productContract) {
                if (is_null($productContract['product_id'])) {
                    $missingVendorsAndProducts[] = [
                        'user_id' => $loggedUser->id,
                        'user_email' => $loggedUser->email,
                        'company_id' => $company->id,
                        'vendor_name' => $vendorContract['company_name'],
                        'vendor_id' => $vendorContract['company_id'] ?? null,
                        'product_name' => $productContract['product_name'],
                    ];
                    $hasMissingProducts = true;
                } else {
                    $validProductContracts[] = $productContract;
                }
            }
            // Adding unmatched vendor and/or products to result
            if (is_null($vendorContract['company_id']) && !$hasMissingProducts) {
                $missingVendorsAndProducts[] = [
                    'user_id' => $loggedUser->id,
                    'user_email' => $loggedUser->email,
                    'company_id' => $company->id,
                    'vendor_name' => $vendorContract['company_name'],
                    'vendor_id' => null,
                    'product_name' => null,
                ];
            }

            // Building list for contracts to be stored
            if (!is_null($vendorContract['company_id']) && !empty($validProductContracts)) {
                $vendorContract['product_contracts'] = $validProductContracts;
                $contractsToStore[] = $vendorContract;
            }
        }
        // Creating matched contracts
        foreach ($contractsToStore as $contract) {
            Log::debug($source . 'Creating new contract: ' . "\n" . json_encode($contract, JSON_PRETTY_PRINT));
            ContractService::storeVendorContract($contract, $company, $loggedUser);
        }
        // Sending missing vendor/products email to Channel Program
        Log::debug($source . 'Missing Vendors/Products: ' . "\n" . json_encode($missingVendorsAndProducts, JSON_PRETTY_PRINT));

        // Send emails
        $response = [];
        foreach ($missingVendorsAndProducts as $missingRecord) {
            $missingProductOrVendor = MissingProductOrVendor::create($missingRecord);
            $salesEmail = AppConfig::loadAppConfigByKey('SALES_EMAIL', DefaultEmail::SALES_EMAIL)
                ->value;
            $supportEmail = AppConfig::loadAppConfigByKey('SUPPORT_EMAIL', DefaultEmail::SUPPORT_EMAIL)
                ->value;
            if (!filter_var($salesEmail, FILTER_VALIDATE_EMAIL)) {
                $salesEmail = AppConfig::updateAppConfigByKey('SALES_EMAIL', DefaultEmail::SALES_EMAIL)->value;
            }
            if (!filter_var($supportEmail, FILTER_VALIDATE_EMAIL)) {
                $supportEmail = AppConfig::updateAppConfigByKey('SUPPORT_EMAIL', DefaultEmail::SUPPORT_EMAIL)->value;
            }
            Notification::route('mail', [$supportEmail, $salesEmail])
                ->notify(new MissingProductOrVendorNotification($missingProductOrVendor));

            $response[] = new MissingProductOrVendorResource($missingProductOrVendor);
        }
        // Sending missing vendor/products email to Channel Program
        Log::debug($source . 'Stored Missing Vendors/Products: ' . "\n" . json_encode($response, JSON_PRETTY_PRINT));
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/contracts/{company}/update",
     *     operationId="contracts/update",
     *     tags={"ContractController"},
     *     summary="Update contract for a company",
     *     description="Update contract for a company",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     * @throws Exception
     */
    // </editor-fold>
    // API Call: PUT
    // http://127.0.0.1:8000/api/v1/contracts/{company}/update
    // Bearer token needed
    public function update(ContractUpdateRequest $request, Company $company): ContractResource
    {
        $response = Gate::inspect('contracts', $company);
        if (!$response->allowed()) {
            abort(403, $response->message());
        }
        $loggedUser = AuthService::getAuthUser();
        $contract = ContractService::loadContractFromIdAndType($request->id, ContractTypes::CONTRACT);
        ContractService::updateContract($loggedUser, $contract, $request->validated());
        $contract->refresh()->load([
            'company', 'currency',
            'clientVendor:id,name,is_distributor',
            'clientProduct:id,name,category_id,description',
            'productContracts',
            'productContracts.currency',
            'productContracts.nextPaymentDates',
            'productContracts.notifications',
            'productContracts.agreement',
        ]);

        return new ContractResource($contract);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/contracts/{company}/product_contracts/update",
     *     operationId="contracts/product_contracts/update",
     *     tags={"ContractController"},
     *     summary="Update a product contract for a company",
     *     description="Update a product contract for a company",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     * @throws Exception
     */
    // </editor-fold>
    // API Call: PUT
    // http://127.0.0.1:8000/api/v1/contracts/{company}/product_contracts/update
    // Bearer token needed
    public function updateProductContract(ProductContractUpdateRequest $request, Company $company): ContractResource
    {
        $response = Gate::inspect('contracts', $company);
        if (!$response->allowed()) {
            abort(403, $response->message());
        }
        $loggedUser = AuthService::getAuthUser();
        $contract = ContractService::loadContractFromIdAndType($request->id, ContractTypes::PRODUCT_CONTRACT, $request->parent_id);
        ContractService::updateContract($loggedUser, $contract, $request->validated());
        $contract->refresh()->load([
            'company', 'currency',
            'clientVendor:id,name,is_distributor',
            'clientProduct:id,name,category_id,description',
            'nextPaymentDates',
            'notifications',
            'agreement',
        ]);

        return new ContractResource($contract);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Delete(
     *     path="/api/v1/contracts/{company}/delete",
     *     operationId="contracts/delete",
     *     tags={"ContractController"},
     *     summary="Delete contract for a company",
     *     description="Delete contract for a company",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: DELETE
    // http://127.0.0.1:8000/api/v1/contracts/{company}/delete
    /*
    {
        "id":"required|numeric|exists:contracts,id|isFromCompany",
    }
    */
    // Bearer token needed
    public function delete(ContractDeleteRequest $request, Company $company): JsonResponse
    {
        $response = Gate::inspect('contracts', $company);
        if (!$response->allowed()) {
            abort(403, $response->message());
        }
        $loggedUser = AuthService::getAuthUser();
        $contract = Contract::findOrFail($request->id);
        ContractService::deleteContract($loggedUser, $contract);

        return response()->json();
    }

    private function prepareFilters(Collection $contracts): array
    {
        $filters = [];
        $categories = $contracts->pluck('category.parentCategory')->flatten()->unique();
        if ($categories->count() > 0) {
            $categories->transform(function ($cat) {
                if (empty($cat)) {
                    $cat = new Category();
                    $cat->name = 'Uncategorized';
                }

                return $cat;
            });
            $filters['categories'] = ContractsFilters::categories;
            $filters['categories']['items'] = CategoryAsFilterResource::collection($categories->sortBy('name'));
        }
        $vendors = $contracts->pluck('company')->flatten()->unique()->filter()
            ->sortBy('name');
        if ($vendors->count() > 0) {
            $filters['vendors'] = ContractsFilters::vendors;
            $filters['vendors']['items'] = CompanyAsFilterResource::collection($vendors);
        }
        if ($contracts->count() > 0) {
            $filters['expiration_dates'] = ContractsFilters::expiration_dates;
            $filters['expiration_dates']['items'] = $contracts->unique('end_date')->map(function ($contract) {
                if ($contract->end_date) {
                    $endDate = new Carbon(new DateTime($contract->end_date));
                    $dateObj = new stdClass();
                    $dateObj->id = $contract->end_date;
                    $dateObj->name = $endDate->format('M d, Y');

                    return $dateObj;
                }

                return null;
            })->filter()->sortBy('id')->toArray();
        }
        $contractBillingTypes = $contracts->pluck('contractBillingType')->flatten()->unique()->filter()
            ->sortBy('order');
        if ($contractBillingTypes->count() > 0) {
            $filters['billing_types'] = ContractsFilters::billing_types;
            $filters['billing_types']['items'] = CategoryResource::collection($contractBillingTypes);
        }

        return $filters;
    }
}
