<?php

namespace App\Http\Controllers\Api\Filter;

use App\Helpers\PermissionsHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Company\Client\CompanyClientProductSearchRequest;
use App\Http\Requests\Company\Client\CompanyClientVendorSearchRequest;
use App\Http\Resources\Company\Client\ClientVendorResource;
use App\Http\Resources\Contract\ContractAgreementResource;
use App\Http\Resources\Contract\ContractBillingTypeOptionsResource;
use App\Http\Resources\Contract\ContractBillingTypeResource;
use App\Http\Resources\Contract\ContractNotificationTypeResource;
use App\Http\Resources\Contract\ContractTypeResource;
use App\Models\Contract\ClientProduct;
use App\Models\Contract\ClientVendor;
use App\Models\Contract\ContractAgreement;
use App\Models\Contract\ContractBillingType;
use App\Models\Contract\ContractBillingTypeOptions;
use App\Models\Contract\ContractNotificationType;
use App\Models\Contract\ContractType;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class FilterContractController extends Controller
{
    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/filters/contracts/types",
     *     operationId="getAllContractTypes",
     *     tags={"FilterContractController"},
     *     summary="Get all contract types",
     *     description="Returns all contract types",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/filters/contracts/types
    // NO Bearer token needed
    public function getAllContractTypes()
    {
        return ContractTypeResource::collection(ContractType::whereNotNull('id')->orderBy('order')->get());
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/filters/contracts/billing-types",
     *     operationId="getAllContractBillingTypes",
     *     tags={"FilterContractController"},
     *     summary="Get all contract types",
     *     description="Returns all contract billing types",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/filters/contracts/billing-types
    // NO Bearer token needed
    public function getAllContractBillingTypes()
    {
        return ContractBillingTypeResource::collection(
            ContractBillingType::whereNotNull('id')->orderBy('order')->get());
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/filters/contracts/billing-type-options",
     *     operationId="getAllContractNotificationTypes",
     *     tags={"FilterContractController"},
     *     summary="Get all contract type options",
     *     description="Returns all contract billing type options",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/filters/contracts/billing-type-options
    // NO Bearer token needed
    public function getAllContractNotificationTypes()
    {
        return ContractNotificationTypeResource::collection(
            ContractNotificationType::whereNotNull('id')->orderBy('order')->get());
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/filters/contract/notification-types",
     *     operationId="getAllContractBillingTypeOptions",
     *     tags={"FilterContractController"},
     *     summary="Get all contract types",
     *     description="Returns all contract billing types",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/filters/contract/notification-types
    // NO Bearer token needed
    public function getAllContractBillingTypeOptions()
    {
        return ContractBillingTypeOptionsResource::collection(
            ContractBillingTypeOptions::whereNotNull('id')->orderBy('name')->get()
        );
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/filters/contracts/agreements",
     *     operationId="getAllContractAgreements",
     *     tags={"FilterContractController"},
     *     summary="Get all contract agreements",
     *     description="Returns all contract agreements",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/filters/contracts/agreements
    // NO Bearer token needed
    public function getAllContractAgreements()
    {
        return ContractAgreementResource::collection(
            ContractAgreement::whereNotNull('id')->orderBy('order')->get()
        );
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/filters/contracts/client-vendors",
     *     operationId="getClientVendors",
     *     tags={"FiltersController"},
     *     summary="Get client vendors",
     *     description="Get client vendors",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/filters/contracts/client-vendors
    // NO Bearer token needed
    public function getClientVendors(CompanyClientVendorSearchRequest $request): AnonymousResourceCollection
    {
        $companyId = $request->company_id ?? PermissionsHelper::getCompanyId($request);
        $clientVendors = ClientVendor::where('name', 'ilike', '%' . $request->name . '%')
            ->where('owner_id', $companyId)
            ->orderBy('name')
            ->get();

        return ClientVendorResource::collection($clientVendors);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/filters/contracts/client-products",
     *     operationId="getClientProducts",
     *     tags={"FiltersController"},
     *     summary="Get client products",
     *     description="Get client products",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/filters/contracts/client-products
    // NO Bearer token needed
    public function getClientProducts(CompanyClientProductSearchRequest $request): AnonymousResourceCollection
    {
        $companyId = $request->company_id ?? PermissionsHelper::getCompanyId($request);
        $clientProducts = ClientProduct::where('name', 'ilike', '%' . $request->name . '%')
            ->where('owner_id', $companyId)
            ->orderBy('name')
            ->get();

        return ClientVendorResource::collection($clientProducts);
    }
}
