<?php

namespace App\Http\Controllers\Api\IndustryEvent;

use App\Enums\Company\CompanyProfileTypes;
use App\Enums\IndustryEventLocation;
use App\Enums\IndustryEventsFilter;
use App\Enums\IndustryEventStatus;
use App\Enums\SubjectType;
use App\Helpers\UtilityHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\AdminSearchRequest;
use App\Http\Requests\IndustryEvent\IndustryEventDeleteRequest;
use App\Http\Requests\IndustryEvent\IndustryEventFilterRequest;
use App\Http\Requests\IndustryEvent\IndustryEventsExportRequest;
use App\Http\Requests\IndustryEvent\IndustryEventShowForCalendarRequest;
use App\Http\Requests\IndustryEvent\IndustryEventsShowAllRequest;
use App\Http\Requests\IndustryEvent\IndustryEventStoreRequest;
use App\Http\Requests\IndustryEvent\IndustryEventUnsubscribeEmailRequest;
use App\Http\Requests\IndustryEvent\IndustryEventUpdateRequest;
use App\Http\Resources\IndustryEvent\IndustryEventResource;
use App\Http\Resources\Media\ImageSimpleResource;
use App\Models\Country;
use App\Models\IndustryEvent\IndustryEvent;
use App\Models\State;
use App\Models\User;
use App\Services\AuthService;
use App\Services\ImageService;
use App\Services\UserService;
use DateTime;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Illuminate\Validation\ValidationException;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class IndustryEventsController extends Controller
{
    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/industry-events",
     *     operationId="showForCalendar",
     *     tags={"IndustryEventsController"},
     *     summary="Get all all the industry events filtered by date range to show in the calendar",
     *     description="Returns all the industry events filtered by date range to show in the calendar",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    /* http://127.0.0.1:8000/api/v1/industry-events
        ?start_date=required_with:end_date|date
        &end_date=required_with:start_date|after:start_date|date
        &search_word=sometimes|required|string|min:3
        &event_type=sometimes|required|allowedValues
        &cp_event=sometimes|required|boolean
        &past_events=sometimesrequired|boolean
        &location=sometimes|required|string|min:3
        &presenters=sometimes|required|array|min:1
    needs Bearer Token*/
    public function showForCalendar(IndustryEventShowForCalendarRequest $request)
    {
        $query = IndustryEvent::select(
            DB::raw("date_part('year', industry_events.start_date) as year"),
            DB::raw("date_part('month', industry_events.start_date) as month"),
            DB::raw('CAST(industry_events.id AS VARCHAR) AS event_id'),
            'industry_events.id',
            'industry_events.event_name',
            'industry_events.organization',
            'industry_events.start_date',
            'industry_events.end_date',
            'industry_events.in_person_or_virtual',
            'industry_events.link',
            'industry_events.cp_event',
            'industry_events.time_zone',
            'industry_events.location',
            'industry_events.status',
            DB::raw('CAST(industry_events.author_id AS VARCHAR)'),
            'industry_events.author_type',
            DB::raw('CAST(industry_events.subject_id AS VARCHAR)'),
            'industry_events.subject_type',
            'industry_events.created_at',
            'industry_events.updated_at',
            DB::raw('companies.name AS subject_name'),
            DB::raw('companies.friendly_url AS subject_friendly_url'),
            'industry_events.description',
            'industry_events.feature_flag',
            'industry_events.feature_start_date',
            'industry_events.feature_end_date'
        )
            ->leftJoin('companies', 'companies.id', '=', 'industry_events.subject_id')
            ->where('status', IndustryEventStatus::approved)
            ->orderBy('industry_events.start_date', 'ASC');
        $industryEvents = $this->prepareQuery($request, $query);
        $industryEvents = $industryEvents->get();
        ImageService::appendCompanyAvatarsURLs($industryEvents, 'subject_id');
        ImageService::appendIndustryEventMedia($industryEvents);
        $industryEvents->transform(function ($event) {
            $event->id = '' . $event->id;
            if (!empty($event) && !empty($event->image)) {
                $event->image = new ImageSimpleResource($event->image);
            }

            return $event;
        });
        $industryEvents = $industryEvents->groupBy('year');
        $industryEvents->transform(function ($year) {
            return $year->groupBy('month');
        });

        return $industryEvents;
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/industry-events/unsubscribe-with-token",
     *     operationId="unsubscribeEmailWithToken",
     *     tags={"IndustryEventsController"},
     *     summary="Unsubscribe the user from all industry calendar emails using the identification token",
     *     description="Returns an unsubscribe confirmation message",
     *
     *     @OA\RequestBody(
     *         required=true,
     *
     *         @OA\JsonContent(
     *             required={"unsubscription_token"},
     *
     *             @OA\Property(property="unsubscription_token", type="string"),
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/industry-events/unsubscribe
    /* {
           "unsubscription_token" : "required|string",
        }
     * */
    public function unsubscribeEmailWithToken(IndustryEventUnsubscribeEmailRequest $request)
    {
        $success = UserService::unsubscribeEmailWithToken($request->input('unsubscription_token'));

        if (!$success) {
            return response()->json([], 422);
        }

        return response()->json();
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/industry-events/show-all",
     *     operationId="industry-events/show-all/showAll",
     *     tags={"IndustryEventsController"},
     *     summary="Get all all the industry events for the claimer or admin",
     *     description="Returns all the industry events for the claimer or admin",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/industry-events/show-all
    // OPTIONAL PARAMS
    /*
       ?subject_id=required|numeric,
        &subject_type=required|AllowedValues,
        &paged=BOOLEAN
        &items_per_page=ITEMS
        &page=PAGE
        &order_by=COLUMN_NAME
        &sort=SORT_VALUE
        &search_word=SEARCH_WORD
        &display_past_events=STRING
        &status[]=sometimes|required|array|AllowedValues
     *
     * */
    // needs Bearer Token
    public function showAll(IndustryEventsShowAllRequest $request)
    {
        $subjectType = SubjectType::getValue($request->subject_type);
        $model = UtilityHelper::validateExistence($subjectType, $request->subject_id);
        $query = $this->prepareShowAllQuery($request);
        $result = UtilityHelper::getSearchRequestQueryResults($request, $query);

        if ($result instanceof LengthAwarePaginator) {
            $pageResults = $result->getCollection();
        } else {
            $pageResults = $result;
        }

        $pageResults = ImageService::appendSubjectsAvatars($pageResults, $model);

        if ($result instanceof LengthAwarePaginator) {
            $result->setCollection($pageResults);
        } else {
            $result = $pageResults;
        }

        return IndustryEventResource::collection($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/industry-events/store",
     *     operationId="industry-events/show-all/store",
     *     tags={"IndustryEventsController"},
     *     summary="Store the industry event",
     *     description="Store the industry event",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/industry-events/store
    /* {
           "subject_id" : "required|numeric",
           "subject_type" : "required|AllowedValues",
           "organization" : "sometimes|required|string|max:100",
           "event_name" : "required|string|max:255",
           "location" : "sometimes|required|string|max:2048",
           "time_zone" : "required",
           "start_date" : "required|after_or_equal:today|date",
           "end_date" : "required|after:start_date|date",
           "image": "sometimes|required|MimeTypes(MediaType::image)|max:5000",
           "in_person_or_virtual" : "required|AllowedValues",
           "link" : "sometimes|required|string|max:2048",
           "description" : "sometimes|required|string",
           "city" : "nullable|string|max:255",
           "state" : "nullable|string|max:255",
           "country" : "nullable|string|max:255",
        }
     * */
    // needs Bearer Token
    public function store(IndustryEventStoreRequest $request)
    {
        $subjectType = SubjectType::getValue($request->subject_type);
        $model = UtilityHelper::validateExistence($subjectType, $request->subject_id);
        $response = Gate::inspect('add-industry-event', $model);
        if (!$response->allowed()) {
            abort(403, $response->message());
        }
        $industryEventStatus = IndustryEventStatus::awaiting_approval;
        $industryEvent = IndustryEvent::create([
            'subject_id' => $request->subject_id,
            'subject_type' => get_class($model),
            'author_id' => AuthService::getLoggedInUserId(),
            'author_type' => User::class,
            'organization' => $request->organization,
            'event_name' => $request->event_name,
            'location' => $request->location,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
            'in_person_or_virtual' => $request->in_person_or_virtual,
            'link' => $request->link,
            'cp_event' => false,
            'status' => $industryEventStatus,
            'description' => $request->description,
            'time_zone' => $request->time_zone,
        ]);
        if ($request->has('image')) {
            $this->appendImage($request->image, $industryEvent);
        }
        if ($request->has('state') && $request->has('country')) {
            $this->updateCityState($request->all(), $industryEvent);
        }
        $industryEvent->load('media');

        return new IndustryEventResource($industryEvent);
    }

    /**
     * @return void
     */
    private function updateCityState(array $request, IndustryEvent $industryEvent)
    {
        $state = State::select('id')->where('name', $request['state'])
            ->orWhere('abbreviation', $request['state'])->first();
        if (!empty($state)) {
            $industryEvent->state_id = $state->id;
        }
        $country = Country::select('id')->where('short_code', $request['country'])
            ->orWhere('title', $request['country'])->first();
        if (!empty($country)) {
            $industryEvent->country_id = $country->id;
        }
        $industryEvent->zip = $request['zip'] ?? null;
        $industryEvent->city = $request['city'];
        $industryEvent->save();
    }

    private function appendImage($image, $industryEvent)
    {
        $loggedInUser = AuthService::getAuthUser();
        $customProperties = [];
        $customProperties['author_id'] = '' . $loggedInUser->id;
        $customProperties['author_name'] = $loggedInUser->first_name . ' ' . $loggedInUser->last_name;
        $industryEvent->addMedia($image)
            ->withCustomProperties($customProperties)
            ->toMediaCollection(config('custom.media_collections.industry_event_image'));
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/industry-events/update",
     *     operationId="industry-events/show-all/update",
     *     tags={"IndustryEventsController"},
     *     summary="Update an industry event",
     *     description="Update an industry event",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/industry-events/update
    /* {
            "id": "required|numeric|exists:industry_events,id",
            "organization" : "sometimes|required|string|max:100",
            "event_name": "sometimes|required|string|max:255",
            "location": "nullable|string|max:2048",
            "time_zone" : "sometimes|required",
            "start_date": "sometimes|required|date",
            "end_date": "sometimes|required|after:start_date|date",
            "in_person_or_virtual": "sometimes|required|AllowedValues(in_person,virtual)",
            "image": "sometimes|required|MimeTypes(MediaType::image)|max:5000",
            "link":"sometimes|required|string|max:1000",
            "status":"sometimes|required|allowedValues",
            "description" : "sometimes|required|string",
            "state" : "nullable|string|max:255",
            "country" : "nullable|string|max:255",
        }
     * */
    // needs Bearer Token
    public function update(IndustryEventUpdateRequest $request)
    {
        $industryEvent = IndustryEvent::findOrFail($request->id);
        UtilityHelper::validateExistence($industryEvent->subject_type, $industryEvent->subject_id);
        if ($industryEvent->status === IndustryEventStatus::rejected) {
            throw ValidationException::withMessages([
                config('genericMessages.error.INVALID_INDUSTRY_EVENT_REJECTED'),
            ]);
        }
        $industryEventStatus = IndustryEventStatus::awaiting_approval;
        if ($request->has('status') && $request->status !== $industryEvent->status) {
            if ($industryEvent->subject->companyProfileType->value === CompanyProfileTypes::VendorFree) {
                throw ValidationException::withMessages([
                    config('genericMessages.error.INVALID_INDUSTRY_EVENT_REJECTED'),
                ]);
            } else {
                $this->validateUpdateStatus($request, $industryEvent);
            }
        } else {
            $industryEvent->approved_user_id = null;
            $industryEvent->approved_date = null;
            $industryEvent->rejected_user_id = null;
            $industryEvent->rejected_date = null;
            $industryEvent->reason = null;
            $industryEvent->status = $industryEventStatus;
        }
        if ($request->has('image')) {
            $media = Media::select('id')->where('model_id', $industryEvent->id)->first();
            if ($media) {
                Media::find($media->id)->delete();
            }
            $this->appendImage($request->image, $industryEvent);
        }
        $industryEvent->update($request->validated());
        if ($request->has('state') && $request->has('country')) {
            $this->updateCityState($request->all(), $industryEvent);
        }
        $industryEvent->load('media');

        return new IndustryEventResource($industryEvent);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Delete(
     *     path="/api/v1/industry-events/delete",
     *     operationId="industry-events/show-all/delete",
     *     tags={"IndustryEventsController"},
     *     summary="Delete industry event id",
     *     description="Delete an industry event id",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: DELETE
    // http://127.0.0.1:8000/api/v1/industry-events/delete
    /* {
            "id" : "required|numeric|exists:industry_events,id"
        }
     * */
    // needs Bearer Token
    public function delete(IndustryEventDeleteRequest $request)
    {
        $industryEvent = IndustryEvent::findOrFail($request->id);
        UtilityHelper::validateExistence($industryEvent->subject_type, $industryEvent->subject_id);
        $industryEvent->delete();

        return response()->json();
    }

    private function prepareShowAllQuery(IndustryEventsShowAllRequest $request)
    {
        $query = IndustryEvent::with('subject')
            ->where('subject_id', $request->subject_id);

        if ($request->has('search_word')) {
            $query->whereRaw("(lower(event_name) like '%" .
                strtolower($request->search_word) . "%' OR lower(location) like '%" .
                strtolower($request->search_word) . "%' OR lower(organization) like '%" .
                strtolower($request->search_word) . "%')");
        }

        if (!$request->has('display_past_events') || !$request->display_past_events) {
            $query->where('start_date', '>=', now()->startOfDay());
        }

        if ($request->has('status')) {
            $query->whereIn('status', $request->status);
        }

        if (!$request->has('order_by')) {
            $query->orderBy('feature_flag', 'DESC');
            $query->orderBy('created_at', 'DESC');
        }

        return $query;
    }

    private function validateUpdateStatus($request, $industryEvent)
    {
        $industryEvent->approved_user_id = null;
        $industryEvent->approved_date = null;
        $industryEvent->rejected_user_id = null;
        $industryEvent->rejected_date = null;
        $industryEvent->reason = null;

        switch ($request->status) {
            case IndustryEventStatus::awaiting_approval:
                $allowedCurrentStatuses = IndustryEventStatus::getKeys([
                    IndustryEventStatus::archived,
                    IndustryEventStatus::approved,
                ]);
                $errorMessage = config('genericMessages.error.INVALID_INDUSTRY_EVENT_UNARCHIVED');

                break;
            case IndustryEventStatus::archived:
                $allowedCurrentStatuses = IndustryEventStatus::getKeys([
                    IndustryEventStatus::approved,
                    IndustryEventStatus::awaiting_approval,
                ]);
                $errorMessage = config('genericMessages.error.INVALID_INDUSTRY_EVENT_ARCHIVED');

                break;
            default:
                throw ValidationException::withMessages([
                    'ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::Logic not implemented for: ' . $request->status,
                ]);
        }

        if (!in_array($industryEvent->status, $allowedCurrentStatuses)) {
            throw ValidationException::withMessages([
                $errorMessage,
            ]);
        }
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/industry-events/export-calendar-to-csv",
     *     operationId="industry-events/export-calendar-to-csv",
     *     tags={"IndustryEventsController"},
     *     summary="Export industry events to csv",
     *     description="Export industry events to csv",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: Get
    // http://127.0.0.1:8000/api/v1/industry-events/export-calendar-to-csv
    /* {
            "id" : "required|numeric|exists:industry_events,id"
        }
     * */
    // needs Bearer Token
    public function exportIndustryEvents(IndustryEventsExportRequest $request)
    {
        if ($request->start_date) {
            $start = $request->start_date;
            $end = $request->end_date;
        } else {
            $start = now()->startOfMonth();
            $end = now()->endOfMonth();
        }

        $query = IndustryEvent::select(
            DB::raw('CAST(industry_events.id AS VARCHAR) AS event_id'),
            'industry_events.event_name',
            'industry_events.organization',
            'industry_events.start_date',
            'industry_events.end_date',
            'industry_events.in_person_or_virtual',
            'industry_events.link',
            'industry_events.location',
            'industry_events.description',
            'industry_events.feature_flag',
            DB::raw('companies.name AS subject_name'),
        )
            ->leftJoin('companies', 'companies.id', '=', 'industry_events.subject_id')
            ->where('status', IndustryEventStatus::approved)
            ->orderBy('start_date', 'DESC');
        $query = $this->prepareQuery($request, $query);
        $query = UtilityHelper::prepareSearchQuery($query, $request->search, $query->getModel());
        $industryEvents = $query->get();
        $columns = ['Sr', 'Organization', 'Event Name', 'Event Type', 'Start date and Time', 'End Date and Time', 'Event URL', 'Event Location', 'Description', 'Feature Flag'];
        $headers = ['From Date: ' . $start, 'To Date: ' . $end];
        $fileName = 'Industry Event Report ' . now()->format('m-d-Y') . '.csv';
        $data = [];
        foreach ($industryEvents as $key => $row) {
            $data[] = [
                ++$key,
                $row->organization, $row->event_name, $row->in_person_or_virtual, $row->start_date, $row->end_date, $row->link, $row->location, strip_tags($row->description), $row->feature_flag,
            ];
        }

        return UtilityHelper::getCSVFileResponse($columns, $data, $fileName, $headers);
    }

    private function prepareQuery(AdminSearchRequest $request, $query)
    {
        $start = null;
        $end = null;
        $includePastEvents = $request->has('past_events') && $request->past_events === 'true';
        $today = new DateTime();
        if (!$includePastEvents) {
            $start = date_format($today, 'Y-m-d H:i');
        }
        if ($request->has('start_date')) {
            $startDate = new DateTime($request->start_date);
            if (($startDate < $today && $includePastEvents) || ($startDate > $today)) {
                $start = date_format($startDate, 'Y-m-d H:i');
            }
            $end = $request->end_date;
        }

        $query->when($request->has('search_word'), function ($query) use ($request) {
            return $query->whereRaw("(lower(companies.name) like '%" .
                strtolower($request->search_word) . "%' OR lower(event_name) like '%" .
                strtolower($request->search_word) . "%' OR lower(industry_events.description) like '%" .
                strtolower($request->search_word) . "%' OR lower(location) like '%" .
                strtolower($request->search_word) . "%')");
        })->when($start, function ($query) use ($start) {
            return $query->where(function ($query) use ($start) {
                $datetime = new DateTime($start);
                $datetime->setTime(0, 0, 0);
                $query->where('start_date', '>=', $datetime->format('Y-m-d H:i:s'))
                    ->orWhereRaw('? BETWEEN start_date and end_date', [$start]);
            });
        })->when($end, function ($query) use ($end) {
            return $query->where(function ($query) use ($end) {
                $datetime = new DateTime($end);
                $datetime->setTime(23, 59, 59);
                $query->where('end_date', '<=', $datetime->format('Y-m-d H:i:s'))
                    ->orWhereRaw('? BETWEEN start_date and end_date', [$end]);
            });
        })->when($request->has('location'), function ($query) use ($request) {
            return $query->where('location', 'ilike', '%' . $request->location . '%');
        })->when($request->has('cp_event'), function ($query) use ($request) {
            return $query->where('cp_event', $request->cp_event);
        })->when($request->has('event_type'), function ($query) use ($request) {
            $eventTypes = [
                IndustryEventLocation::getKey(IndustryEventLocation::virtual),
                IndustryEventLocation::getKey(IndustryEventLocation::webinar),
                IndustryEventLocation::getKey(IndustryEventLocation::virtual_product_demo),
            ];
            if ($request->event_type === 'in_person') {
                $eventTypes = [
                    IndustryEventLocation::getKey(IndustryEventLocation::conference),
                    IndustryEventLocation::getKey(IndustryEventLocation::regional_event),
                    IndustryEventLocation::getKey(IndustryEventLocation::road_show),
                ];
            }

            return $query->whereIn('in_person_or_virtual', $eventTypes);
        })->when($request->has('presenters'), function ($query) use ($request) {
            return $query->where(function ($q) use ($request) {
                $q->whereIn('companies.name', $request->presenters)
                    ->orWhereIn('industry_events.organization', $request->presenters);
            });
        });

        return $query;
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/industry-events/filters",
     *     operationId="industry-events/Filter",
     *     tags={"IndustryEventsController"},
     *     summary="Returns the industry events location filters",
     *     description="Returns the industry events location filters",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/industry-events/filters
    // ?start_date=required|date
    // &end_date=required_with:start_date|after:start_date|date
    // needs Bearer Token
    public function filters(IndustryEventFilterRequest $request)
    {
        $start = $request->start_date;
        $end = $request->end_date;
        $items = [];
        $filters = IndustryEventsFilter::asArray();
        $industryEventLocations = IndustryEvent::whereNotNull('location')
            ->where('status', IndustryEventStatus::approved)
            ->where('in_person_or_virtual', '!=', IndustryEventLocation::virtual)
            ->when($start, function ($query) use ($start, $end) {
                return $query->where(function ($query) use ($start, $end) {
                    $query->whereBetween('start_date', [$start, $end])
                        ->orWhereBetween('end_date', [$start, $end])
                        ->orWhereRaw('? BETWEEN start_date and end_date', [$start])
                        ->orWhereRaw('? BETWEEN start_date and end_date', [$end]);
                });
            })->distinct('location')->pluck('id')->toArray();
        if (count($industryEventLocations) > 0) {
            $eventLocations = IndustryEvent::select('industry_events.city', 'states.name as state', 'countries.title as country')
                ->join('countries', 'industry_events.country_id', 'countries.id')
                ->join('states', 'industry_events.state_id', 'states.id')
                ->whereIn('industry_events.id', $industryEventLocations)->whereNotNull('industry_events.city')
                ->whereNotNull('industry_events.state_id')->whereNotNull('industry_events.country_id')
                ->groupBy('industry_events.city', 'states.name', 'countries.title')->get();
            if ($eventLocations->count() > 0) {
                $eventLocations->map(function ($locations) use (&$items) {
                    $items[] = [
                        'id' => $locations->city . ', ' . $locations->state . ', ' . $locations->country,
                        'name' => $locations->city . ', ' . $locations->state . ', ' . $locations->country,
                    ];
                });
                if (count($items) > 0) {
                    $filters['locations']['items'] = $items;
                }
            }
        }

        return response()->json([
            'filters' => $filters,
        ]);
    }
}
