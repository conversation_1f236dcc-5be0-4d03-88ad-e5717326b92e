<?php

namespace App\Http\Controllers\Api\Partner;

use App\Enums\Partner\InvitePartnerFilter;
use App\Enums\Partner\InviteSortingOptions;
use App\Enums\Partner\PartnerApplicationSource;
use App\Enums\Partner\PartnerPortalInvitationInitiator;
use App\Enums\Partner\PartnerPortalInvitationStatus;
use App\Facades\Models\User;
use App\Helpers\UtilityHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Partner\Invites\AcceptInvitationRequest;
use App\Http\Requests\Partner\Invites\AcceptMultipleInvitationRequest;
use App\Http\Requests\Partner\Invites\GetInvitePartnerRequest;
use App\Http\Requests\Partner\Invites\InvitePartnerCsvRequest;
use App\Http\Requests\Partner\Invites\InvitePartnerDeleteByIdsRequest;
use App\Http\Requests\Partner\Invites\InvitePartnerDeleteRequest;
use App\Http\Requests\Partner\Invites\InvitePartnerRequest;
use App\Http\Resources\Partner\Invite\InvitationResource;
use App\Http\Resources\Partner\Invite\OpenInvitationResource;
use App\Jobs\InvitePartnerCsvJob;
use App\Log;
use App\Models\Company\Company;
use App\Models\MSPFollowingPartner;
use App\Services\AuthService;
use App\Services\Company\CompanyService;
use App\Services\LookupService\LookupService;
use App\Services\Partner\InvitePartnerService;
use App\Services\UtilityService;
use App\Services\ValidationService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Validation\ValidationException;

class InvitePartnerController extends Controller
{
    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/partner/{vendor}/invite",
     *     operationId="partner/sendInvite",
     *     tags={"InvitePartnerController"},
     *     summary="Creates an invitation between companies for conecting in a PRM",
     *     description="Creates an invitation between companies for conecting in a PRM",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: Post
    // http://127.0.0.1:8000/api/v1/partner/{vendor}/invite
    // needs Bearer Token
    // BODY
    /*
     * {
            "as_company_id": "sometimes|required|exists:companies,id",
            "email": "ARRAY OF EMAILS",
            "initiator":"AllowedValues(PartnerPortalInvitationInitiator::getKeys())"
            "source":"AllowedValues(PartnerPortalInvitationInitiator::getKeys())"
        }
     */
    public function sendInvite(InvitePartnerRequest $request, Company $vendor): AnonymousResourceCollection
    {
        CompanyService::validateCompanyIsVendor($vendor->id);
        CompanyService::validateCompanyPartnerPageIsActive($vendor);
        ValidationService::blackListDomainListValidation($request->email);
        $loggedUser = AuthService::getAuthUser();
        $companyId = $request->has('as_company_id') ? $request->as_company_id : $loggedUser->company_id;
        $applicationSource = null;
        if ($request->initiator === PartnerPortalInvitationInitiator::Partner) {
            // Vendor inviting MSP
            $applicationSource = $request->source ?? PartnerApplicationSource::VendorInvite;
        } else {
            // MSP requesting access to Vendor
            CompanyService::validateCompanyIsMSP($companyId);
            if (count($request->email) > 1 || $loggedUser->email !== $request->email[0]) {
                throw ValidationException::withMessages([
                    'email' => ['You can only invite the email you are logged in with.'],
                ]);
            }
        }
        $userInvites = InvitePartnerService::fetchUsersFromEmail($request->email);
        [$errorMessages, $companyPartner] = InvitePartnerService::sendUsersInvite(
            $vendor,
            $companyId,
            $request->initiator,
            $loggedUser->id,
            $userInvites,
            $applicationSource
        );
        if (count($errorMessages)) {
            Log::debug('ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::' . json_encode($errorMessages));
        }
        InvitePartnerService::appendCompanyAvatar(collect($companyPartner));
        InvitePartnerService::appendUserObjectsToCollection(collect($companyPartner));

        return InvitationResource::collection($companyPartner);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/partner/{vendor}/open-invitation",
     *     operationId="getOpenInvitation",
     *     tags={"InvitePartnerController"},
     *     summary="Get open invitation for Partner Portal",
     *     description="Get open invitation for Partner Portal",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/partner/{vendor}/open-invitation
    // Bearer token needed
    public function getOpenInvitation(Company $vendor): OpenInvitationResource
    {
        CompanyService::validateCompanyIsVendor($vendor->id);
        CompanyService::validateCompanyPartnerPageIsActive($vendor);

        $invite = InvitePartnerService::getOpenInvitation($vendor);

        return new OpenInvitationResource($invite);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/partner/{vendor}/invite/resend-invite/{inviteId}",
     *     operationId="invite/reSendInvite",
     *     tags={"InvitePartnerController"},
     *     summary="Resend invite",
     *     description="Resend invite",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: PUTreSendInviteupdate-partnership
    // http://127.0.0.1:8000/api/v1/partner/{vendor}/invite/resend-invite/{inviteId}
    /*
    Bearer token needed*/
    public function reSendInvite(Company $vendor, string $invite): JsonResponse
    {
        try {
            CompanyService::validateCompanyIsVendor($vendor->id);
            CompanyService::validateCompanyPartnerPageIsActive($vendor);

            InvitePartnerService::reSendUsersInvite($invite);

            return response()->json(['message' => 'Invitation resent successfully.']);
        } catch (\Throwable $th) {
            Log::debug('ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::' . $th->getMessage());

            return response()->json(['message' => config('genericMessages.error.INVITE_NOT_SENT')], 500);
        }
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/partner/{vendor}/invite/resendAllInvite",
     *     operationId="invite/resendAllInvite",
     *     tags={"InvitePartnerController"},
     *     summary="Resend all invite",
     *     description="Resend all invite",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: PUTreSendInviteupdate-partnership
    // http://127.0.0.1:8000/api/v1/partner/{vendor}/invite/resendAllInvite
    /*
    Bearer token needed*/
    public function resendAllInvite(Company $vendor): JsonResponse
    {
        try {
            CompanyService::validateCompanyIsVendor($vendor->id);
            CompanyService::validateCompanyPartnerPageIsActive($vendor);

            $allInvites = MSPFollowingPartner::select('company_partners.id')
                ->where('followed_partner_id', $vendor->id)
                ->where('status', 'invited')
                ->whereNull('company_partners.deleted_at')
                ->whereNotNull('company_partners.email')->get();

            foreach ($allInvites as $invite) {
                InvitePartnerService::reSendUsersInvite($invite->id);
            }

            return response()->json(['message' => config('genericMessages.success.INVITE_WERE_SENT')]);
        } catch (\Throwable $th) {
            Log::debug('ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::' . $th->getMessage());

            return response()->json(['message' => config('genericMessages.error.INVITE_NOT_SENT')], 500);
        }
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/partner/{vendor}/invite/resendBulkInvite",
     *     operationId="invite/resendBulkInvite",
     *     tags={"InvitePartnerController"},
     *     summary="Resend bulk invite",
     *     description="Resend bulk invite",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: PUTreSendInviteupdate-partnership
    // BODY
    /*{
        'invites':'required|integer|array'
    }
    Bearer token needed*/
    public function resendBulkInvite(Company $vendor, Request $request): JsonResponse
    {
        try {
            CompanyService::validateCompanyIsVendor($vendor->id);
            CompanyService::validateCompanyPartnerPageIsActive($vendor);

            $allInvites = $request->input('invites');

            foreach ($allInvites as $invite) {
                InvitePartnerService::reSendUsersInvite($invite);
            }

            return response()->json(['message' => config('genericMessages.success.INVITE_WERE_SENT')]);
        } catch (\Throwable $th) {
            Log::debug('ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::' . $th->getMessage());

            return response()->json(['message' => config('genericMessages.error.INVITE_NOT_SENT')], 500);
        }
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/partner/{vendor}/invite/csv",
     *     operationId="partner/sendInvite/csv",
     *     tags={"InvitePartnerController"},
     *     summary="Creates an invitation between companies for conecting in a PRM to all csv emails",
     *     description="Creates an invitation between companies for conecting in a PRM to all csv emails",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: Post
    // http://127.0.0.1:8000/api/v1/partner/{vendor}/invite/csv
    // needs Bearer Token
    // BODY
    /*
     * {
            "file": "***CSV FILE*****",
        }
     */
    public function sendInviteFromCsv(InvitePartnerCsvRequest $request, Company $vendor)
    {
        $loggedUser = AuthService::getAuthUser();
        if ($request->hasFile('file')) {
            $csvData = UtilityService::csvToCollection($request->file('file'), array_keys(config('common.partner.invites.csv_headers')));
            $emails = $csvData->pluck(config('common.partner.invites.csv_headers.emailAddress'))->filter()->unique()->toArray();
            if (count($emails) == 0) {
                $responseMessage['file_error'] = 'The file appears to be blank, please select a new file and try again.';

                return response()->json($responseMessage);
            }
            $successEmails = [];
            $errors = ValidationService::blackListDomainListValidation($emails, false);
            $emails = array_diff($emails, $errors);
            foreach ($emails as $email) {
                if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    $successEmails[] = $email;
                } else {
                    $errors[] = $email;
                }
            }
        }
        dispatch(new InvitePartnerCsvJob(
            $successEmails,
            $vendor,
            $loggedUser->company_id, PartnerPortalInvitationInitiator::Partner,
            $loggedUser->id,
            PartnerApplicationSource::VendorInvite
        ));
        $countSuccessEmails = count($successEmails);
        $countErrors = count($errors);
        $message = ($countSuccessEmails > 1)
            ? 'Successfully sent ' . $countSuccessEmails . ' invitations. '
            : 'Successfully sent ' . $countSuccessEmails . ' invitation. ';
        $errorMessage = ($countErrors > 0)
            ? 'However, ' . (
                ($countErrors > 1)
                    ? $countErrors . ' invitations'
                    : $countErrors . ' invitation'
            ) . ' could not be sent. Please review your file and try again.'
            : '';
        $responseMessage['success_message'] = $message . $errorMessage;
        $responseMessage['successful'] = $successEmails;
        $responseMessage['errors_found'] = $errors;

        return response()->json($responseMessage);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/partner/{vendor}/remove-invite",
     *     operationId="partner/removeInviteUser",
     *     tags={"InvitePartnerController"},
     *     summary="Remove Invite user to follow Partner Portal",
     *     description="Remove Invite user to follow Partner Portal",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: Post
    // http://127.0.0.1:8000/api/v1/partner/{vendor}/remove-invite
    // needs Bearer Token
    // BODY
    /*
     * {
            "requested_by_msp": "sometimes|required|boolean",
            "request_ids": "required|NumericArray|exists:company_partners,id"
            "rejected_reason": "required|AllowedValues",
            "rejected_reason_other": "required_if:other|string|Profanity"
        }
     */
    public function removeInvite(InvitePartnerDeleteRequest $request, Company $vendor): JsonResponse
    {
        $successRequest = [];
        $errors = [];
        foreach ($request->request_ids as $requestId) {
            $canDelete = false;
            $mspFollowingPartner = MSPFollowingPartner::select(
                'id', 'followed_partner_id', 'follower_partner_id', 'status', 'deleted_at'
            )->firstWhere('id', $requestId);
            $canDelete = !empty($mspFollowingPartner) && is_null($mspFollowingPartner->deleted_at) &&
                $mspFollowingPartner->followed_partner_id !== config('custom.channel_program_company.id');
            $loggedUser = AuthService::getAuthUser();
            if ($canDelete) {
                $mspFollowingPartner->rejected_by = $loggedUser->id;
                $mspFollowingPartner->rejected_at = Carbon::now();
                $mspFollowingPartner->rejected_reason = $request->rejected_reason;
                $mspFollowingPartner->rejected_reason_other = $request->rejected_reason_other;
                $mspFollowingPartner->deleted_at = Carbon::now();
                $mspFollowingPartner->save();
                $successRequest[] = $requestId;
            } else {
                $errors[] = $requestId;
            }
        }
        $countSuccessRequest = count($successRequest);
        $countErrors = count($errors);
        $message = ($countSuccessRequest > 1)
            ? 'Successfully deleted ' . $countSuccessRequest . ' invitations. '
            : 'Successfully deleted ' . $countSuccessRequest . ' invitation. ';
        $errorMessage = ($countErrors > 0)
            ? (($countErrors > 1)
                ? $countErrors . ' invitations not found.'
                : $countErrors . ' invitation  not found.')
            : '';
        if ($countErrors == 0) {
            $responseMessage['success_message'] = $message;
        } else {
            $responseMessage['success_message'] = $message . $errorMessage;
        }

        return response()->json($responseMessage);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/partner/remove-invite",
     *     operationId="partner/removeInviteByIds",
     *     tags={"InvitePartnerController"},
     *     summary="Remove Invite from PRM using the company ids",
     *     description="Remove Invite from PRM using the company ids",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: Post
    // http://127.0.0.1:8000/api/v1/partner/remove-invite
    // needs Bearer Token
    // BODY
    /*
     * {
            "msp_id": "required|numeric|exists:companies,id",
            "vendor_id": "required|numeric|exists:companies,id",
            "requested_by_msp": "sometimes|required|boolean"
        }
     */
    public function removeInviteByIds(InvitePartnerDeleteByIdsRequest $request): JsonResponse
    {
        $canDelete = false;
        $partnership = MSPFollowingPartner::where('follower_partner_id', $request->msp_id)
            ->where('followed_partner_id', $request->vendor_id)
            ->where('followed_partner_id', '<>', config('custom.channel_program_company.id'))
            ->whereNull('deleted_at')
            ->get();

        if ($partnership->count() > 0) {
            $loggedUser = AuthService::getAuthUser();
            if (!AuthService::userIsSuperAdmin($loggedUser)) {
                if ($request->has('requested_by_msp') && $request->requested_by_msp) {
                    $msp = Company::findOrFail($request->msp_id);
                    CompanyService::validateCompanyIsMSP($msp);
                } else {
                    $vendor = Company::findOrFail($request->vendor_id);
                    CompanyService::validateCompanyIsVendor($vendor);
                }
            }
            $canDelete = true;
        }

        if ($canDelete) {
            MSPFollowingPartner::where('follower_partner_id', $request->msp_id)
                ->where('followed_partner_id', $request->vendor_id)
                ->where('followed_partner_id', '<>', config('custom.channel_program_company.id'))
                ->whereNull('deleted_at')
                ->update([
                    'rejected_by' => $loggedUser->id,
                    'rejected_at' => Carbon::now(),
                    'deleted_at' => Carbon::now(),
                ]);

            return response()->json();
        }

        throw ValidationException::withMessages([
            config('genericMessages.error.MSP_NOT_PARTNER_OR_INVITED'),
        ]);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/partner/{vendor}/invites",
     *     operationId="getInvites",
     *     tags={"InvitePartnerController"},
     *     summary="Get invites for Partner Portal",
     *     description="Get invites for Partner Portal",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/partner/{vendor}/invites
    /*
        ?paged=BOOLEAN&page=PAGE&items_per_page
        &order_by=COLUMN_NAME&sort=SORT_VALUE
        &search_word=SEARCH_WORD
        &search[key]=VALUE
     */
    // Bearer token needed
    public function getInvites(GetInvitePartnerRequest $request, Company $vendor): AnonymousResourceCollection
    {
        $query = MSPFollowingPartner::with('partner', 'invitedBy', 'acceptedBy',
            'followerPartner.enumType:id,value')
            ->select('company_partners.*')
            ->where('followed_partner_id', $vendor->id)
            ->whereNull('company_partners.deleted_at')
            ->whereNotNull('company_partners.email');

        $query = UtilityHelper::prepareSearchQuery($query, $request->search, new MSPFollowingPartner(), 'company_partners');
        $query = $this->prepareShowAllQuery($request, $query);
        $result = UtilityHelper::getSearchRequestQueryResults($request, $query);
        if ($result instanceof LengthAwarePaginator) {
            $pageResults = $result->getCollection();
        } else {
            $pageResults = $result;
        }
        InvitePartnerService::appendUserObjectsToCollection($pageResults);
        if ($result instanceof LengthAwarePaginator) {
            $result->setCollection($pageResults);
        } else {
            $result = $pageResults;
        }

        return InvitationResource::collection($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/partner/invitation/{inviteId}/accept",
     *     operationId="invite/acceptInvite",
     *     tags={"InvitePartnerController"},
     *     summary="user accept invite to Partner Portal",
     *     description="user accept invite to Partner Portal",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/partner/invitation/{inviteId}/accept
    // accepted_reason: required|string|AllowedValues
    // accepted_reason_other : required|numeric|exists:lookup_option_values,id
    // Bearer token needed
    public function acceptInvite(AcceptInvitationRequest $request, string $inviteId): InvitationResource
    {
        $loggedInUser = AuthService::getAuthUser();
        $acceptedReason = $request->accepted_reason ?? LookupService::getLookupOptionValueId(
            'partner_invitation_accepting_reasons',
            config('custom.prm.mutual_accept_reason')
        );

        $acceptedReasonOther = $request->accepted_reason_other ?? null;
        // The use of the invitation ID was retained here for compatibility with older invitations.
        $invite = MSPFollowingPartner::where(function ($q) use ($inviteId) {
            if (is_numeric($inviteId)) {
                $q->where('id', $inviteId);
            } else {
                $q->where('token', $inviteId);
            }
        })->whereNull('deleted_at')->first();
        if (empty($invite)) {
            throw ValidationException::withMessages([
                config('genericMessages.error.PARTNER_INVITATION_NOT_FOUND'),
            ]);
        }
        // Validating if user's company is MSP when the invite comes from the Vendor
        if ($invite->initiator === PartnerPortalInvitationInitiator::Partner) {
            CompanyService::validateCompanyIsMSP($loggedInUser->company_id);
        }
        $invite = match ($invite->status) {
            PartnerPortalInvitationStatus::Accepted => throw ValidationException::withMessages([
                config('genericMessages.error.PARTNER_INVITATION_IS_ALREADY_ACCEPTED'),
            ]),
            PartnerPortalInvitationStatus::Rejected => throw ValidationException::withMessages([
                config('genericMessages.error.PARTNER_INVITATION_IS_REJECTED'),
            ]),
            PartnerPortalInvitationStatus::Invited => InvitePartnerService::acceptInvitedInvitation(
                $invite,
                $loggedInUser,
                $acceptedReason,
                $acceptedReasonOther,
                $request->follower_partner_ids
            ),
            PartnerPortalInvitationStatus::Requested => InvitePartnerService::acceptRequestedInvitation(
                $invite,
                $loggedInUser,
                $acceptedReason,
                $acceptedReasonOther,
            ),
            default => throw ValidationException::withMessages([
                ['ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::Logic not implemented for::' . $invite->status],
            ])
        };

        return new InvitationResource($invite);
    }

    private function prepareShowAllQuery($request, $query)
    {
        $query->with([
            'followerPartner.avatar', 'partner.avatar', 'invitedBy',
        ])->when($request->has('search_word'), function ($q) use ($request) {
            $q->where(function ($q) use ($request) {
                $q->whereRaw('lower(company_partners.email) like \'%' . strtolower($request->search_word) . '%\'')
                    ->orWhereHas('partner', function ($q) use ($request) {
                        $q->where('name', 'ilike', '%' . $request->search_word . '%')
                            ->orWhere('profile_vendor_handle', 'ilike', '%' . $request->search_word . '%')
                            ->orWhere('friendly_url', 'ilike', '%' . $request->search_word . '%');
                    })->OrWhereHas('followerPartner', function ($q) use ($request) {
                        $q->where('name', 'ilike', '%' . $request->search_word . '%')
                            ->orWhere('profile_vendor_handle', 'ilike', '%' . $request->search_word . '%')
                            ->orWhere('friendly_url', 'ilike', '%' . $request->search_word . '%');
                    })->OrWhereHas('invitedBy', function ($query) use ($request) {
                        $query->whereRaw('CONCAT(first_name, \' \', last_name) ilike \'%' . $request->search_word . '%\'')
                            ->orWhere('friendly_url', 'ilike', '%' . $request->search_word . '%')
                            ->orWhere('handle', 'ilike', '%' . $request->search_word . '%');
                    });
            });
        });
        $query->when(!$request->has('order_by'), function ($q) use ($request) {
            $q->orderBy('company_partners.created_at', $request->sort ?? 'DESC');
        }, function ($q) use ($request) {
            if ($request->order_by == InviteSortingOptions::ALPHANUMERIC) {
                $q->leftJoin('users as invited_by_user', 'company_partners.invited_by', '=', 'invited_by_user.id')
                    ->groupBy('invited_by_user.id', 'company_partners.id');
                $q->orderBy('invited_by_user.first_name', $request->sort ?? 'asc')
                    ->orderBy('company_partners.email', $request->sort ?? 'asc');
                $request->offsetUnset('order_by');
            } elseif ($request->order_by == InviteSortingOptions::SOURCE) {
                $q->orderBy('company_partners.source', $request->sort ?? 'asc');
                $request->offsetUnset('order_by');
            } elseif ($request->order_by == InviteSortingOptions::COMPANYNAME) {
                $q->leftJoin('companies as follower_partner', 'company_partners.follower_partner_id', '=', 'follower_partner.id')
                    ->groupBy('follower_partner.id', 'company_partners.id');
                $q->orderBy('follower_partner.name', $request->sort ?? 'asc');
                $request->offsetUnset('order_by');
            } elseif ($request->order_by == 'invited_at') {
                $q->orderBy('company_partners.updated_at', $request->sort ?? 'asc');
                $request->offsetUnset('order_by');
            } else {
                $q->orderBy('company_partners.created_at', $request->sort ?? 'DESC');
            }
        });

        return $query;
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/partner/invitation/{inviteId}/update-partnership",
     *     operationId="invite/updatePartnership",
     *     tags={"InvitePartnerController"},
     *     summary="user update partnership in Partner Portal",
     *     description="user update partnership in Partner Portal",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: PUT
    // http://127.0.0.1:8000/api/v1/partner/invitation/{inviteId}/update-partnership
    // BODY
    /*{
        'accepted_reason':'required|integer|exists:lookup_option_values,id',
        'accepted_reason_other':"Required_If:lookup_option_values->show_answer_option=true|string|profinity'
    }
    Bearer token needed*/
    public function updatePartnership(AcceptInvitationRequest $request, $inviteId): InvitationResource
    {
        $invite = MSPFollowingPartner::where('id', $inviteId)
            ->where('status', PartnerPortalInvitationStatus::Accepted)
            ->whereNull('deleted_at')->first();
        if (!$invite) {
            throw ValidationException::withMessages([
                config('genericMessages.error.PARTNER_INVITATION_NOT_FOUND'),
            ]);
        }
        $invite = InvitePartnerService::updateAcceptedInvitationPartnership($invite, $request->validated());

        return new InvitationResource($invite);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/partner/invitation/{vendor}/accept-multiple",
     *     operationId="invite/acceptMultipleInvite",
     *     tags={"InvitePartnerController"},
     *     summary="user accept multiple invite to Partner Portal",
     *     description="user accept multiple invite to Partner Portal",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: PUT
    // http://127.0.0.1:8000/api/v1/partner/invitation/{inviteId}/accept
    // {
    //      'invite_ids':'required|NumericArray|exists:company_partners,id',
    //      'invite_by_ids':'required|NumericArray|exists:users,id',
    //      'accepted_reason':'required|integer|exists:lookup_option_values,id',
    //      'accepted_reason_other':"Required_If:lookup_option_values->show_answer_option=true|string|profinity'
    // }
    // Bearer token needed
    public function acceptMultipleInvite(AcceptMultipleInvitationRequest $request): AnonymousResourceCollection
    {
        $loggedInUser = AuthService::getAuthUser();
        $invites = MSPFollowingPartner::select('id', 'followed_partner_id', 'follower_partner_id', 'status',
            'deleted_at')->whereIn('id', $request->invite_ids)->whereNull('deleted_at')
            ->whereNotIn('status', [PartnerPortalInvitationStatus::Accepted, PartnerPortalInvitationStatus::Rejected])
            ->get();
        $inviteReason['accepted_reason'] = $request->accepted_reason;
        if ($request->has('accepted_reason') && $request->accepted_reason_other != '') {
            $inviteReason['accepted_reason_other'] = $request->accepted_reason_other;
        }
        foreach ($invites as $invite) {
            InvitePartnerService::updateValidatedInvite($invite, $loggedInUser, $inviteReason);
        }

        return InvitationResource::collection($invites);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/partner/{vendor}/invite/filters",
     *     operationId="partner/invite/filters",
     *     tags={"InvitePartnerController"},
     *     summary="Returns the invites filters",
     *     description="Returns the invites filters",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/partner/{vendor}/invite/filters
    // needs Bearer Token
    public function filter(Company $vendor)
    {
        $filters = InvitePartnerFilter::asArray();
        $query = MSPFollowingPartner::where('status', PartnerPortalInvitationStatus::Invited)
            ->where('initiator', PartnerPortalInvitationInitiator::Partner)
            ->where('followed_partner_id', $vendor->id)
            ->whereNotNull('source')->whereNull('follower_partner_id')
            ->orderBy('source', 'ASC')
            ->distinct()
            ->pluck('source')->toArray();
        if (count($query) > 0) {
            foreach ($query as $source) {
                $filters['source']['items'][$source] = $source;
            }
        }

        return response()->json([
            'filters' => $filters,
        ]);
    }
}
