<?php

namespace App\Http\Controllers\Api\Plaid\Company;

use App\Helpers\UtilityHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Plaid\Alert\ShowAllAlertNotificationsRequest;
use App\Http\Requests\Plaid\Alert\StoreAlertSettingsRequest;
use App\Http\Resources\Plaid\Alert\PlaidAlertNotificationResource;
use App\Http\Resources\Plaid\Alert\PlaidAlertSettingsResource;
use App\Log;
use App\Models\Company\Company;
use App\Services\Plaid\PlaidAlertService;
use App\Services\Plaid\PlaidService;
use Illuminate\Database\QueryException;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

class PlaidCompanyAlertController extends Controller
{
    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/plaid/{company}/alert/settings",
     *     operationId="plaid/alert/settings/save",
     *     tags={"PlaidCompanyAlertController"},
     *     summary="Save Notify Alert Settings",
     *     description="Save Notify Alert Settings information of the company",
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/plaid/{company}/alert/settings
    // BODY
    /*{}
    / Bearer Token NEEDED*/
    public function storeAlertSettings(Company $company, StoreAlertSettingsRequest $request)
    {
        PlaidService::validateIfUserBelongToCompany($company);

        try {
            $data = $request->validated();

            DB::transaction(function () use ($data, $company) {
                foreach ($data['alerts'] as $alert) {
                    $company->plaidNotifyAlerts()->updateOrCreate(
                        [
                            'company_id' => $company->id,
                            'type_id' => $alert['type_id'],
                        ],
                        [
                            'period_id' => $alert['period_id'],
                            'active' => $alert['active'],
                        ]
                    );
                }

                $company->plaidNotifyRecipient()->updateOrCreate([
                    'company_id' => $company->id,
                ],  $data['recipients']);
            });

            $company->refresh();
            $company->load(PlaidAlertSettingsResource::ALL_RELATIONSHIP_LOAD);

            return new PlaidAlertSettingsResource($company);
        } catch (QueryException $ex) {
            Log::error('ERROR::' . __CLASS__ . '::' . __FUNCTION__
                . '::' . $ex->getMessage() . '::' . $ex->getTraceAsString());

            throw ValidationException::withMessages([
                'ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::' . $ex->getMessage(),
            ]);
        }
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\GET(
     *     path="/api/v1/plaid/{company}/alert/settings",
     *     operationId="plaid/alert/settings/showAll",
     *     tags={"PlaidCompanyAlertController"},
     *     summary="Get current alert settings",
     *     description="Get current alert settings of the company",
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/plaid/{company}/alert/settings
    // BODY
    /*{}
    / Bearer Token NEEDED*/
    public function getAlertSettings(Company $company): PlaidAlertSettingsResource
    {
        PlaidService::validateIfUserBelongToCompany($company);
        $company->load(PlaidAlertSettingsResource::ALL_RELATIONSHIP_LOAD);

        return new PlaidAlertSettingsResource($company);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\GET(
     *     path="/api/v1/plaid/{company}/alert/notifications",
     *     operationId="plaid/alert/notifications/showAll",
     *     tags={"PlaidCompanyAlertController"},
     *     summary="Get Company Alert Notifications",
     *     description="Get Get Company Alert Notifications for overivew page",
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/plaid/{company}/alert/notifications
    // BODY
    /*{}
    / Bearer Token NEEDED*/
    public function showAllAlertNotifications(Company $company, ShowAllAlertNotificationsRequest $request): AnonymousResourceCollection
    {
        PlaidService::validateIfUserBelongToCompany($company);
        $query = PlaidAlertService::getCompanyNotifications($company, $request);

        $notifications = UtilityHelper::getSearchRequestQueryResults($request, $query);

        return PlaidAlertNotificationResource::collection($notifications);
    }
}
