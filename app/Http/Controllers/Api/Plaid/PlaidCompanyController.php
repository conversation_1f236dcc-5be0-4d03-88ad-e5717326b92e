<?php

namespace App\Http\Controllers\Api\Plaid;

use App\Enums\Company\CompanyType as CompanyTypeEnum;
use App\Enums\MyStackPartnerStatus;
use App\Enums\Plaid\Filters\AlertNotificationFilters;
use App\Enums\Plaid\Filters\BreakDownFilters;
use App\Enums\Plaid\Filters\TransactionFilters;
use App\Facades\Services\FilterService;
use App\Helpers\UtilityHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\AdminSearchRequest;
use App\Http\Requests\Plaid\Bulk\BulkBankAccountRequest;
use App\Http\Requests\Plaid\Bulk\BulkUpdateBankAccountRequest;
use App\Http\Requests\Plaid\Bulk\SubscriptionBulkUpdateRequest;
use App\Http\Requests\Plaid\Bulk\TransactionBulkUpdateRequest;
use App\Http\Requests\Plaid\Costs\CategoryCostsRequest;
use App\Http\Requests\Plaid\Costs\SubscriptionCostsRequest;
use App\Http\Requests\Plaid\Costs\VendorCostsRequest;
use App\Http\Requests\Plaid\ExpensesBreakdownRequest;
use App\Http\Requests\Plaid\ExpensesOvertimeRequest;
use App\Http\Requests\Plaid\ExpensesOverviewBreakdownRequest;
use App\Http\Requests\Plaid\ExpensesSummaryRequest;
use App\Http\Requests\Plaid\ExportExpensesToCsvRequest;
use App\Http\Requests\Plaid\Overview\ExpensesContractCostsOvertimeRequest;
use App\Http\Requests\Plaid\Overview\ExpensesOverviewOvertimeRequest;
use App\Http\Requests\Plaid\ShowAllCompanySubscriptionsRequest;
use App\Http\Requests\Plaid\ShowAllCompanySubscriptionsUpcomingExpensesRequest;
use App\Http\Requests\Plaid\ShowAllCompanyTransactionsRequest;
use App\Http\Requests\Plaid\ShowAllLinkedAccountsRequest;
use App\Http\Requests\Plaid\ShowAllSubscriptionTransactionsRequest;
use App\Http\Requests\Plaid\Stack\ShowAllStackNotLinkedExpensesRequest;
use App\Http\Requests\Plaid\Stack\ShowAllStackNotLinkedExpensesSuggestedRequest;
use App\Http\Requests\Plaid\SubscriptionUpdateRequest;
use App\Http\Requests\Plaid\TransactionsFiltersRequest;
use App\Http\Requests\Plaid\TransactionUpdateRequest;
use App\Http\Requests\Plaid\UpdateBankAccountRequest;
use App\Http\Resources\Plaid\PlaidBankAccountResource;
use App\Http\Resources\Plaid\PlaidSubscriptionResource;
use App\Http\Resources\Plaid\PlaidTransactionResource;
use App\Http\Resources\Plaid\Search\SearchSubscriptionResource;
use App\Http\Resources\Plaid\Search\SearchTransactionResource;
use App\Http\Resources\Plaid\ShowAllSubscriptionsUpcomingExpensesResource;
use App\Http\Resources\Plaid\Simples\PlaidInstitutionSimpleResource;
use App\Http\Resources\Plaid\Simples\PlaidTransactionSimpleResource;
use App\Http\Resources\Plaid\Stack\StackNotLinkedExpensesResource;
use App\Http\Resources\Plaid\Stack\StackNotLinkedExpensesSuggestionsResource;
use App\Jobs\Plaid\PlaidAccountSync;
use App\Models\Category\Category;
use App\Models\Company\Company;
use App\Models\Contract\ClientProduct;
use App\Models\Contract\Contract;
use App\Models\MyStack\CustomerStack;
use App\Models\MyStack\MyStack;
use App\Models\Plaid\PlaidBankAccount;
use App\Models\Plaid\PlaidSubscription;
use App\Models\Product;
use App\Services\AuthService;
use App\Services\ClientVendor\ClientVendorService;
use App\Services\Contract\ContractService;
use App\Services\ImageService;
use App\Services\MyStack\CompanyClientStackService;
use App\Services\MyStack\MyStackCustomerService;
use App\Services\MyStack\MyStackService;
use App\Services\Plaid\PlaidAlertService;
use App\Services\Plaid\PlaidExpensesService;
use App\Services\Plaid\PlaidService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\QueryException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\StreamedResponse;

class PlaidCompanyController extends Controller
{
    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/plaid/{company}/linked-accounts",
     *     operationId="plaid/company/linked-accounts/showAll",
     *     tags={"PlaidCompanyController"},
     *     summary="Get linked accounts by company",
     *     description="Get linked accounts that have been synced for the company",
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/plaid/{company}/linked-accounts
    // BODY
    /*{}
    / Bearer Token NEEDED*/
    public function showAllLinkedAccounts(Company $company, ShowAllLinkedAccountsRequest $request): AnonymousResourceCollection
    {
        $query = $company->plaidBankAcounts()
            ->select(
                'plaid_bank_accounts.id',
                'plaid_bank_accounts.mask',
                'plaid_bank_accounts.name',
                'plaid_bank_accounts.display_name',
                'plaid_bank_accounts.type',
                'plaid_bank_accounts.last_sync',
                'plaid_bank_accounts.is_syncing',
                'plaid_bank_accounts.plaid_bank_link_id',
                'plaid_institutions.institution_id'
            )->join('plaid_bank_links', 'plaid_bank_links.id', '=', 'plaid_bank_accounts.plaid_bank_link_id')
            ->join('plaid_institutions', 'plaid_institutions.institution_id', '=', 'plaid_bank_links.plaid_institution_id')
            ->when($request->has('search_word'), function ($query) use ($request) {
                $searchWord = '%' . $request->input('search_word') . '%';
                $query->where(function ($query) use ($searchWord) {
                    $query->where('plaid_bank_accounts.display_name', 'ilike', $searchWord)
                        ->orWhere('plaid_bank_accounts.mask', 'ilike', $searchWord);
                });
            });

        $transactions = UtilityHelper::getSearchRequestQueryResults($request, $query);

        return PlaidBankAccountResource::collection($transactions);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/plaid/{company}/search/transactions",
     *     operationId="plaid/company/search/transactions",
     *     tags={"PlaidCompanyController"},
     *     summary="Search transactions",
     *     description="Search company's transactions",
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/plaid/{company}/search/transactions
    // BODY
    /*{}
    / Bearer Token NEEDED*/
    public function searchTransactions(Company $company, AdminSearchRequest $request): AnonymousResourceCollection
    {
        $query = PlaidExpensesService::getAllExpenses($company)
            ->select(
                'plaid_transactions.id',
                'plaid_transactions.description',
                'plaid_transactions.merchant_name',
                'plaid_transactions.amount',
                'plaid_transactions.logo_url',
                'plaid_transactions.product_id',
                'plaid_transactions.productable_type',
                'plaid_transactions.contract_id',
                'plaid_transactions.vendor_id',
                'plaid_transactions.account_id',
                'plaid_transactions.company_id',
            )
            ->join('plaid_bank_accounts', 'plaid_bank_accounts.account_id', '=', 'plaid_transactions.account_id')
            ->leftJoin('companies as vendor_companies', 'vendor_companies.id', '=', 'plaid_transactions.vendor_id')
            ->productJoin($company)
            ->leftJoin('contracts', 'contracts.id', '=', 'plaid_transactions.contract_id')
            ->when($request->has('search_word'), function ($query) use ($request) {
                $searchWord = '%' . $request->input('search_word') . '%';
                $query->where(function ($query) use ($searchWord) {
                    $query->where('products.name', 'ilike', $searchWord)
                        ->orWhere('plaid_transactions.amount', 'ilike', $searchWord)
                        ->orWhere('plaid_transactions.merchant_name', 'ilike', $searchWord)
                        ->orWhere('plaid_transactions.description', 'ilike', $searchWord)
                        ->orWhere('vendor_companies.name', 'ilike', $searchWord)
                        ->orWhere('contracts.name', 'ilike', $searchWord);
                });
            })->with(SearchTransactionResource::ALL_RELATIONSHIP_LOAD);

        $transactions = UtilityHelper::getSearchRequestQueryResults($request, $query);

        return SearchTransactionResource::collection($transactions);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/plaid/{company}/search/subscriptions",
     *     operationId="plaid/company/search/subscriptions",
     *     tags={"PlaidCompanyController"},
     *     summary="Search subscriptions",
     *     description="Search company's subscriptions",
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/plaid/{company}/search/subscriptions
    // BODY
    /*{}
    / Bearer Token NEEDED*/
    public function searchSubscriptions(Company $company, AdminSearchRequest $request): AnonymousResourceCollection
    {
        $query = PlaidExpensesService::getSubscriptions($company)
            ->select(
                'plaid_subscriptions.id',
                'plaid_subscriptions.merchant_name',
                'plaid_subscriptions.description',
                'plaid_subscriptions.last_amount',
                'plaid_subscriptions.product_id',
                'plaid_subscriptions.productable_type',
                'plaid_subscriptions.contract_id',
                'plaid_subscriptions.vendor_id',
                'plaid_subscriptions.account_id',
                'plaid_subscriptions.company_id',
            )
            ->join('plaid_bank_accounts', 'plaid_bank_accounts.account_id', '=', 'plaid_transactions.account_id')
            ->leftJoin('companies as vendor_companies', 'vendor_companies.id', '=', 'plaid_transactions.vendor_id')
            ->productJoin($company)
            ->leftJoin('contracts', 'contracts.id', '=', 'plaid_transactions.contract_id')
            ->when($request->has('search_word'), function ($query) use ($request) {
                $query->where(function ($query) use ($request) {
                    $searchWord = '%' . $request->input('search_word') . '%';
                    $query->where('products.name', 'ilike', $searchWord)
                        ->orWhere('plaid_subscriptions.last_amount', 'ilike', $searchWord)
                        ->orWhere('plaid_subscriptions.merchant_name', 'ilike', $searchWord)
                        ->orWhere('plaid_subscriptions.description', 'ilike', $searchWord)
                        ->orWhere('vendor_companies.name', 'ilike', $searchWord)
                        ->orWhere('contracts.name', 'ilike', $searchWord);
                });
            })->with(SearchSubscriptionResource::ALL_RELATIONSHIP_LOAD);

        $transactions = UtilityHelper::getSearchRequestQueryResults($request, $query);

        return SearchSubscriptionResource::collection($transactions);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/plaid/{company}/count",
     *     operationId="plaid/company/count",
     *     tags={"PlaidCompanyController"},
     *     summary="Get company count info",
     *     description="Get company count about subscriptions, transactions, bank links, and bank accounts ",
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/plaid/{company}/count
    // BODY
    /*{}
    / Bearer Token NEEDED*/
    public function count(Company $company): JsonResponse
    {
        PlaidService::validateIfUserBelongToCompany($company);
        $startDate = Carbon::now()->startOfDay()->subMonths(12);
        $endDate = Carbon::now()->endOfDay();

        return response()->json(
            [
                'subscriptions' => $company->plaidSubscriptions()->count(),
                'transactions' => $company->plaidTransactions()
                    ->whereBetween('plaid_transactions.date', [$startDate, $endDate])
                    ->count(),
                'bank_links' => $company->plaidBankLinks()->count(),
                'bank_accounts' => $company->plaidBankLinks()->withCount('accounts')->get()->sum('accounts_count'),
            ]
        );
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/plaid/{company}/expenses/overview/summary",
     *     operationId="plaid/expenses/overview/summary",
     *     tags={"PlaidCompanyController"},
     *     summary="Get Expenses overview Summary",
     *     description="Get Expenses overview Summary with details about stack, transactions, contracts and all transactions",
     *
     *     @OA\Parameter(
     *         name="start_date",
     *         in="query",
     *         description="Start date for the summary period",
     *         required=true,
     *
     *         @OA\Schema(
     *             type="string",
     *             format="date"
     *         )
     *     ),
     *
     *     @OA\Parameter(
     *         name="end_date",
     *         in="query",
     *         description="End date for the summary period",
     *         required=true,
     *
     *         @OA\Schema(
     *             type="string",
     *             format="date"
     *         )
     *     ),
     *
     *     @OA\Parameter(
     *         name="category[]",
     *         in="query",
     *         description="Array of category IDs to filter by",
     *         required=false,
     *
     *         @OA\Schema(
     *             type="array",
     *
     *             @OA\Items(type="integer")
     *         )
     *     ),
     *
     *     @OA\Parameter(
     *         name="sub_category[]",
     *         in="query",
     *         description="Array of sub-category IDs to filter by",
     *         required=false,
     *
     *         @OA\Schema(
     *             type="array",
     *
     *             @OA\Items(type="integer")
     *         )
     *     ),
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/plaid/{company}/expenses/overview/summary
    // BODY
    /*{}
    / Bearer Token NEEDED*/
    public function expenseOverviewSummary(Company $company, ExpensesSummaryRequest $request): JsonResponse
    {
        PlaidService::validateIfUserBelongToCompany($company);

        $startDate = Carbon::parse($request->start_date);
        $endDate = Carbon::parse($request->end_date);

        $prevStartDate = $startDate->copy()->subDays($startDate->diffInDays($endDate));
        $prevEndDate = $startDate->copy();

        $currentDateRange = [$startDate, $endDate];
        $prevDateRange = [$prevStartDate, $prevEndDate];

        // Get category and sub-category filters
        $categoryIds = $request->input('category', []);
        $subCategoryIds = $request->input('sub_category', []);

        // Get expenses data with filters
        $allExpensesQuery = PlaidExpensesService::getAllExpenses($company, $currentDateRange)
            ->when(!empty($categoryIds), function ($query) use ($categoryIds) {
                $query->whereIn('plaid_transactions.category_id', $categoryIds);
            })
            ->when(!empty($subCategoryIds), function ($query) use ($subCategoryIds) {
                $query->whereIn('plaid_transactions.sub_category_id', $subCategoryIds);
            });
        $allExpensesAmount = $allExpensesQuery->sum('amount');

        $allExpensesPrevQuery = PlaidExpensesService::getAllExpenses($company, $prevDateRange)
            ->when(!empty($categoryIds), function ($query) use ($categoryIds) {
                $query->whereIn('plaid_transactions.category_id', $categoryIds);
            })
            ->when(!empty($subCategoryIds), function ($query) use ($subCategoryIds) {
                $query->whereIn('plaid_transactions.sub_category_id', $subCategoryIds);
            });
        $allExpensesPrevAmount = $allExpensesPrevQuery->sum('amount');

        $subscriptionExpensesQuery = PlaidExpensesService::getSubscriptionExpenses($company, $currentDateRange)
            ->when(!empty($categoryIds), function ($query) use ($categoryIds) {
                $query->whereIn('plaid_subscriptions.category_id', $categoryIds);
            })
            ->when(!empty($subCategoryIds), function ($query) use ($subCategoryIds) {
                $query->whereIn('plaid_subscriptions.sub_category_id', $subCategoryIds);
            });
        $subscriptionExpensesAmount = $subscriptionExpensesQuery->sum('amount');

        $subscriptionPrevExpensesQuery = PlaidExpensesService::getSubscriptionExpenses($company, $prevDateRange)
            ->when(!empty($categoryIds), function ($query) use ($categoryIds) {
                $query->whereIn('plaid_subscriptions.category_id', $categoryIds);
            })
            ->when(!empty($subCategoryIds), function ($query) use ($subCategoryIds) {
                $query->whereIn('plaid_subscriptions.sub_category_id', $subCategoryIds);
            });
        $subscriptionPrevExpensesAmount = $subscriptionPrevExpensesQuery->sum('amount');

        $stackExpensesQuery = PlaidExpensesService::getMyStackExpenses($company, $currentDateRange)
            ->when(!empty($categoryIds), function ($query) use ($categoryIds) {
                $query->whereIn('plaid_transactions.category_id', $categoryIds);
            })
            ->when(!empty($subCategoryIds), function ($query) use ($subCategoryIds) {
                $query->whereIn('plaid_transactions.sub_category_id', $subCategoryIds);
            });
        $stackExpensesAmount = $stackExpensesQuery->sum('amount');

        $prevStackExpensesQuery = PlaidExpensesService::getMyStackExpenses($company, $prevDateRange)
            ->when(!empty($categoryIds), function ($query) use ($categoryIds) {
                $query->whereIn('plaid_transactions.category_id', $categoryIds);
            })
            ->when(!empty($subCategoryIds), function ($query) use ($subCategoryIds) {
                $query->whereIn('plaid_transactions.sub_category_id', $subCategoryIds);
            });
        $prevStackExpensesAmount = $prevStackExpensesQuery->sum('amount');

        // Get contract costs data with filters
        $contracts = ContractService::loadContracts(
            $company->enumType->value,
            $company->id,
            $startDate,
            $endDate,
            ['nextPaymentDates', 'category', 'category.parentCategory'],
            categories: $categoryIds,
            subCategories: $subCategoryIds,
        );

        // Calculate current contract costs
        $contractCosts = ContractService::getContractsTotalsInPeriod(
            $company,
            $contracts,
            $startDate,
            $endDate
        );
        $contractCurrentTotal = $contractCosts->sum('total_cost');

        // Calculate previous contract costs
        $contractPrevCosts = ContractService::getContractsTotalsInPeriod(
            $company,
            $contracts,
            $prevStartDate,
            $prevEndDate
        );
        $contractPrevTotal = $contractPrevCosts->sum('total_cost');

        return response()->json(
            [
                'all' => [
                    'amount' => '' . round($allExpensesAmount, 2),
                    'transactions' => $allExpensesQuery->count(),
                    'subscriptions' => PlaidExpensesService::getSubscriptions($company, $currentDateRange)
                        ->when(!empty($categoryIds), function ($query) use ($categoryIds) {
                            $query->whereIn('plaid_subscriptions.category_id', $categoryIds);
                        })
                        ->when(!empty($subCategoryIds), function ($query) use ($subCategoryIds) {
                            $query->whereIn('plaid_subscriptions.sub_category_id', $subCategoryIds);
                        })
                        ->get()
                        ->count(),
                    'percentage_diff' => PlaidExpensesService::getPercentageDiff($allExpensesPrevAmount, $allExpensesAmount),
                ],
                'subscriptions' => [
                    'amount' => '' . round($subscriptionExpensesAmount, 2),
                    'transactions' => $subscriptionExpensesQuery->count(),
                    'percentage_diff' => PlaidExpensesService::getPercentageDiff($subscriptionPrevExpensesAmount, $subscriptionExpensesAmount),
                ],
                'stack' => [
                    'amount' => '' . round($stackExpensesAmount, 2),
                    'transactions' => $stackExpensesQuery->count(),
                    'subscriptions' => PlaidExpensesService::getMyStackSubscriptions($company, $currentDateRange)
                        ->when(!empty($categoryIds), function ($query) use ($categoryIds) {
                            $query->whereIn('plaid_subscriptions.category_id', $categoryIds);
                        })
                        ->when(!empty($subCategoryIds), function ($query) use ($subCategoryIds) {
                            $query->whereIn('plaid_subscriptions.sub_category_id', $subCategoryIds);
                        })
                        ->get()
                        ->count(),
                    'percentage_diff' => PlaidExpensesService::getPercentageDiff($prevStackExpensesAmount, $stackExpensesAmount),
                ],
                'contracts' => [
                    'amount' => '' . round($contractCurrentTotal, 2),
                    'count' => $contracts->count(),
                    'percentage_diff' => PlaidExpensesService::getPercentageDiff($contractPrevTotal, $contractCurrentTotal),
                ],
            ]
        );
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/plaid/{company}/expenses/upcoming-summary",
     *     operationId="plaid/expenses/upcoming-summary",
     *     tags={"PlaidCompanyController"},
     *     summary="Get Upcoming Summary",
     *     description="Get Upcoming Expenses from subscriptions",
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/plaid/{company}/expenses/upcoming-summary
    // BODY
    /*{}
    / Bearer Token NEEDED*/
    public function expensesUpcomingSummary(Company $company): JsonResponse
    {
        PlaidService::validateIfUserBelongToCompany($company);

        $upComingExpenses = PlaidExpensesService::getUpcomingExpenses($company);

        $subscriptions = PlaidExpensesService::getSubscriptions($company)->with('transactions:id,date,amount')->get();

        $previoSubscriptionsAmount = 0;

        foreach ($subscriptions as $subscription) {
            $transactions = $subscription->transactions()
                ->select('plaid_transactions.date', 'plaid_transactions.amount')
                ->orderByDesc('plaid_transactions.date')
                ->limit(2)
                ->get();

            if (isset($transactions[1])) {
                $previoSubscriptionsAmount += $transactions[1]->amount;
            }
        }
        $totalUpcomingAmount = $upComingExpenses->sum('last_amount');

        return response()->json(
            [
                'count' => $upComingExpenses->count(),
                'cost' => '' . $totalUpcomingAmount,
                'upcoming_date' => PlaidExpensesService::getUpcomingExpensesDate($company),
                'percentage_diff' => PlaidExpensesService::getPercentageDiff($previoSubscriptionsAmount, $totalUpcomingAmount),
            ]
        );
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/plaid/{company}/costs/subscriptions",
     *     operationId="plaid/costs/subscriptions",
     *     tags={"PlaidCompanyController"},
     *     summary="Get Subscriptions Costs",
     *     description="Get Subscriptions costs details compared by month or year",
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/plaid/{company}/costs/subscriptions
    // BODY
    /*{}
    / Bearer Token NEEDED*/
    public function subscriptionsCosts(Company $company, SubscriptionCostsRequest $request): JsonResponse
    {
        PlaidService::validateIfUserBelongToCompany($company);

        $currentMonth = Carbon::now()->month;
        $previousMonth = Carbon::now()->subMonth()->month;

        $currentYear = Carbon::now()->year;
        $previousYear = Carbon::now()->subYear()->year;

        $subscriptionCosts = $company->plaidSubscriptions()
            ->selectRaw('
        SUM(CASE
            WHEN EXTRACT(MONTH FROM plaid_transactions.date) = ? AND EXTRACT(YEAR FROM plaid_transactions.date) = ? THEN plaid_transactions.amount
            ELSE 0
        END) AS current_month_sum,
        SUM(CASE
            WHEN EXTRACT(MONTH FROM plaid_transactions.date) = ? AND EXTRACT(YEAR FROM plaid_transactions.date) = ? THEN plaid_transactions.amount
            ELSE 0
        END) AS previous_month_sum,
        SUM(CASE
            WHEN EXTRACT(YEAR FROM plaid_transactions.date) = ? THEN plaid_transactions.amount
            ELSE 0
        END) AS current_year_sum,
        SUM(CASE
            WHEN EXTRACT(YEAR FROM plaid_transactions.date) = ? THEN plaid_transactions.amount
            ELSE 0
        END) AS previous_year_sum', [
                $currentMonth, $currentYear, // Current month
                $previousMonth, $currentYear, // Previous month
                $currentYear, // Current year
                $previousYear, // Previous year
            ])
            ->join('plaid_transactions', 'plaid_transactions.plaid_subscription_id', '=', 'plaid_subscriptions.id')
            ->when($request->stack_only, function ($query) {
                $query->stackOnly();
            })
            ->first();

        return response()->json(
            [
                'month' => [
                    'current' => '' . round($subscriptionCosts->current_month_sum, 2),
                    'previous' => '' . round($subscriptionCosts->previous_month_sum, 2),
                ],
                'year' => [
                    'current' => '' . round($subscriptionCosts->current_year_sum, 2),
                    'previous' => '' . round($subscriptionCosts->previous_year_sum, 2),
                ],
            ]
        );
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/plaid/{company}/costs/category",
     *     operationId="plaid/costs/category",
     *     tags={"PlaidCompanyController"},
     *     summary="Get Categories Costs",
     *     description="Get Categories costs details compared by month or year",
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/plaid/{company}/costs/category
    // BODY
    /*{}
    / Bearer Token NEEDED*/
    public function categoryCosts(Company $company, CategoryCostsRequest $request): JsonResponse
    {
        PlaidService::validateIfUserBelongToCompany($company);

        $categoryCost = $company->plaidTransactions()
            ->selectRaw($this->getSelectCostQueryRaw())
            ->where('plaid_transactions.category_id', $request->get('id'))
            ->first();

        return response()->json(
            [
                'month' => [
                    'current' => $categoryCost->current_month_sum,
                    'previous' => $categoryCost->previous_month_sum,
                ],
                'year' => [
                    'current' => $categoryCost->current_year_sum,
                    'previous' => $categoryCost->previous_year_sum,
                ],
            ]
        );
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/plaid/{company}/costs/vendor",
     *     operationId="plaid/costs/vendor",
     *     tags={"PlaidCompanyController"},
     *     summary="Get Vendor Costs",
     *     description="Get Vendor costs details compared by month or year",
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/plaid/{company}/costs/vendor
    // BODY
    /*{}
    / Bearer Token NEEDED*/
    public function vendorCosts(Company $company, VendorCostsRequest $request): JsonResponse
    {
        PlaidService::validateIfUserBelongToCompany($company);

        $categoryCost = $company->plaidTransactions()
            ->selectRaw($this->getSelectCostQueryRaw())
            ->where('plaid_transactions.vendor_id', $request->get('id'))
            ->first();

        return response()->json(
            [
                'month' => [
                    'current' => $categoryCost->current_month_sum,
                    'previous' => $categoryCost->previous_month_sum,
                ],
                'year' => [
                    'current' => $categoryCost->current_year_sum,
                    'previous' => $categoryCost->previous_year_sum,
                ],
            ]
        );
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/plaid/{company}/expenses/overtime",
     *     operationId="plaid/expenses/overtime",
     *     tags={"PlaidCompanyController"},
     *     summary="Get company expenses over time",
     *     description="Get company expenses over time providing label and value that going to be used by FE chart",
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/plaid/{company}/expenses/overtime
    // BODY
    /*{}
    / Bearer Token NEEDED*/
    public function expensesOverTime(Company $company, ExpensesOvertimeRequest $request): JsonResponse
    {
        PlaidService::validateIfUserBelongToCompany($company);

        $expensesOverTime = $company->plaidTransactions()
            ->selectRaw('EXTRACT(YEAR FROM date) as year, EXTRACT(MONTH FROM date) as month, SUM(amount) as total_expenses')
            ->groupByRaw('EXTRACT(YEAR FROM date), EXTRACT(MONTH FROM date)')
            ->orderByRaw('EXTRACT(YEAR FROM date), EXTRACT(MONTH FROM date)')
            ->where('ignore', false)
            ->when($request->has('year') && !$request->has(['start_date', 'end_date']), function ($query) use ($request) {
                $query->whereYear('date', $request->year);
            })
            ->when($request->has('category_id'), function ($query) use ($request) {
                $query->where('category_id', $request->category_id);
            })->when($request->has('sub_category_id'), function ($query) use ($request) {
                $query->where('sub_category_id', $request->sub_category_id);
            })->when($request->has('plaid_category_id'), function ($query) use ($request) {
                $query->where('plaid_category_id', $request->plaid_category_id);
            })->when($request->has('plaid_sub_category_id'), function ($query) use ($request) {
                $query->where('plaid_sub_category_id', $request->plaid_sub_category_id);
            })->when($request->has('vendor_id'), function ($query) use ($request) {
                $query->where('vendor_id', $request->vendor_id);
            })->when($request->stack_only, function ($query) {
                $query->stackOnly();
            })
            ->when($request->has(['start_date', 'end_date']) && !$request->has('year'), function ($query) use ($request) {
                $query->whereBetween('date', [$request->start_date, $request->end_date]);
            })->get();

        $expensesOverTime = $expensesOverTime->map(function ($expense) {
            return [
                'label' => date('F Y', mktime(0, 0, 0, $expense->month, 1, $expense->year)),
                'value' => round($expense->total_expenses, 2),
            ];
        });

        return response()->json($expensesOverTime);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/plaid/{company}/expenses/overview/overtime",
     *     operationId="plaid/expenses/overview/overtime",
     *     tags={"PlaidCompanyController"},
     *     summary="Get company expenses overview over time",
     *     description="Get company expenses overview over time providing label and value that going to be used by FE chart",
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/plaid/{company}/expenses/overview/overtime
    // BODY
    /*{}
    / Bearer Token NEEDED*/
    public function expensesOverviewOverTime(Company $company, ExpensesOverviewOvertimeRequest $request): JsonResponse
    {
        PlaidService::validateIfUserBelongToCompany($company);

        $year = $request->year;
        $dateRange = $request->has(['start_date', 'end_date']) ? [$request->start_date, $request->end_date] : [];

        $unmappedExpensesQuery = PlaidExpensesService::getUnmappedExpenses($company, $dateRange);
        $unmappedExpences = PlaidExpensesService::getExpencesTransactionsDateFormat($unmappedExpensesQuery, $year, $dateRange);

        $allExpensesStackQuery = PlaidExpensesService::getMyStackExpenses($company);
        $allExpensesStacks = PlaidExpensesService::getExpencesTransactionsDateFormat($allExpensesStackQuery, $year, $dateRange);

        $allExpensesSubscriptions = PlaidExpensesService::getExpencesTransactionsDateFormat($company->plaidSubscriptionTransactions(), $year, $dateRange);

        return response()->json([
            'unmapped_expenses' => $unmappedExpences,
            'subscription_expenses' => $allExpensesSubscriptions,
            'stack_expenses' => $allExpensesStacks,
        ]);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/plaid/{company}/expenses/contract-cost-overtime",
     *     operationId="plaid//expenses/contract-cost-overtime",
     *     tags={"PlaidCompanyController"},
     *     summary="Contract Cost Overtime",
     *     description="Get Contract Cost Overtime",
     *
     *     @OA\Parameter(
     *         name="id",
     *         in="query",
     *         description="Contract ID",
     *         required=false,
     *
     *         @OA\Schema(
     *             type="integer"
     *         )
     *     ),
     *
     *     @OA\Parameter(
     *         name="year",
     *         in="query",
     *         description="Year for filtering (YYYY)",
     *         required=false,
     *
     *         @OA\Schema(
     *             type="integer"
     *         )
     *     ),
     *
     *     @OA\Parameter(
     *         name="start_date",
     *         in="query",
     *         description="Start date for filtering (YYYY-MM-DD)",
     *         required=false,
     *
     *         @OA\Schema(
     *             type="string",
     *             format="date"
     *         )
     *     ),
     *
     *     @OA\Parameter(
     *         name="end_date",
     *         in="query",
     *         description="End date for filtering (YYYY-MM-DD)",
     *         required=false,
     *
     *         @OA\Schema(
     *             type="string",
     *             format="date"
     *         )
     *     ),
     *
     *     @OA\Parameter(
     *         name="category[]",
     *         in="query",
     *         description="Array of category IDs to filter by",
     *         required=false,
     *
     *         @OA\Schema(
     *             type="array",
     *
     *             @OA\Items(type="integer")
     *         )
     *     ),
     *
     *     @OA\Parameter(
     *         name="sub_category[]",
     *         in="query",
     *         description="Array of sub-category IDs to filter by",
     *         required=false,
     *
     *         @OA\Schema(
     *             type="array",
     *
     *             @OA\Items(type="integer")
     *         )
     *     ),
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/plaid/{company}/expenses/contract-cost-overtime
    // ?id=123&year=2023&start_date=2023-01-01&end_date=2023-12-31&category[]=1&category[]=2&sub_category[]=3&sub_category[]=4
    // BODY
    /*{}
    / Bearer Token NEEDED*/
    public function expensesContractCostOvertime(Company $company, ExpensesContractCostsOvertimeRequest $request): JsonResponse
    {
        PlaidService::validateIfUserBelongToCompany($company);

        $year = $request->year;
        $dateRange = $request->has(['start_date', 'end_date']) ? [$request->start_date, $request->end_date] : [];

        // Check if we're filtering by category or sub_category
        $hasCategoryFilter = $request->has('category') && !empty($request->category);
        $hasSubCategoryFilter = $request->has('sub_category') && !empty($request->sub_category);

        // Get contracts based on filters
        $contractsQuery = $company->contracts()
            ->select('contracts.id')
            ->join('categories', 'categories.id', '=', 'contracts.category_id')
            ->when($request->has('id'), function ($query) use ($request) {
                $query->where('contracts.id', $request->id);
            })
            ->when($hasCategoryFilter, function ($query) use ($request) {
                $query->whereIn('categories.parent_id', $request->category);
            })
            ->when($hasSubCategoryFilter, function ($query) use ($request) {
                $query->whereIn('contracts.category_id', $request->sub_category);
            });

        // Get the contracts
        $contracts = $contractsQuery->get();

        if ($contracts->isEmpty()) {
            return response()->json([
                'linked_expenses' => [],
                'contract_costs' => [],
            ]);
        }

        // Get contract IDs
        $contractIds = $contracts->pluck('id')->toArray();

        // Get linked expenses for all filtered contracts
        $linkedExpensesQuery = $company->plaidTransactions()
            ->when($request->has('id'), function ($query) use ($request) {
                $query->where('plaid_transactions.contract_id', $request->id);
            })
            ->when($hasCategoryFilter, function ($query) use ($request) {
                $query->whereIn('plaid_transactions.category_id', $request->category);
            })->when($hasSubCategoryFilter, function ($query) use ($request) {
                $query->whereIn('plaid_transactions.sub_category_id', $request->sub_category);
            });
        $linkedExpenses = PlaidExpensesService::getExpencesTransactionsDateFormat($linkedExpensesQuery, $year, $dateRange);

        // Get next payments for all filtered contracts
        $nextPaymentsQuery = DB::table('contract_next_payment_dates')
            ->join('contracts', 'contracts.id', '=', 'contract_next_payment_dates.contract_id')
            ->join('categories', 'categories.id', '=', 'contracts.category_id')
            ->whereIn('contract_next_payment_dates.contract_id', $contractIds)
            ->selectRaw('EXTRACT(YEAR FROM contract_next_payment_dates.date) as year, EXTRACT(MONTH FROM contract_next_payment_dates.date) as month, SUM(contract_next_payment_dates.cost) as total_expenses')
            ->when($hasCategoryFilter, function ($query) use ($request) {
                $query->whereIn('categories.parent_id', $request->category);
            })
            ->when($hasSubCategoryFilter, function ($query) use ($request) {
                $query->whereIn('contracts.category_id', $request->sub_category);
            })
            // Apply date filters
            ->when($year, function ($query) use ($year) {
                $query->whereYear('contract_next_payment_dates.date', $year);
            })
            ->when(empty(!$dateRange), function ($query) use ($dateRange) {
                $query->whereBetween('contract_next_payment_dates.date', $dateRange);
            })
            ->groupByRaw('EXTRACT(YEAR FROM contract_next_payment_dates.date), EXTRACT(MONTH FROM contract_next_payment_dates.date)')
            ->orderByRaw('EXTRACT(YEAR FROM contract_next_payment_dates.date), EXTRACT(MONTH FROM contract_next_payment_dates.date)');

        $nextPayments = PlaidExpensesService::getLabelvalueFromDateResult($nextPaymentsQuery->get());

        return response()->json([
            'linked_expenses' => $linkedExpenses,
            'contract_costs' => $nextPayments,
        ]);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/plaid/{company}/transactions",
     *     operationId="plaid/transactions/showAll",
     *     tags={"PlaidCompanyController"},
     *     summary="Get company transactions",
     *     description="Get company transactions",
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/plaid/{company}/transactions
    // BODY
    /*{}
    / Bearer Token NEEDED*/
    public function showAllTransactions(Company $company, ShowAllCompanyTransactionsRequest $request): AnonymousResourceCollection
    {
        PlaidService::validateIfUserBelongToCompany($company);

        $query = $company->plaidTransactions()
            ->select(
                'plaid_transactions.id',
                'plaid_transactions.merchant_name',
                'plaid_transactions.date',
                'plaid_transactions.product_id',
                'plaid_transactions.productable_type',
                'plaid_transactions.category_id',
                'plaid_transactions.plaid_category_id',
                'plaid_transactions.contract_id',
                'plaid_transactions.vendor_id',
                'plaid_transactions.sub_category_id',
                'plaid_transactions.account_id',
                'plaid_transactions.description',
                'plaid_transactions.notes',
                'plaid_transactions.amount',
                'plaid_transactions.my_stack_id',
                'plaid_transactions.stackable_type',
                'plaid_transactions.ignore',
                'plaid_transactions.plaid_subscription_id'
            )
            ->join('plaid_bank_accounts', 'plaid_bank_accounts.account_id', '=', 'plaid_transactions.account_id')
            ->leftJoin('categories', 'categories.id', '=', 'plaid_transactions.category_id')
            ->leftJoin('categories as plaid_categories', 'plaid_categories.id', '=', 'plaid_transactions.plaid_category_id')
            ->leftJoin('companies as vendor_companies', 'vendor_companies.id', '=', 'plaid_transactions.vendor_id')
            ->productJoin($company)
            ->leftJoin('contracts', 'contracts.id', '=', 'plaid_transactions.contract_id')
            ->when($request->has('subscription_id'), function ($query) use ($request) {
                $query->where('plaid_transactions.plaid_subscription_id', $request->subscription_id);
            })->when($request->has('contract_id'), function ($query) use ($request) {
                $query->where('plaid_transactions.contract_id', $request->contract_id);
            })
            ->when($request->has('search_word'), function ($query) use ($request) {
                $query->where(function ($query) use ($request) {
                    $searchWord = '%' . $request->input('search_word') . '%';

                    $query->where('products.name', 'ilike', $searchWord)
                        ->orWhere('plaid_transactions.description', 'ilike', $searchWord)
                        ->orWhere('vendor_companies.name', 'ilike', $searchWord)
                        ->orWhere('contracts.name', 'ilike', $searchWord)
                        ->orWhere('plaid_categories.name', 'ilike', $searchWord);
                });
            })
            ->when($request->has('amount.min') && $request->has('amount.max'), function ($query) use ($request) {
                $query->whereBetween('plaid_transactions.amount', [$request->get('amount')['min'], $request->get('amount')['max']]);
            })->when($request->has('vendor'), function ($query) use ($request) {
                $query->where(function ($query) use ($request) {
                    $vendorIds = array_filter($request->vendor, fn ($value) => $value !== 'null');
                    $query->when(!empty($vendorIds), function ($query) use ($vendorIds) {
                        $query->whereIn('plaid_transactions.vendor_id', $vendorIds);
                    })->when(in_array('null', $request->vendor), function ($query) {
                        $query->orWhere('plaid_transactions.vendor_id', null);
                    });
                });
            })->when($request->has('category'), function ($query) use ($request) {
                $query->where(function ($query) use ($request) {
                    $categoryIds = array_filter($request->category, fn ($value) => $value !== 'null');
                    $query->when(!empty($categoryIds), function ($query) use ($categoryIds) {
                        $query->whereIn('plaid_transactions.category_id', $categoryIds);
                    })->when(in_array('null', $request->category), function ($query) {
                        $query->orWhere('plaid_transactions.category_id', null);
                    });
                });
            })->when($request->has('sub_category'), function ($query) use ($request) {
                $query->where(function ($query) use ($request) {
                    $subCategoryIds = array_filter($request->sub_category, fn ($value) => $value !== 'null');
                    $query->when(!empty($subCategoryIds), function ($query) use ($subCategoryIds) {
                        $query->whereIn('plaid_transactions.sub_category_id', $subCategoryIds);
                    })->when(in_array('null', $request->sub_category), function ($query) {
                        $query->orWhere('plaid_transactions.sub_category_id', null);
                    });
                });
            })->when($request->has('plaid_category'), function ($query) use ($request) {
                $query->where(function ($query) use ($request) {
                    $plaidCategoryIds = array_filter($request->plaid_category, fn ($value) => $value !== 'null');
                    $query->when(!empty($plaidCategoryIds), function ($query) use ($plaidCategoryIds) {
                        $query->whereIn('plaid_transactions.plaid_category_id', $plaidCategoryIds);
                    })->when(in_array('null', $request->plaid_category), function ($query) {
                        $query->orWhere('plaid_transactions.plaid_category_id', null);
                    });
                });
            })->when($request->has('plaid_sub_category'), function ($query) use ($request) {
                $query->where(function ($query) use ($request) {
                    $plaidSubCategoryIds = array_filter($request->plaid_sub_category, fn ($value) => $value !== 'null');
                    $query->when(!empty($plaidSubCategoryIds), function ($query) use ($plaidSubCategoryIds) {
                        $query->whereIn('plaid_transactions.plaid_sub_category', $plaidSubCategoryIds);
                    })->when(in_array('null', $request->plaid_sub_category), function ($query) {
                        $query->orWhere('plaid_transactions.plaid_sub_category', null);
                    });
                });
            })->when($request->has('product'), function ($query) use ($request) {
                $query->where(function ($query) use ($request) {
                    $productIds = array_filter($request->product, fn ($value) => $value !== 'null');
                    $query->when(!empty($productIds), function ($query) use ($productIds) {
                        $query->whereIn('plaid_transactions.product_id', $productIds);
                    })->when(in_array('null', $request->product), function ($query) {
                        $query->orWhere('plaid_transactions.product_id', null);
                    });
                });
            })->when($request->has('account'), function ($query) use ($request) {
                $query->where(function ($query) use ($request) {
                    $accountIds = array_filter($request->account, fn ($value) => $value !== 'null');
                    $query->when(!empty($accountIds), function ($query) use ($accountIds) {
                        $query->whereIn('plaid_transactions.account_id', $accountIds);
                    })->when(in_array('null', $request->account), function ($query) {
                        $query->orWhere('plaid_transactions.account_id', null);
                    });
                });
            })->when($request->has('date.start_date') && $request->has('date.end_date'), function ($query) use ($request) {
                $query->whereBetween('plaid_transactions.date', [$request->input('date.start_date'), $request->input('date.end_date')]);
            })->when(!$request->has('date.start_date') && !$request->has('date.end_date'), function ($query) {
                $startDate = Carbon::now()->startOfDay()->subMonths(12);
                $endDate = Carbon::now()->endOfDay();
                $query->whereBetween('plaid_transactions.date', [$startDate, $endDate]);
            })->when($request->has('visibility'), function ($query) use ($request) {
                $query->where('plaid_transactions.ignore', $request->visibility);
            })->when($request->stack_only, function ($query) {
                $query->stackOnly();
            })->when(!$request->has('order_by'), function ($query) {
                $query->orderBy('plaid_transactions.ignore', 'ASC')
                    ->orderBy('plaid_transactions.date', 'DESC');
            })
            ->groupBy('plaid_transactions.id', 'products.name', 'plaid_categories.name', 'categories.name', 'plaid_bank_accounts.display_name')
            ->with(PlaidTransactionResource::ALL_RELATIONSHIP_LOAD);

        if (!$request->has('order_by')) {
            $query->orderBy('plaid_transactions.date', 'DESC');
        }

        $transactions = UtilityHelper::getSearchRequestQueryResults($request, $query);

        return PlaidTransactionResource::collection($transactions);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/plaid/{company}/subscriptions",
     *     operationId="plaid/subscriptions/showAll",
     *     tags={"PlaidCompanyController"},
     *     summary="Get company plaid subscriptions",
     *     description="Get company plaid subscriptions",
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/plaid/{company}/subscriptions
    // BODY
    /*{}
    / Bearer Token NEEDED*/
    public function showAllSubscriptions(Company $company, ShowAllCompanySubscriptionsRequest $request): AnonymousResourceCollection
    {
        PlaidService::validateIfUserBelongToCompany($company);

        $query = $company->plaidSubscriptions()
            ->select(
                'plaid_subscriptions.id',
                'plaid_subscriptions.description',
                'plaid_subscriptions.logo_url',
                'plaid_subscriptions.merchant_name',
                'plaid_subscriptions.ignore',
                'plaid_subscriptions.last_date',
                'plaid_subscriptions.product_id',
                'plaid_subscriptions.productable_type',
                'plaid_subscriptions.plaid_category_id',
                'plaid_subscriptions.category_id',
                'plaid_subscriptions.contract_id',
                'plaid_subscriptions.vendor_id',
                'plaid_subscriptions.sub_category_id',
                'plaid_subscriptions.account_id',
                'plaid_subscriptions.notes',
                'plaid_subscriptions.last_amount',
                'plaid_subscriptions.frequency',
                'plaid_subscriptions.my_stack_id',
                'plaid_subscriptions.stackable_type'
            )->join('plaid_bank_accounts', 'plaid_bank_accounts.account_id', '=', 'plaid_subscriptions.account_id')
            ->leftJoin('categories', 'categories.id', '=', 'plaid_subscriptions.category_id')
            ->leftJoin('categories as plaid_categories', 'plaid_categories.id', '=', 'plaid_subscriptions.plaid_category_id')
            ->leftJoin('companies as vendor_companies', 'vendor_companies.id', '=', 'plaid_subscriptions.vendor_id')
            ->productJoin($company)
            ->leftJoin('contracts', 'contracts.id', '=', 'plaid_subscriptions.contract_id')
            ->when($request->has('search_word'), function ($query) use ($request) {
                $search = '%' . $request->input('search_word') . '%';
                $query->where(function ($query) use ($search) {
                    $query->where('plaid_subscriptions.frequency', 'ilike', $search)
                        ->orWhere('products.name', 'ilike', $search)
                        ->orWhere('vendor_companies.name', 'ilike', $search)
                        ->orWhere('plaid_subscriptions.description', 'ilike', $search)
                        ->orWhere('contracts.name', 'ilike', $search)
                        ->orWhere('plaid_categories.name', 'ilike', $search);
                });
            })->when($request->has('vendor'), function ($query) use ($request) {
                $query->where(function ($query) use ($request) {
                    $vendorIds = array_filter($request->vendor, fn ($value) => $value !== 'null');
                    $query->when(!empty($vendorIds), function ($query) use ($vendorIds) {
                        $query->whereIn('plaid_subscriptions.vendor_id', $vendorIds);
                    })->when(in_array('null', $request->vendor), function ($query) {
                        $query->orWhere('plaid_subscriptions.vendor_id', null);
                    });
                });
            })->when($request->has('category'), function ($query) use ($request) {
                $query->where(function ($query) use ($request) {
                    $categoryIds = array_filter($request->category, fn ($value) => $value !== 'null');
                    $query->when(!empty($categoryIds), function ($query) use ($categoryIds) {
                        $query->whereIn('plaid_subscriptions.category_id', $categoryIds);
                    })->when(in_array('null', $request->category), function ($query) {
                        $query->orWhere('plaid_subscriptions.category_id', null);
                    });
                });
            })->when($request->has('sub_category'), function ($query) use ($request) {
                $query->where(function ($query) use ($request) {
                    $subCategoryIds = array_filter($request->sub_category, fn ($value) => $value !== 'null');
                    $query->when(!empty($subCategoryIds), function ($query) use ($subCategoryIds) {
                        $query->whereIn('plaid_subscriptions.sub_category_id', $subCategoryIds);
                    })->when(in_array('null', $request->sub_category), function ($query) {
                        $query->orWhere('plaid_subscriptions.sub_category_id', null);
                    });
                });
            })->when($request->has('plaid_category'), function ($query) use ($request) {
                $query->where(function ($query) use ($request) {
                    $plaidCategoryIds = array_filter($request->plaid_category, fn ($value) => $value !== 'null');
                    $query->when(!empty($plaidCategoryIds), function ($query) use ($plaidCategoryIds) {
                        $query->whereIn('plaid_subscriptions.plaid_category_id', $plaidCategoryIds);
                    })->when(in_array('null', $request->plaid_category), function ($query) {
                        $query->orWhere('plaid_subscriptions.plaid_category_id', null);
                    });
                });
            })->when($request->has('plaid_sub_category'), function ($query) use ($request) {
                $query->where(function ($query) use ($request) {
                    $plaidSubCategoryIds = array_filter($request->plaid_sub_category, fn ($value) => $value !== 'null');
                    $query->when(!empty($plaidSubCategoryIds), function ($query) use ($plaidSubCategoryIds) {
                        $query->whereIn('plaid_subscriptions.plaid_sub_category', $plaidSubCategoryIds);
                    })->when(in_array('null', $request->plaid_sub_category), function ($query) {
                        $query->orWhere('plaid_subscriptions.plaid_sub_category', null);
                    });
                });
            })->when($request->has('product'), function ($query) use ($request) {
                $query->where(function ($query) use ($request) {
                    $productIds = array_filter($request->product, fn ($value) => $value !== 'null');
                    $query->when(!empty($productIds), function ($query) use ($productIds) {
                        $query->whereIn('plaid_subscriptions.product_id', $productIds);
                    })->when(in_array('null', $request->product), function ($query) {
                        $query->orWhere('plaid_subscriptions.product_id', null);
                    });
                });
            })->when($request->has('account'), function ($query) use ($request) {
                $query->where(function ($query) use ($request) {
                    $accountIds = array_filter($request->account, fn ($value) => $value !== 'null');
                    $query->when(!empty($accountIds), function ($query) use ($accountIds) {
                        $query->whereIn('plaid_subscriptions.account_id', $accountIds);
                    })->when(in_array('null', $request->account), function ($query) {
                        $query->orWhere('plaid_subscriptions.account_id', null);
                    });
                });
            })->when($request->has('contract'), function ($query) use ($request) {
                $query->where(function ($query) use ($request) {
                    $contractIds = array_filter($request->contract, fn ($value) => $value !== 'null');
                    $query->when(!empty($contractIds), function ($query) use ($contractIds) {
                        $query->whereIn('plaid_subscriptions.contract_id', $contractIds);
                    })->when(in_array('null', $request->contract), function ($query) {
                        $query->orWhere('plaid_subscriptions.contract_id', null);
                    });
                });
            })->when($request->has('account'), function ($query) use ($request) {
                $query->whereIn('plaid_subscriptions.account_id', $request->account);
            })->when($request->has('visibility'), function ($query) use ($request) {
                $query->where('plaid_subscriptions.ignore', $request->visibility);
            })->when($request->has('start_date') && $request->has('end_date'), function ($query) use ($request) {
                $query->whereBetween('plaid_subscriptions.last_date', [$request->get('start_date'), $request->get('end_date')]);
            })->when(!$request->has('date.start_date') && !$request->has('date.end_date'), function ($query) {
                $startDate = Carbon::now()->startOfDay()->subMonths(12);
                $endDate = Carbon::now()->endOfDay();
                $query->whereBetween('plaid_subscriptions.last_date', [$startDate, $endDate]);
            })->when($request->stack_only, function ($query) {
                $query->stackOnly();
            })->groupBy('plaid_subscriptions.id', 'products.name', 'categories.name', 'plaid_categories.name')
            ->with(PlaidSubscriptionResource::ALL_RELATIONSHIP_LOAD);

        if (!$request->has('order_by')) {
            $query->orderBy('plaid_subscriptions.last_date', 'DESC');
        }

        $transactions = UtilityHelper::getSearchRequestQueryResults($request, $query);

        return PlaidSubscriptionResource::collection($transactions);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/plaid/{company}/subscriptions/upcoming-expenses",
     *     operationId="plaid/subscriptions/upcoming-expenses/showAll",
     *     tags={"PlaidCompanyController"},
     *     summary="Get subscriptions upcoming expenses",
     *     description="Get subscriptions upcoming expenses amount and date",
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/plaid/{company}/subscriptions/upcoming-expenses
    // BODY
    /*{}
    / Bearer Token NEEDED*/
    public function showAllSubscriptionsUpcomingExpenses(Company $company, ShowAllCompanySubscriptionsUpcomingExpensesRequest $request): AnonymousResourceCollection
    {
        PlaidService::validateIfUserBelongToCompany($company);

        $query = $company->plaidSubscriptions()
            ->select(
                'plaid_subscriptions.id',
                'plaid_subscriptions.description',
                'plaid_subscriptions.logo_url',
                'plaid_subscriptions.merchant_name',
                'plaid_subscriptions.ignore',
                'plaid_subscriptions.predicted_next_date',
                'plaid_subscriptions.vendor_id',
                'plaid_subscriptions.last_amount',
            )->join('plaid_bank_accounts', 'plaid_bank_accounts.account_id', '=', 'plaid_subscriptions.account_id')
            ->leftJoin('companies as vendor_companies', 'vendor_companies.id', '=', 'plaid_subscriptions.vendor_id')
            ->notIgnored()
            ->when($request->has('search_word'), function ($query) use ($request) {
                $search = '%' . $request->input('search_word') . '%';
                $query->where(function ($query) use ($search) {
                    $query->where('vendor_companies.name', 'ilike', $search)
                        ->orWhere('plaid_subscriptions.predicted_next_date', 'ilike', $search)
                        ->orWhere('plaid_subscriptions.description', 'ilike', $search)
                        ->orWhere('plaid_subscriptions.merchant_name', 'ilike', $search);
                });
            })->when($request->has('vendor'), function ($query) use ($request) {
                $query->where(function ($query) use ($request) {
                    $vendorIds = array_filter($request->vendor, fn ($value) => $value !== 'null');
                    $query->when(!empty($vendorIds), function ($query) use ($vendorIds) {
                        $query->whereIn('plaid_subscriptions.vendor_id', $vendorIds);
                    })->when(in_array('null', $request->vendor), function ($query) {
                        $query->orWhere('plaid_subscriptions.vendor_id', null);
                    });
                });
            })->where('plaid_subscriptions.predicted_next_date', '>', now())
            ->groupBy('plaid_subscriptions.id')
            ->with(ShowAllSubscriptionsUpcomingExpensesResource::ALL_RELATIONSHIP_LOAD);

        $transactions = UtilityHelper::getSearchRequestQueryResults($request, $query);

        return ShowAllSubscriptionsUpcomingExpensesResource::collection($transactions);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/plaid/{company}/{subscriptionId}/transactions",
     *     operationId="plaid/subscriptions/transactions/showAll",
     *     tags={"PlaidCompanyController"},
     *     summary="Get all Subscription's transactions",
     *     description="Show All subscription's transactions info",
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/plaid/{company}/{subscriptionId}/transactions
    // BODY
    /*{}
    / Bearer Token NEEDED*/
    public function showAllSubscriptionTransactions(Company $company, PlaidSubscription $plaidSubscription, ShowAllSubscriptionTransactionsRequest $request)
    {
        PlaidService::validateIfUserBelongToCompany($company);

        if ($plaidSubscription->company_id !== (string)$company->id) {
            throw ValidationException::withMessages([
                config('genericMessages.error.PLAID_ERROR_SUBSCRIPTION_NOT_BELONG_ACCOUNT'),
            ]);
        }
        $transactionsQuery = $plaidSubscription
            ->transactions()
            ->select('plaid_transactions.id', 'plaid_transactions.date', 'plaid_transactions.amount', 'plaid_transactions.description')
            ->leftJoin('categories', 'categories.id', '=', 'plaid_transactions.category_id')
            ->leftJoin('companies as vendor_companies', 'vendor_companies.id', '=', 'plaid_transactions.vendor_id')
            ->leftJoin('products', 'products.id', '=', 'plaid_transactions.product_id')
            ->leftJoin('contracts', 'contracts.id', '=', 'plaid_transactions.contract_id')
            ->when($request->has('date.start_date') && $request->has('date.end_date'), function ($query) use ($request) {
                $query->whereBetween('plaid_transactions.date', [$request->get('date')['start_date'], $request->get('date')['end_date']]);
            })
            ->when($request->has('amount.min') && $request->has('amount.max'), function ($query) use ($request) {
                $query->whereBetween('plaid_transactions.amount', [$request->get('amount')['min'], $request->get('amount')['max']]);
            })
            ->when($request->has('search_word'), function ($query) use ($request) {
                $search = '%' . $request->input('search_word') . '%';
                $query->where(function ($query) use ($search) {
                    $query->where('products.name', 'ilike', $search)
                        ->orWhere('vendor_companies.name', 'ilike', $search)
                        ->orWhere('contracts.name', 'ilike', $search);
                });
            });

        $transactions = UtilityHelper::getSearchRequestQueryResults($request, $transactionsQuery);

        return PlaidTransactionSimpleResource::collection($transactions);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/plaid/{company}/expenses/breakdown",
     *     operationId="plaid/expenses/breakdown",
     *     tags={"PlaidCompanyController"},
     *     summary="Expenses Breakdown",
     *     description="Get Expenses Breakdown detail with all the categories summary",
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/plaid/{company}/expenses/breakdown
    // BODY
    /*{}
    / Bearer Token NEEDED*/
    public function expensesBreakdown(Company $company, ExpensesBreakdownRequest $request): JsonResponse
    {
        PlaidService::validateIfUserBelongToCompany($company);

        $isCategory = in_array($request->get('by'), ['category', 'sub_category']) || !$request->has('by');

        $startDate = Carbon::parse($request->start_date);
        $endDate = Carbon::parse($request->end_date);

        $prevStartDate = $startDate->copy()->subDays($startDate->diffInDays($endDate));
        $prevEndDate = $startDate->copy();

        $expensesBreakDown = $company->plaidTransactions()
            ->where('ignore', false)
            ->when($isCategory, function ($query) use ($request) {
                $categoryColumn = $request->get('by') === 'category' ? 'plaid_transactions.category_id' : 'plaid_transactions.sub_category_id';
                $query->selectRaw("COALESCE(categories.name, 'Uncategorized') AS name, categories.color, categories.id,
                ROUND(SUM(plaid_transactions.amount), 2) AS amount")
                    ->leftJoin('categories', 'categories.id', '=', $categoryColumn)
                    ->groupBy('categories.name', 'categories.color', 'categories.id')
                    ->orderBy('categories.name');
            })->when($request->get('by') === 'vendor', function ($query) {
                $query->selectRaw("COALESCE(companies.name, 'N/D') AS name, companies.id,
                ROUND(SUM(plaid_transactions.amount), 2) AS amount")
                    ->leftJoin('companies', 'companies.id', '=', 'plaid_transactions.vendor_id')
                    ->groupBy('companies.name', 'companies.id')
                    ->orderBy('companies.name');
            })->when($request->get('expenses') === 'subscriptions', function ($query) {
                $query->whereNotNull('plaid_transactions.plaid_subscription_id');
            })->when($request->has('category_id'), function ($query) use ($request) {
                $query->where('plaid_transactions.category_id', $request->get('category_id'));
            })->when($request->has('plaid_category_id'), function ($query) use ($request) {
                $query->where('plaid_transactions.plaid_category_id', $request->get('plaid_category_id'));
            })->when($request->stack_only, function ($query) {
                $query->stackOnly();
            })
            ->whereBetween('plaid_transactions.date', [$startDate, $endDate])
            ->get();

        $totalAmount = $expensesBreakDown->sum('amount');

        if ($isCategory) {
            $expensesBreakDown->transform(function ($model) use ($totalAmount, $company, $prevStartDate, $prevEndDate, $request) {
                $model->percentage = round(($model->amount / $totalAmount) * 100, 2);
                $model->color = $model->color ?? '#000000';

                if ($request->has('plaid_category_id')) {
                    $column = $request->get('by') === 'category' ? 'plaid_transactions.plaid_category_id' : 'plaid_transactions.plaid_sub_category_id';
                } else {
                    $column = $request->get('by') === 'category' ? 'plaid_transactions.category_id' : 'plaid_transactions.sub_category_id';
                }

                $previousAmount = PlaidExpensesService::getAllExpenses($company, [$prevStartDate, $prevEndDate])
                    ->where($column, $model->id)
                    ->sum('amount');

                $model->percentage_diff = PlaidExpensesService::getPercentageDiff($previousAmount, $model->amount);

                return $model;
            });
        } else {
            $expensesBreakDown->transform(function ($model) use ($totalAmount, $company, $prevStartDate, $prevEndDate) {
                $model->percentage = round(($model->amount / $totalAmount) * 100, 2);
                $model->avatar = null;

                $previousAmount = PlaidExpensesService::getAllExpenses($company, [$prevStartDate, $prevEndDate])
                    ->where('vendor_id', $model->id)
                    ->sum('amount');

                $model->percentage_diff = PlaidExpensesService::getPercentageDiff($previousAmount, $model->amount);

                return $model;
            });

            ImageService::appendCompanyAvatarsURLs($expensesBreakDown);
        }

        return response()->json($expensesBreakDown);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/plaid/{company}/expenses/overview/breakdown",
     *     operationId="plaid/expenses/overview/breakdown",
     *     tags={"PlaidCompanyController"},
     *     summary="Expenses Overview Breakdown",
     *     description="Get Expenses Overview Breakdown detail with all the categories summary",
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/plaid/{company}/expenses/overview/breakdown
    // BODY
    /*{}
    / Bearer Token NEEDED*/
    public function expensesOverviewBreakdown(Company $company, ExpensesOverviewBreakdownRequest $request)
    {
        PlaidService::validateIfUserBelongToCompany($company);

        $startDate = Carbon::parse($request->start_date);
        $endDate = Carbon::parse($request->end_date);

        $prevStartDate = $startDate->copy()->subDays($startDate->diffInDays($endDate));
        $prevEndDate = $startDate->copy();

        $isSubscription = $request->filter === 'subscription';
        $isAllFilter = $request->filter === 'all';
        $hideUncategorized = $request->hide_uncategorized;

        $categoryColumn = 'plaid_transactions.category_id';
        $subCategoryColumn = 'plaid_transactions.sub_category_id';

        if ($isAllFilter) {
            $categoryColumn = 'plaid_transactions.plaid_category_id';
            $subCategoryColumn = 'plaid_transactions.plaid_sub_category_id';
        }

        $getBaseQuery = function () use ($company, $isSubscription, $request) {
            return $company->plaidTransactions()
                ->where('plaid_transactions.ignore', false)
                ->when($isSubscription, function ($query) {
                    $query->whereNotNull('plaid_transactions.plaid_subscription_id');
                })->when($request->has('stack_only'), function ($query) {
                    $query->stackOnly();
                })->when(in_array($request->order_by, ['name', 'amount']), function ($query) use ($request) {
                    $requestSort = $request->sort ?: 'ASC';
                    $query->orderBy($request->get('order_by'), $requestSort);
                });
        };

        $expensesBreakDownCategories = $getBaseQuery()
            ->selectRaw("COALESCE(categories.name, 'Uncategorized') AS name, categories.color, categories.id,
                ROUND(SUM(plaid_transactions.amount), 2) AS amount"
            )->leftJoin('categories', 'categories.id', '=', $categoryColumn)
            ->when($hideUncategorized, function ($query) {
                $query->whereNotNull('categories.id');
            })
            ->whereBetween('plaid_transactions.date', [$request->start_date, $request->end_date])
            ->groupBy('categories.name', 'categories.color', 'categories.id')
            ->get();

        $totalAmount = $expensesBreakDownCategories->sum('amount');

        $getPreviousAmountInfo = static function ($model, $column) use ($getBaseQuery, $prevStartDate, $prevEndDate) {
            $previousAmount = $getBaseQuery()
                ->where($column, $model->id)
                ->whereBetween('plaid_transactions.date', [$prevStartDate, $prevEndDate])
                ->sum('amount');

            return PlaidExpensesService::getPercentageDiff($previousAmount, $model->amount);
        };

        $descendingSort = $request->sort === 'DESC';

        $sortModel = function ($collection) use ($descendingSort, $request) {
            if ($request->order_by === 'percentage') {
                return $collection->sortBy(fn ($model) => $model->percentage, descending: $descendingSort)->values();
            }

            if ($request->order_by === 'change_percentage') {
                return $collection->sortBy(fn ($model) => $model->percentage_diff->change_percentage, descending: $descendingSort)->values();
            }

            return $collection;
        };

        $expensesBreakDownCategories->transform(function ($model) use ($hideUncategorized, $isAllFilter, $subCategoryColumn, $categoryColumn, $totalAmount, $getPreviousAmountInfo, $getBaseQuery, $request, $company, $sortModel) {
            $model->percentage = '' . round(($model->amount / $totalAmount) * 100, 2);
            $model->color = $model->color ?? '#000000';

            $model->percentage_diff = $getPreviousAmountInfo($model, $categoryColumn);

            if (!$isAllFilter) {
                $model->sub_categories = $getBaseQuery()
                    ->selectRaw("COALESCE(categories.name, 'Uncategorized') AS name, categories.id,
                            ROUND( SUM(plaid_transactions.amount), 2) AS amount,
                            ROUND( (SUM(plaid_transactions.amount) / ?) * 100, 2) as percentage", [$model->amount]
                    )->leftJoin('categories', 'categories.id', '=', $subCategoryColumn)
                    ->when($hideUncategorized, function ($query) {
                        $query->whereNotNull('categories.id');
                    })
                    ->whereBetween('plaid_transactions.date', [$request->start_date, $request->end_date])
                    ->where($categoryColumn, $model->id)
                    ->groupBy('categories.name', 'categories.color', 'categories.id')
                    ->get();

                $model->sub_categories->transform(function ($model) use ($subCategoryColumn, $getPreviousAmountInfo, $company) {
                    $model->percentage_diff = $getPreviousAmountInfo($model, $subCategoryColumn);

                    $model->products_count = $model->id ? PlaidExpensesService::getNotLinkedStackContractsBySubCategory($company, $model->id)->get()->count() : 0;

                    return $model;
                });

                $model->sub_categories = $sortModel($model->sub_categories);
            }

            return $model;
        });

        return $sortModel($expensesBreakDownCategories);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/plaid/{company}/stack/not-linked-expenses",
     *     operationId="plaid/stack/not-linked-expenses/showAll",
     *     tags={"PlaidCompanyController"},
     *     summary="Get company stacks not linked to expeses",
     *     description="Get company stacks not linked to expeses  with contracts yet",
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/plaid/{company}/stack/not-linked-expenses
    // BODY
    /*{}
    / Bearer Token NEEDED*/
    public function showAllStackNotLinkedExpenses(Company $company, ShowAllStackNotLinkedExpensesRequest $request)
    {
        PlaidService::validateIfUserBelongToCompany($company);

        $data = $request->validated();

        $notLinkedStack = PlaidExpensesService::getNotLinkedStackContractsBySubCategory($company, $data['sub_category_id'])
            ->when($request->has('order_by'), function ($query) use ($request) {
                $requestSort = $request->sort ?: 'ASC';
                $query->orderBy('contract_name', $requestSort)
                    ->orderBy('product_name', $requestSort);
            })
            ->get();

        return StackNotLinkedExpensesResource::collection($notLinkedStack);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/plaid/{company}/stack/suggested-expenses",
     *     operationId="plaid/{company}/stack/suggested-expenses",
     *     tags={"PlaidCompanyController"},
     *     summary="Get company suggested stacks expeses",
     *     description="Get company suggested stacks expeses data",
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/plaid/{company}/stack/suggested-expenses
    // BODY
    /*{}
    / Bearer Token NEEDED*/
    public function showAllStackSuggestedExpenses(Company $company, ShowAllStackNotLinkedExpensesSuggestedRequest $request): AnonymousResourceCollection
    {
        PlaidService::validateIfUserBelongToCompany($company);

        $data = $request->validated();

        $hasSubCategoryParameter = $request->has('sub_category_id');

        $notLinkedStacksVendorNames = [];

        if ($hasSubCategoryParameter) {
            $notLinkedStacks = PlaidExpensesService::getNotLinkedStackContractsBySubCategory($company, $data['sub_category_id'])
                ->groupBy('vendor_companies.name')
                ->distinct()
                ->get();
            $notLinkedStacks->pluck('vendor_name')->each(function ($vendorName) use (&$notLinkedStacksVendorNames) {
                $explodedVendorName = explode(' ', $vendorName);
                foreach ($explodedVendorName as $splitedName) {
                    $notLinkedStacksVendorNames[] = $splitedName;
                }
            });
        }

        $query = PlaidExpensesService::getAllExpenses($company)
            ->select('plaid_transactions.id',
                'plaid_transactions.merchant_name',
                'plaid_transactions.description',
                'plaid_transactions.company_id',
                DB::raw("'transaction' as type"),
                'plaid_transactions.amount',
                'plaid_transactions.logo_url',
                'plaid_transactions.account_id',
                'plaid_transactions.vendor_id',
                DB::raw('CAST(plaid_transactions.date AS DATE) as date')
            )
            ->when($hasSubCategoryParameter, function ($q) use ($data) {
                $q->where('plaid_transactions.sub_category_id', $data['sub_category_id']);
            })
            ->when(!$hasSubCategoryParameter, function ($q) {
                $q->whereNull('plaid_transactions.sub_category_id');
            })
            ->where(function ($query) use ($notLinkedStacksVendorNames) {
                $query->whereNull('plaid_transactions.my_stack_id')
                    ->orWhere(function ($query) use ($notLinkedStacksVendorNames) {
                        $query->whereNull('plaid_transactions.my_stack_id');
                        foreach ($notLinkedStacksVendorNames as $notLinkedStackVendorName) {
                            $query->orWhere('plaid_transactions.merchant_name', 'ilike', '%' . $notLinkedStackVendorName . '%')
                                ->orWhere('plaid_transactions.description', 'ilike', '%' . $notLinkedStackVendorName . '%');
                        }
                    });
            })
            ->union(
                PlaidExpensesService::getSubscriptions($company)
                    ->select('plaid_subscriptions.id',
                        'plaid_subscriptions.merchant_name',
                        'plaid_subscriptions.description',
                        'plaid_subscriptions.company_id',
                        DB::raw("'Recurring Expense' as type"),
                        'plaid_subscriptions.last_amount as amount',
                        DB::raw('NULL as logo_url'),
                        'plaid_subscriptions.account_id',
                        'plaid_subscriptions.vendor_id',
                        DB::raw('CAST(plaid_subscriptions.last_date AS DATE) as date')
                    )
                    ->when($hasSubCategoryParameter, function ($q) use ($data) {
                        $q->where('plaid_subscriptions.sub_category_id', $data['sub_category_id']);
                    })
                    ->when(!$hasSubCategoryParameter, function ($q) {
                        $q->whereNull('plaid_subscriptions.sub_category_id');
                    })
                    ->where('plaid_subscriptions.company_id', $company->id)
                    ->where(function ($query) use ($notLinkedStacksVendorNames) {
                        $query->whereNull('plaid_transactions.my_stack_id')
                            ->orWhere(function ($query) use ($notLinkedStacksVendorNames) {
                                $query->whereNull('plaid_transactions.my_stack_id');
                                foreach ($notLinkedStacksVendorNames as $notLinkedStackVendorName) {
                                    $query->orWhere('plaid_transactions.merchant_name', 'ilike', '%' . $notLinkedStackVendorName . '%')
                                        ->orWhere('plaid_transactions.description', 'ilike', '%' . $notLinkedStackVendorName . '%');
                                }
                            });
                    })
            )->when($request->has('order_by'), function ($query) use ($request) {
                $requestSort = $request->sort ?: 'ASC';
                $query->orderBy($request->order_by, $requestSort);
            })
            ->with(StackNotLinkedExpensesSuggestionsResource::ALL_RELATIONSHIP_LOAD);

        if ($request->has('paged') && $request->paged) {
            $result = $query->paginate($request->items_per_page ?? config('common.searchPagingLength'));
        } else {
            $result = $query->get();
        }

        return StackNotLinkedExpensesSuggestionsResource::collection($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/plaid/{companyId}/filters/breakdown",
     *     operationId="plaid/filters/breakdown/showAll",
     *     tags={"ChannelDealsController"},
     *     summary="Get break down filters",
     *     description="Get break down filters",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/plaid/{company}/filters/breakdown
    // NO PARAMS
    // needs Bearer Token
    public function showBreakdownFilters(): JsonResponse
    {
        $filters['by'] = BreakDownFilters::BY;
        $filters['by']['items'] = [
            [
                'id' => 'category',
                'name' => 'Category',
            ],
            [
                'id' => 'vendor',
                'name' => 'Vendor',
            ],
        ];

        $filters['expenses'] = BreakDownFilters::EXPENSES;
        $filters['expenses']['items'] = [
            [
                'id' => 'all',
                'name' => 'All Expenses',
            ],
            [
                'id' => 'subscriptions',
                'name' => 'Subscriptions',
            ],
        ];

        return response()->json([
            'filters' => $filters,
        ]);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/plaid/{companyId}/filters/transactions",
     *     operationId="plaid/filters/transactions/showAll",
     *     tags={"PlaidCompanyController"},
     *     summary="Get transactions filters",
     *     description="Get transactions down filters",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/plaid/{company}/filters/transactions
    // NO PARAMS
    // needs Bearer Token
    public function showTransactionsFilters(Company $company, TransactionsFiltersRequest $request): JsonResponse
    {
        $transactionsQuery = $company->plaidTransactions()
            ->when($request->has(['start_date', 'end_date']), function ($query) use ($request) {
                $query->whereBetween('date', [$request->start_date, $request->end_date]);
            })->when($request->has('vendor_id'), function ($query) use ($request) {
                $query->where('vendor_id', $request->vendor_id);
            })->when($request->has('category_id'), function ($query) use ($request) {
                $query->where('category_id', $request->category_id);
            });

        if (!$transactionsQuery->count()) {
            return response()->json();
        }

        $transactions = $transactionsQuery
            ->select('id', 'product_id', 'productable_type', 'vendor_id', 'category_id', 'plaid_category_id', 'account_id')
            ->with(['product:id,name', 'vendor:id,name', 'category:id,name,color', 'plaidCategory:id,name,color', 'plaidBankAccount:id,account_id,display_name,mask,plaid_bank_link_id', 'plaidBankAccount.plaidBankLink:id,plaid_institution_id'])
            ->get();

        $filters = $this->commonsTransactionFilters($company, $transactions);
        $filters['amount'] = TransactionFilters::AMOUNT;
        $filters['date'] = TransactionFilters::DATE;

        return response()->json([
            'filters' => $filters,
        ]);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/filters/subscriptions-upcoming-expenses",
     *     operationId="filters/subscriptions-upcoming-expenses",
     *     tags={"PlaidCompanyController"},
     *     summary="Get Subscriptions upcoming expenses",
     *     description="Get Subscriptions upcoming expenses data",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/plaid/{company}/filters/subscriptions-upcoming-expenses
    // NO PARAMS
    // needs Bearer Token
    public function showSubscriptionsUpcomingExpensesFilters(Company $company, TransactionsFiltersRequest $request): JsonResponse
    {
        $query = $company->plaidSubscriptions()
            ->select(
                'plaid_subscriptions.id',
                'plaid_subscriptions.vendor_id',
            )->leftJoin('companies as vendor_companies', 'vendor_companies.id', '=', 'plaid_subscriptions.vendor_id')
            ->notIgnored()
            ->where('plaid_subscriptions.predicted_next_date', '>', now())
            ->groupBy('plaid_subscriptions.id')
            ->with('vendor');

        if (!$query->count()) {
            return response()->json();
        }

        $upComingSubsriptionsExpenses = $query->get();

        $vendors = $upComingSubsriptionsExpenses->pluck('vendor')->filter();
        $filters['vendor'] = TransactionFilters::VENDOR;
        $filters['vendor']['items'] = FilterService::prepareItemCollectionByIdName($vendors);
        $filters['vendor']['items'][] = FilterService::prepareItemUnassigned();

        $filters['amount'] = TransactionFilters::AMOUNT;
        $filters['date'] = TransactionFilters::DATE;

        return response()->json([
            'filters' => $filters,
        ]);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/plaid/{company}/filters/alert-notifications",
     *     operationId="plaid/filters/alert-notifications/showAll",
     *     tags={"PlaidCompanyController"},
     *     summary="Show all plaid notifications filters",
     *     description="Show all plaid notifications filters for FE",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/plaid/{company}/filters/alert-notifications
    // NO PARAMS
    // needs Bearer Token
    public function showAlertNotificationsFilters(Company $company): JsonResponse
    {
        $alertNotificationsQuery = $company->plaidCompanyNotifications();

        if (!$alertNotificationsQuery->count()) {
            return response()->json();
        }

        $alertNotifications = $alertNotificationsQuery
            ->select(
                'plaid_company_notifications.subscription_id',
                'plaid_company_notifications.type_id',
                'plaid_company_notifications.period_id',
            )
            ->leftJoin('plaid_subscriptions', 'plaid_subscriptions.id', '=', 'plaid_company_notifications.subscription_id')
            ->leftJoin('companies', 'companies.id', '=', 'plaid_subscriptions.vendor_id')
            ->with(['plaidSubscription:id,vendor_id,product_id,productable_type', 'plaidSubscription.vendor:id,name', 'type', 'period'])
            ->get();

        $filters['vendor'] = AlertNotificationFilters::VENDOR;
        $filters['vendor']['items'] = FilterService::prepareItemCollectionByIdName($alertNotifications->pluck('plaidSubscription.vendor')->filter());

        $filters['alert_type'] = AlertNotificationFilters::ALERT_TYPE;
        $filters['alert_type']['items'] = FilterService::prepareItemCollectionByIdName($alertNotifications->pluck('type')->filter());

        $filters['period'] = AlertNotificationFilters::PERIOD;
        $filters['period']['items'] = FilterService::prepareItemCollectionByIdName($alertNotifications->pluck('period')->filter());

        $filters['date'] = TransactionFilters::DATE;

        return response()->json([
            'filters' => $filters,
        ]);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/plaid/{company}/filters/subscriptions",
     *     operationId="plaid/filters/subscriptions/showAll",
     *     tags={"PlaidCompanyController"},
     *     summary="Show all subscriptions filters",
     *     description="Show all subscriptions filters for FE",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/plaid/{company}/filters/subscriptions
    // NO PARAMS
    // needs Bearer Token
    public function showSubscriptionsFilters(Company $company, TransactionsFiltersRequest $request): JsonResponse
    {
        $subscriptionsQuery = $company->plaidSubscriptions()->when($request->has(['start_date', 'end_date']), function ($query) use ($request) {
            $query->whereBetween('last_date', [$request->start_date, $request->end_date]);
        });

        if (!$subscriptionsQuery->count()) {
            return response()->json();
        }

        $transactions = $subscriptionsQuery
            ->select('id', 'product_id', 'productable_type', 'vendor_id', 'category_id', 'plaid_category_id', 'account_id')
            ->with(['product:id,name', 'vendor:id,name', 'category:id,name,color',  'plaidCategory:id,name,color', 'plaidBankAccount:id,account_id,display_name,mask,plaid_bank_link_id', 'plaidBankAccount.plaidBankLink:id,plaid_institution_id'])
            ->get();

        $filters = $this->commonsTransactionFilters($company, $transactions); // 2

        return response()->json([
            'filters' => $filters,
        ]);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/plaid/{company}/filters/{plaidSubscription}/transactions",
     *     operationId="plaid/filters/subscriptions/transactions/showAll",
     *     tags={"PlaidCompanyController"},
     *     summary="Show all subscriptions transactions filters",
     *     description="Show all subscriptions transactions filters for FE",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/plaid/{company}/filters/{plaidSubscription}/transactions
    // NO PARAMS
    // needs Bearer Token
    public function showSubscriptionTransactionsFilters(Company $company, PlaidSubscription $plaidSubscription): JsonResponse
    {
        if (!$plaidSubscription->transactions()->count()) {
            return response()->json();
        }

        $filters['amount'] = TransactionFilters::AMOUNT;

        return response()->json([
            'filters' => $filters,
        ]);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/plaid/bulk/transaction",
     *     operationId="plaid/bulk/transactions",
     *     tags={"PlaidCompanyController"},
     *     summary="Bulk update for transactions",
     *     description="Bulk update for transactions",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws \Exception
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/plaid/{companyId}/bulk/transaction
    // BODY
    /*{}
    // Bearer token needed */
    public function bulkUpdateTransaction(Company $company, TransactionBulkUpdateRequest $request): AnonymousResourceCollection
    {
        PlaidService::validateIfUserBelongToCompany($company);

        try {
            $requestData = $request->validated();

            $transactionsUpdated = collect();

            DB::transaction(function () use ($company, $requestData, $transactionsUpdated, $request) {
                foreach ($requestData['ids'] as $transactionId) {
                    $transaction = $company->plaidTransactions()->find($transactionId);

                    if (!$transaction) {
                        throw ValidationException::withMessages([
                            config('genericMessages.error.PLAID_ERROR_TRANSACTION_NOT_BELONG_ACCOUNT'),
                        ]);
                    }

                    $transaction->fill($requestData);

                    $this->autoFillAndSaveData($transaction, $request, $requestData, $company);

                    $transactionsUpdated->push($transaction);

                    $transaction->load(PlaidTransactionResource::ALL_RELATIONSHIP_LOAD);
                }
            });
        } catch (QueryException $ex) {
            \App\Log::error('ERROR::' . __CLASS__ . '::' . __FUNCTION__
                . '::' . $ex->getMessage() . '::' . $ex->getTraceAsString());

            throw ValidationException::withMessages([
                'ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::' . $ex->getMessage(),
            ]);
        }

        return PlaidTransactionResource::collection($transactionsUpdated);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/plaid/company/bulk/sync-bank-account",
     *     operationId="plaid/bulk/sync-bank-account",
     *     tags={"PlaidCompanyController"},
     *     summary="Bulk sync bank account",
     *     description="Bulk sync bank account at the same time",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws \Exception
     */
    // </editor-fold>
    // API Call: post
    // http://127.0.0.1:8000/api/v1/plaid/{companyId}/bulk/sync-bank-account
    // BODY
    /*{}
    // Bearer token needed */
    public function bulkSyncBankAccount(Company $company, BulkBankAccountRequest $request): AnonymousResourceCollection
    {
        PlaidService::validateIfUserBelongToCompany($company);

        $requestData = $request->validated();
        $loggedUserId = AuthService::getLoggedInUserId();
        $bankAccountSynced = collect();

        foreach ($requestData['ids'] as $bankAccountId) {
            $plaidBankAccount = $company->plaidBankAcounts()->find($bankAccountId);

            if ($plaidBankAccount) {
                if ($plaidBankAccount->can_be_synced) {
                    $plaidBankAccount->is_syncing = true;
                    $plaidBankAccount->save();
                    $plaidBankAccount->refresh();
                    PlaidAccountSync::dispatch($plaidBankAccount, $loggedUserId);
                }
                $bankAccountSynced->push($plaidBankAccount);
            }
        }

        return PlaidBankAccountResource::collection($bankAccountSynced);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Delete(
     *     path="/api/v1/plaid/company/bulk/bank-account",
     *     operationId="plaid/bulk/bank-account/delete",
     *     tags={"PlaidCompanyController"},
     *     summary="Bulk delete bank account",
     *     description="Bulk delete bank account at the same time",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws \Exception
     */
    // </editor-fold>
    // API Call: DELETE
    // http://127.0.0.1:8000/api/v1/plaid/{companyId}/bulk/bank-account
    // BODY
    /*{}
    // Bearer token needed */
    public function bulkDeleteBankAccount(Company $company, BulkBankAccountRequest $request): JsonResponse
    {
        PlaidService::validateIfUserBelongToCompany($company);

        $requestData = $request->validated();

        foreach ($requestData['ids'] as $bankAccountId) {
            $plaidBankAccount = $company->plaidBankAcounts()->find($bankAccountId);

            if (!$plaidBankAccount) {
                throw ValidationException::withMessages([
                    config('genericMessages.error.PLAID_ERROR_BANK_ACCOUNT_NOT_BELONG_TO_COMPANY'),
                    'account_id' => $bankAccountId,
                ]);
            }

            $plaidBankAccount->delete();
        }

        PlaidAlertService::refreshCompanyAlertNotification($company);

        return response()->json();
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/plaid/company/bulk/bank-account",
     *     operationId="plaid/company/bulk/bank-account",
     *     tags={"PlaidCompanyController"},
     *     summary="Bulk bank account update",
     *     description="Update bank account using bulk action",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws \Exception
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/plaid/{companyId}/bulk/bank-account
    // BODY
    /*{}
    // Bearer token needed */
    public function bulkUpdateBankAccount(Company $company, BulkUpdateBankAccountRequest $request): AnonymousResourceCollection
    {
        PlaidService::validateIfUserBelongToCompany($company);

        $requestData = $request->validated();
        $bankAccountUpdated = collect();

        foreach ($requestData['ids'] as $bankAccountId) {
            $plaidBankAccount = $company->plaidBankAcounts()->find($bankAccountId);

            if (!$plaidBankAccount) {
                throw ValidationException::withMessages([
                    config('genericMessages.error.PLAID_ERROR_BANK_ACCOUNT_NOT_BELONG_TO_COMPANY'),
                    'account_id' => $bankAccountId,
                ]);
            }

            $plaidBankAccount->update([
                'display_name' => $requestData['display_name'],
            ]);

            $plaidBankAccount->refresh();

            $bankAccountUpdated->push($plaidBankAccount);
        }

        return PlaidBankAccountResource::collection($bankAccountUpdated);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/plaid/company/transaction",
     *     operationId="plaid/company/transaction/update",
     *     tags={"PlaidCompanyController"},
     *     summary="Update transaction",
     *     description="Update transaction data",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws \Exception
     */
    // </editor-fold>
    // API Call: PUT
    // http://127.0.0.1:8000/api/v1/plaid/{companyId}/transaction
    // BODY
    /*{}
    // Bearer token needed */
    public function updateTransaction(Company $company, TransactionUpdateRequest $request): PlaidTransactionResource
    {
        PlaidService::validateIfUserBelongToCompany($company);

        $requestData = $request->validated();

        $transaction = $company->plaidTransactions()->find($requestData['id']);

        if (!$transaction) {
            throw ValidationException::withMessages([
                config('genericMessages.error.PLAID_ERROR_TRANSACTION_NOT_BELONG_ACCOUNT'),
            ]);
        }

        try {
            DB::transaction(function () use ($request, $requestData, $transaction, $company) {
                $transaction->fill($requestData);

                $this->autoFillAndSaveData($transaction, $request, $requestData, $company);
            });
        } catch (QueryException $ex) {
            \App\Log::error('ERROR::' . __CLASS__ . '::' . __FUNCTION__
                . '::' . $ex->getMessage() . '::' . $ex->getTraceAsString());

            throw ValidationException::withMessages([
                'ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::' . $ex->getMessage(),
            ]);
        }

        $transaction->load(PlaidTransactionResource::ALL_RELATIONSHIP_LOAD);

        return new PlaidTransactionResource($transaction);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/plaid/company/subscription/update",
     *     operationId="plaid/company/subscription/update",
     *     tags={"PlaidCompanyController"},
     *     summary="Update subscription",
     *     description="Update subscription",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws \Exception
     */
    // </editor-fold>
    // API Call: PUT
    // http://127.0.0.1:8000/api/v1/plaid/{companyId}/subscription
    // BODY
    /*{}
    // Bearer token needed */
    public function updateSubscription(Company $company, SubscriptionUpdateRequest $request): PlaidSubscriptionResource
    {
        PlaidService::validateIfUserBelongToCompany($company);

        $requestData = $request->validated();

        $subscription = $company->plaidSubscriptions()->find($requestData['id']);

        if (!$subscription) {
            throw ValidationException::withMessages([
                config('genericMessages.error.PLAID_ERROR_SUBSCRIPTION_NOT_BELONG_ACCOUNT'),
            ]);
        }

        try {
            DB::transaction(function () use ($request, $requestData, $subscription, $company) {
                $subscription->fill($requestData);

                $this->autoFillAndSaveData($subscription, $request, $requestData, $company);

                PlaidExpensesService::syncSubscriptionTransactionsData($subscription);
            });
        } catch (QueryException $ex) {
            \App\Log::error('ERROR::' . __CLASS__ . '::' . __FUNCTION__
                . '::' . $ex->getMessage() . '::' . $ex->getTraceAsString());

            throw ValidationException::withMessages([
                'ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::' . $ex->getMessage(),
            ]);
        }

        $subscription->load(PlaidSubscriptionResource::ALL_RELATIONSHIP_LOAD);

        return new PlaidSubscriptionResource($subscription);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/plaid/company/bulk/subscription",
     *     operationId="company/bulk/subscription/update",
     *     tags={"PlaidCompanyController"},
     *     summary="Bulk subscription update",
     *     description="Bulk subscription update",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws \Exception
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/plaid/{companyId}/bulk/subscription
    // BODY
    /*{}
    // Bearer token needed */
    public function bulkUpdateSubscription(Company $company, SubscriptionBulkUpdateRequest $request): AnonymousResourceCollection
    {
        PlaidService::validateIfUserBelongToCompany($company);

        $requestData = $request->validated();

        $subscriptionsUpdated = collect();

        foreach ($requestData['ids'] as $subscriptionId) {
            $subscription = $company->plaidSubscriptions()->find($subscriptionId);
            if (!$subscription) {
                throw ValidationException::withMessages([
                    config('genericMessages.error.PLAID_ERROR_SUBSCRIPTION_NOT_BELONG_ACCOUNT'),
                ]);
            }

            $subscription->fill($requestData);

            $this->autoFillAndSaveData($subscription, $request, $requestData, $company);

            $subscription->load(PlaidSubscriptionResource::ALL_RELATIONSHIP_LOAD);

            PlaidExpensesService::syncSubscriptionTransactionsData($subscription);

            $subscriptionsUpdated->push($subscription);
        }

        return PlaidSubscriptionResource::collection($subscriptionsUpdated);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Delete(
     *     path="/api/v1/plaid/company/{companyId}/{plaidBankAccount:account_id}",
     *     operationId="plaid/company/bankAccount/delete",
     *     tags={"PlaidCompanyController"},
     *     summary="Delete bank account",
     *     description="Delete bank account",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws \Exception
     */
    // </editor-fold>
    // API Call: DELETE
    // http://127.0.0.1:8000/api/v1/plaid/{companyId}/{plaidBankAccount:account_id}
    // BODY
    /*{}
    // Bearer token needed */
    public function deleteBankAccount(Company $company, PlaidBankAccount $plaidBankAccount): JsonResponse
    {
        PlaidService::validateIfUserBelongToCompany($company);

        if ($plaidBankAccount->plaidBankLink->company_id !== (string)$company->id) {
            throw ValidationException::withMessages([
                config('genericMessages.error.PLAID_ERROR_BANK_ACCOUNT_NOT_BELONG_TO_COMPANY'),
            ]);
        }

        try {
            DB::transaction(function () use ($plaidBankAccount) {
                $plaidBankAccount->delete();
            });

            PlaidAlertService::refreshCompanyAlertNotification($company);

            return response()->json();
        } catch (QueryException $ex) {
            Log::error(__CLASS__ . '::' . __FUNCTION__ . ' - Error deleting bank account');
            Log::error($ex->getMessage());

            throw ValidationException::withMessages([$ex->getMessage()]);
        }
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/plaid/company/bankAccountId/sync",
     *     operationId="plaid/company/bankAccountId/sync",
     *     tags={"PlaidCompanyController"},
     *     summary="Sync bank account transactions",
     *     description="Sync bank account transactions",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws \Exception
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/plaid/{companyId}/{plaidBankAccount:account_id}/sync
    // BODY
    /*{}
    // Bearer token needed */
    public function syncBankAccount(Company $company, PlaidBankAccount $plaidBankAccount): PlaidBankAccountResource
    {
        PlaidService::validateIfUserBelongToCompany($company);

        if (!$plaidBankAccount->can_be_synced) {
            throw ValidationException::withMessages([
                config('genericMessages.error.PLAID_ERROR_BANK_ACCOUNT_CAN_NOT_BE_SYNCED'),
            ]);
        }

        $plaidBankAccount->is_syncing = true;
        $plaidBankAccount->save();
        $plaidBankAccount->refresh();

        PlaidAccountSync::dispatch($plaidBankAccount, AuthService::getLoggedInUserId());

        return new PlaidBankAccountResource($plaidBankAccount);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/plaid/company/bankAccount",
     *     operationId="plaid/company/bankAccount/update",
     *     tags={"PlaidCompanyController"},
     *     summary="Update bank account info",
     *     description="Update bank account",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws \Exception
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/plaid/{companyId}/{plaidBankAccount:account_id}
    // BODY
    /*{}
    // Bearer token needed */
    public function updateBankAccount(Company $company, PlaidBankAccount $plaidBankAccount, UpdateBankAccountRequest $request): PlaidBankAccountResource
    {
        PlaidService::validateIfUserBelongToCompany($company);
        $data = $request->validated();

        $plaidBankAccount->update($data);
        $plaidBankAccount->refresh();

        return new PlaidBankAccountResource($plaidBankAccount);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/plaid/{companyId}/expenses/export-to-csv",
     *     operationId="plaid/company/expenses/export-to-csv",
     *     tags={"PlaidCompanyController"},
     *     summary="Get CSV file with views info by expenses",
     *     description="Get CSV file with views info by expenses",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/plaid/{companyId}/expenses/export-to-csv
    // OPTIONAL PARAMS
    /*
     * */
    // needs Bearer Token
    public function exportExpensesToCsv(Company $company, ExportExpensesToCsvRequest $request): StreamedResponse
    {
        $validated = $request->validated();

        $data = [];

        $transactionsExpenses = $company->plaidTransactions()
            ->select('date', 'account_id', 'merchant_name', 'amount', 'description', 'plaid_category_id', 'notes', 'ignore', 'category_id')
            ->with(['plaidBankAccount:id,account_id,display_name,mask,plaid_institution_id,type', 'plaidBankAccount.institution:id,institution_id,name',
                'plaidCategory:id,name', 'category:id,name'])
            ->where('date', '>=' , now()->subDays($validated['days'])->startOfDay())
            ->orderByDesc('date')
            ->get();

        $columns = [
            'Date', 'Account Type', 'Account Name', 'Account Number', 'Institution Name', 'Transaction Name',
            'Amount', 'Description', 'Expense Category', 'Stack Category', 'Note', 'Ignored',
        ];

        foreach ($transactionsExpenses as $expense) {
            $data[] = [
                $expense->date, $expense->plaidBankAccount->type, $expense->plaidBankAccount->display_name, $expense->plaidBankAccount->mask,
                $expense->plaidBankAccount->institution->name, $expense->merchant_name, $expense->amount, $expense->description, $expense->plaidCategory->name,
                $expense->category->name ?? '', $expense->notes, $expense->ignore ? 'Yes' : 'No',
            ];
        }

        $fileName = 'company-expenses-' . time() . '.csv';

        return UtilityHelper::getCSVFileResponse($columns, $data, $fileName);
    }

    private function commonsTransactionFilters(
        Company $company,
        Collection $transactionModel
    ): array {
        $company->load('enumType');
        $filters = [];

        if ($company->enumType->value !== CompanyTypeEnum::MSP_CLIENT) {
            $vendors = $transactionModel->pluck('vendor')->filter();
            $filters['vendor'] = TransactionFilters::VENDOR;
            $filters['vendor']['items'] = FilterService::prepareItemCollectionByIdName($vendors);
            $filters['vendor']['items'][] = FilterService::prepareItemUnassigned();
        }

        $products = $transactionModel->pluck('product')->filter();
        $filters['product'] = TransactionFilters::PRODUCT;
        $filters['product']['items'] = FilterService::prepareItemCollectionByIdName($products);
        $filters['product']['items'][] = FilterService::prepareItemUnassigned();

        $categories = $transactionModel->pluck('category')->filter();
        $filters['category'] = TransactionFilters::CATEGORY;
        $filters['category']['items'] = FilterService::prepareItemCollectionByIdName($categories);
        $filters['category']['items'][] = [
            'id' => null,
            'name' => 'Uncategorized',
        ];

        $plaidCategories = $transactionModel->pluck('plaidCategory')->filter();
        $filters['plaid_category'] = TransactionFilters::PLAID_CATEGORY;
        $filters['plaid_category']['items'] = FilterService::prepareItemCollectionByIdName($plaidCategories);

        $plaidBankAccounts = $transactionModel->pluck('plaidBankAccount')->filter();

        if ($plaidBankAccounts->count()) {
            $filters['account'] = TransactionFilters::ACCOUNT;
            $filters['account']['items'] = $plaidBankAccounts
                ->unique()
                ->sortBy('name')
                ->map(fn ($item) => [
                    'id' => $item->account_id,
                    'name' => $item->display_name,
                    'mask' => $item->mask,
                    'institution' => new PlaidInstitutionSimpleResource($item->plaidBankLink->institution),
                ])
                ->values();
        }

        $filters['visibility'] = TransactionFilters::VISIBILITY;
        $filters['visibility']['items'] = [
            [
                'id' => '1',
                'name' => 'Ignored',
            ],
            [
                'id' => '0',
                'name' => 'Not Ignored',
            ],
        ];

        return $filters;
    }

    private function autoFillAndSaveData($transaction, $request, $requestData, Company $company): void
    {
        $isMspClient = $company->enumType->value === CompanyTypeEnum::MSP_CLIENT;
        if ($isMspClient) {
            $productInstance = new ClientProduct();
        } else {
            $productInstance = new Product();
        }

        $saveInfoByProduct = function ($product) use ($requestData, $isMspClient, $transaction) {
            if (!$transaction->vendor_id) {
                if ($isMspClient) {
                    $transaction->vendor_id = $product->owner_id;
                } else {
                    $transaction->vendor_id = $product->company_id;
                }
            }

            if (!$transaction->category_id && !$transaction->sub_category_id && !isset($requestData['category_id'])) {
                if ($isMspClient) {
                    $category = CustomerStack::where('product_id', $product->id)->first()?->category ?? null;
                } else {
                    $category = $product->categories()->first();
                }
                if ($category) {
                    if ($category->parent_id) {
                        $transaction->category_id = $category->parent_id;
                        $transaction->sub_category_id = $category->id;
                    } else {
                        $transaction->category_id = $category->id;
                    }
                }
            }
        };

        if (($request->has('contract_id') && $transaction->contract_id) && !$transaction->product_id && !isset($requestData['product_id'])) {
            $contract = Contract::findOrFail($requestData['contract_id']);
            if ($isMspClient) {
                $product = $contract->clientProduct()->first();
            } else {
                $product = $contract->product()->first();
            }
            if ($product) {
                $transaction->product_id = $product->id;
                $saveInfoByProduct($product);
            }
            if ($contract->category && !isset($requestData['category_id'])) {
                $transaction->category_id = $contract->category->parent_id;
                $transaction->sub_category_id = $contract->category->id;
            }
        }

        if ($request->has('product_id') && $transaction->product_id) {
            $product = $productInstance->findOrFail($requestData['product_id']);
            $saveInfoByProduct($product);
        }

        if ($transaction->sub_category_id && !$transaction->category_id) {
            $transaction->category_id = Category::select('parent_id')->findOrFail($transaction->sub_category_id)->parent_id;
        }

        // Add product to MyStack
        $dirtyData = $transaction->getDirty();
        if ($transaction->product_id && (
            isset($dirtyData['sub_category_id']) ||
            isset($dirtyData['product_id'])
        )) {
            $product = $productInstance->findOrFail($transaction->product_id);
            $loggedUser = AuthService::getAuthUser();

            if ($isMspClient) {
                $myStack = CustomerStack::where('id', $transaction->my_stack_id)->orWhere(function ($query) use ($transaction, $company) {
                    $query->where('company_id', $company->id)
                        ->where('product_id', $transaction->product_id);
                })->first();

                if ($myStack && $transaction->my_stack_id) {
                    $myStack->update([
                        'product_id' => $transaction->product_id,
                        'category_id' => $transaction->sub_category_id,
                    ]);
                }

                if (!$myStack && ($product->category_id || $transaction->sub_category_id)) {
                    $stackProduct = CustomerStack::where('company_id', $company->id)
                        ->where('product_id', $transaction->product_id)
                        ->first();

                    $stackCompany = ClientVendorService::getVendor(
                        $company,
                        $stackProduct->stackCompany ? $stackProduct->stackCompany->id : null,
                        $stackProduct->stackCompany ? $stackProduct->stackCompany->name : $product->name
                    );

                    MyStackCustomerService::validateDuplicateStack(
                        $product->id,
                        $stackCompany->id,
                        $product->category_id ?? $transaction->sub_category_id
                    );

                    // Creating or restoring the stack item
                    $myStack = MyStackCustomerService::createOrRestoreStack(
                        $company->id,
                        $stackCompany->id,
                        $product->id,
                        $transaction->sub_category_id,
                        MyStackPartnerStatus::currentPartner,
                        $loggedUser->id,
                    );
                }

                if ($myStack) {
                    $transaction->my_stack_id = $myStack->id;
                }
            } else {
                $myStack = MyStack::where('id', $transaction->my_stack_id)
                    ->orWhere(function ($query) use ($transaction, $company) {
                        $query->where('company_id', $company->id)
                            ->where('product_id', $transaction->product_id)
                            ->where('category_id', $transaction->sub_category_id);
                    })->first();

                if ($myStack && $transaction->my_stack_id) {
                    $myStack->update([
                        'product_id' => $transaction->product_id,
                        'category_id' => $transaction->sub_category_id ?? $myStack->category_id,
                    ]);
                }

                if (!$myStack && $transaction->sub_category_id) {
                    $myStack = MyStackService::createOrRestoreStack(
                        $company->id,
                        $product->company->id,
                        $product->id,
                        $transaction->sub_category_id,
                        MyStackPartnerStatus::currentPartner,
                        $loggedUser?->id,
                    );
                }

                if ($myStack) {
                    $transaction->my_stack_id = $myStack->id;
                    CompanyClientStackService::addClientStack($myStack, $loggedUser,
                        $stack['client_ids'] ?? []);
                }
            }
        }

        if (!$transaction->product_id) {
            $transaction->my_stack_id = null;
        }

        $transaction->save();
        $transaction->refresh();
    }

    private function getSelectCostQueryRaw(): string
    {
        $currentMonth = Carbon::now()->month;
        $previousMonth = Carbon::now()->subMonth()->month;

        $currentYear = Carbon::now()->year;
        $previousYear = Carbon::now()->subYear()->year;

        return "ROUND(SUM(CASE
            WHEN EXTRACT(MONTH FROM plaid_transactions.date) = {$currentMonth} AND EXTRACT(YEAR FROM plaid_transactions.date) = {$currentYear} THEN plaid_transactions.amount
            ELSE 0
        END), 2) AS current_month_sum,
        ROUND(SUM(CASE
            WHEN EXTRACT(MONTH FROM plaid_transactions.date) = {$previousMonth} AND EXTRACT(YEAR FROM plaid_transactions.date) = {$previousYear} THEN plaid_transactions.amount
            ELSE 0
        END), 2) AS previous_month_sum,
        ROUND(SUM(CASE
            WHEN EXTRACT(YEAR FROM plaid_transactions.date) = {$currentYear} THEN plaid_transactions.amount
            ELSE 0
        END), 2) AS current_year_sum,
        ROUND(SUM(CASE
            WHEN EXTRACT(YEAR FROM plaid_transactions.date) = {$previousYear} THEN plaid_transactions.amount
            ELSE 0
        END), 2) AS previous_year_sum";
    }
}
