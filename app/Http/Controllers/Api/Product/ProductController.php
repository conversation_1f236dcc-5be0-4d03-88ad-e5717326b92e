<?php

namespace App\Http\Controllers\Api\Product;

use App\Enums\AdvertisementStatus;
use App\Enums\AppConfigEnum;
use App\Enums\Company\CompanyProfileTypes;
use App\Enums\ProfileImageType;
use App\Enums\Review\ReviewOrderFilter;
use App\Enums\Review\ReviewStatus;
use App\Enums\SearchType;
use App\Helpers\MediaHelper;
use App\Helpers\PaginateHelper;
use App\Helpers\UtilityHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\AdminSearchRequest;
use App\Http\Requests\Product\ProductDeleteImageRequest;
use App\Http\Requests\Product\ProductDeleteVideoRequest;
use App\Http\Requests\Product\ProductFeatureRequest;
use App\Http\Requests\Product\ProductGraphListingRequest;
use App\Http\Requests\Product\ProductPricingRequest;
use App\Http\Requests\Product\ProductReviewsShowAllRequest;
use App\Http\Requests\Product\ProductSearchRequest;
use App\Http\Requests\Product\ProductStoreRequest;
use App\Http\Requests\Product\ProductTopTrendingRequest;
use App\Http\Requests\Product\ProductUpdateRequest;
use App\Http\Requests\Product\ProductUploadImageRequest;
use App\Http\Requests\Product\ProductUploadVideoRequest;
use App\Http\Resources\Media\ImageResource;
use App\Http\Resources\Product\ProductFeatureResource;
use App\Http\Resources\Product\ProductFullResource;
use App\Http\Resources\Product\ProductGraphListingResource;
use App\Http\Resources\Product\ProductPricingResource;
use App\Http\Resources\Product\ProductSimpleResource;
use App\Http\Resources\Product\ProductTopTrendingResource;
use App\Http\Resources\Review\ReviewResource;
use App\Http\Resources\VideoResource;
use App\Jobs\SyncHubspotCompanyCategory;
use App\Models\Category\Category;
use App\Models\Company\Company;
use App\Models\MyStack\MyStack;
use App\Models\Product;
use App\Models\ProductFeature;
use App\Models\ProductPricing;
use App\Models\Review\Review;
use App\Services\Advertisement\AdvertisementService;
use App\Services\Analytics\AnalyticService;
use App\Services\AppConfig;
use App\Services\AuthService;
use App\Services\BusinessRulesService;
use App\Services\Company\CompanyService;
use App\Services\ImageService;
use App\Services\MediaService;
use App\Services\ProductService;
use App\Services\Search\SearchService;
use App\Services\UserService;
use App\Services\VideoService;
use BenSampo\Enum\Exceptions\InvalidEnumMemberException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Illuminate\Validation\ValidationException;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class ProductController extends Controller
{
    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/products",
     *     operationId="showAllProducts",
     *     tags={"ProductController"},
     *     summary="Show all products",
     *     description="Show all products",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/products
    /*
       ?paged=BOOLEAN&page=PAGE&items_per_page
        &order_by=COLUMN_NAME&sort=SORT_VALUE
        &search_word=SEARCH_WORD
     * */
    // needs Bearer Token
    public function showAllProducts(ProductSearchRequest $request)
    {
        if (!empty($request->search_word)) {
            SearchService::saveSearchPerformed($request->search_word, SearchType::ProductSearch);
        }
        if ($request->has('order_by')) {
            if ($request->order_by === 'company.name') {
                $request->merge(['order_by' => 'companies.name']);
            }
            if ($request->order_by === 'name') {
                $request->merge(['order_by' => 'products.name']);
            }
        }
        $query = $this->prepareShowAllProductsQuery($request);
        $result = UtilityHelper::getSearchRequestQueryResults($request, $query);
        if ($result instanceof LengthAwarePaginator) {
            $pageResults = $result->getCollection();
        } else {
            $pageResults = $result;
        }

        ImageService::appendCompanyAvatars($pageResults->pluck('company')->keyBy('id'));
        ProductService::calculateProductReviewsStatistics($pageResults);
        if ($request->has('rating') && $request->rating !== null) {
            $pageResults = $pageResults->where('rating', '=', $request->rating);
        }

        if ($result instanceof LengthAwarePaginator) {
            $result->setCollection($pageResults);
        } else {
            $result = $pageResults;
        }

        return ProductSimpleResource::collection($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/products/search",
     *     operationId="searchProducts",
     *     tags={"ProductController"},
     *     summary="Get products simplified version",
     *     description="Get products simplified version",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/products/search
    /*
       ?paged=BOOLEAN&page=PAGE&items_per_page
        &search_word=SEARCH_WORD
        &category_id=sometimes|numeric|exists:categories,id
        &msp_company_id=nullable|numeric|exists:companies,id
     * */
    // needs Bearer Token
    public function searchProducts(ProductSearchRequest $request): AnonymousResourceCollection
    {
        $sponsoredVendors = collect();
        if ($request->has('category_id')) {
            $sponsoredVendors = $this->getSponsoredVendors($request)->sortBy('company.name');
        }
        $notSponsoredVendors = $this->getNotSponsoredVendors(
            $request,
            $sponsoredVendors->pluck('company')->pluck('id')->unique()->toArray()
        )->sortBy(['case', 'company.name']);
        $result = $sponsoredVendors->merge($notSponsoredVendors);
        if ($request->has('msp_company_id')) {
            $stackProductIds = MyStack::where('category_id', $request->category_id)
                ->where('company_id', $request->msp_company_id)->pluck('product_id')->toArray();
            $result = $result->whereNotIn('id', $stackProductIds);
        }
        if ($request->has('paged') && $request->paged) {
            $result = PaginateHelper::paginate($request, $result, 'page', $request->items_per_page ?? 0);
        }
        if ($result instanceof LengthAwarePaginator) {
            $pageResults = $result->getCollection();
        } else {
            $pageResults = $result;
        }
        $companies = $pageResults->pluck('company')->keyBy('id');
        ImageService::appendCompanyAvatars($companies);
        AdvertisementService::appendCompanyShowAsSponsored($companies, $request->category_id);
        if ($result instanceof LengthAwarePaginator) {
            $result->setCollection($pageResults);
        } else {
            $result = $pageResults;
        }

        return ProductSimpleResource::collection($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/products/top-trending",
     *     operationId="/api/v1/products/top-trending",
     *     summary="Get top trending products",
     *     description="Get top trending products",
     *     tags={"ProductController"},
     *
     *     @OA\Parameter(
     *         name="period",
     *         in="query",
     *         description="Time period for trending products (required with period_type)",
     *
     *         @OA\Schema(type="integer", example=7)
     *     ),
     *
     *     @OA\Parameter(
     *         name="period_type",
     *         in="query",
     *         description="Type of time period (days, weeks, months, years) (required with period)",
     *
     *         @OA\Schema(type="string", enum={"days", "weeks", "months", "years"})
     *     ),
     *
     *     @OA\Parameter(
     *         name="num_of_products",
     *         in="query",
     *         description="Number of top trending products to retrieve (optional)",
     *
     *         @OA\Schema(type="integer", minimum=1, example=10)
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/products/top-trending
    // Bearer Token NOT needed
    public function topTrending(ProductTopTrendingRequest $request): AnonymousResourceCollection
    {
        $endDate = now()->endOfDay();
        $startDate = null;
        if ($request->has('period') && $request->has('period_type')) {
            switch ($request->period_type) {
                case 'days':
                    $startDate = $endDate->clone()->subDays($request->period)->startOfDay();

                    break;
                case 'weeks':
                    $startDate = $endDate->clone()->subWeeks($request->period)->startOfDay();

                    break;
                case 'months':
                    $startDate = $endDate->clone()->subMonths($request->period)->startOfDay();

                    break;
                case 'years':
                    $startDate = $endDate->clone()->subYears($request->period)->startOfDay();

                    break;
                default:
                    $endDate = null;

                    break;
            }
        } else {
            $startDate = $endDate->clone()->subDays((int)AppConfig::loadAppConfigByKey(
                AppConfigEnum::TOP_TRENDING_INITIAL_DAYS, config('common.topTrendingInitialDays'))->value
            )->startOfDay();
        }
        $listOfDontShowComp = AppConfig::loadAppConfigByKey('DONT_SHOW_COMPANIES_ON_STACK_CHART_OR_TOP_TRENDING');
        $companiesIds = CompanyService::getCompaniesIdsFromStringList($listOfDontShowComp->value);
        $companiesIds[] = config('custom.channel_program_company.id');
        $productsIgnoreList = Product::select('id')
            ->whereIn('company_id', $companiesIds)
            ->get()
            ->pluck('id')
            ->toArray();
        $products = ProductService::getTopTrendingResults(
            collect(),
            $startDate,
            $endDate,
            $request->num_of_products ?? (int)AppConfig::loadAppConfigByKey(
                AppConfigEnum::TOP_TRENDING_PRODUCTS,
                config('common.topTrendingProducts'))->value,
            $productsIgnoreList
        );

        return ProductTopTrendingResource::collection($products);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/products/graph-listing",
     *     operationId="products/graphListing",
     *     tags={"ProductController"},
     *     summary="Show products for graph listing",
     *     description="Show products for graph listing",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/products/graph-listing
    // needs Bearer Token
    public function graphListing(ProductGraphListingRequest $request): AnonymousResourceCollection
    {
        $listOfDontShowComp = AppConfig::loadAppConfigByKey('DONT_SHOW_COMPANIES_ON_STACK_CHART_OR_TOP_TRENDING');
        $companiesIds = CompanyService::getCompaniesIdsFromStringList($listOfDontShowComp->value);
        $productsQuery = Product::with('company.avatar', 'categories', 'reviews');
        if (count($companiesIds) > 0) {
            $productsQuery->whereNotIn('company_id', $companiesIds);
        }
        if ($request->has('categories')) {
            $productsQuery->whereHas('categories', function ($q) use ($request) {
                $q->whereIn('categories.id', $request->categories)
                    ->orWhereIn('categories.parent_id', $request->categories);
            });
        }
        $products = $productsQuery->get();
        ProductService::calculateProductReviewsStatistics($products);
        ProductService::calculateProductsRecommendationRatings($products);
        if ($request->has('order_by')) {
            if ($request->sort === 'ASC' && $request->has('sort')) {
                $result = $products->sortBy($request->order_by);
            } else {
                $result = $products->sortByDesc($request->order_by);
            }
        } else {
            $products->transform(function ($product) {
                $product->default_ordering = $product->total_reviews + $product->rating;

                return $product;
            });
            $result = $products->sortBy('name')->sortByDesc('default_ordering');
        }
        $result = $result->flatten();
        if ($request->has('paged') && $request->paged) {
            $result = PaginateHelper::paginate(
                $request,
                $result->flatten(),
                'page',
                $request->items_per_page ?? config('common.searchPagingLength')
            );
        }
        if ($result instanceof LengthAwarePaginator) {
            $pageResults = $result->getCollection();
        } else {
            $pageResults = $result;
        }
        BusinessRulesService::addCompanyStackChartVisibility($result->pluck('company'));
        ImageService::appendCompanyAvatars($result->pluck('company')->keyBy('id'));
        if ($result instanceof LengthAwarePaginator) {
            $result->setCollection($pageResults);
        } else {
            $result = $pageResults;
        }

        return ProductGraphListingResource::collection($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/products/{friendly_url}",
     *     operationId="products/{friendly_url}/showByFriendlyUrl",
     *     tags={"ProductController"},
     *     summary="Show a product by friendly url",
     *     description="Show a product by friendly url",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/products/{friendly_url}
    // needs Bearer Token
    public function showByFriendlyUrl(Product $product): ProductFullResource
    {
        $product->images = $product->loadImages();
        $product->videos = $product->loadVideos();
        $product->load(['company', 'categories', 'languages']);
        $product->company->avatar = ImageService::findAvatarInDB(
            $product->company,
            ProfileImageType::CompanyAvatar,
            false
        );
        ProductService::calculateProductReviewsStatistics(collect([$product]));

        return new ProductFullResource($product);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/products/{friendly_url}/reviews",
     *     operationId="loadProductReviews",
     *     tags={"ProductController"},
     *     summary="Show product reviews by friendly url",
     *     description="Show product reviews by friendly url",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/products/{friendly_url}/reviews
    // PARAMS
    // ?review_id=sometimes|required|numeric|exists:reviews,id
    // &review_order=sometimes|required|allowedValues
    // needs Bearer Token
    public function loadProductReviews(ProductReviewsShowAllRequest $request, Product $product)
    {
        $selectedFields = [
            'reviews.*',
            DB::raw('ROUND(AVG(review_question_options.impact_rating_value), 2) AS rating'),
            DB::raw('COUNT(analytics.id) AS total_likes'),
        ];

        $query = Review::with(
            'replies',
            'answers',
            'reviewer.avatar',
            'reviewer.userProfileType',
            'answers.question',
            'answers.option'
        )
            ->select($selectedFields)
            ->join('products', 'reviews.model_id', '=', 'products.id')
            ->join('review_answers', 'reviews.id', '=', 'review_answers.review_id')
            ->join('review_questions', 'review_questions.id', '=', 'review_answers.review_question_id')
            ->join('review_question_options', 'review_answers.review_question_option_id', '=', 'review_question_options.id')
            ->leftJoin('analytics', function ($join) {
                $join->on('reviews.id', '=', 'analytics.subject_id')
                    ->where('analytics.action', '=', 'like');
            })
            ->where('products.id', $product->getKey())
            ->where('reviews.status', ReviewStatus::approved)
            ->groupBy(DB::raw(1));

        if ($request->has('review_id')) {
            $query->orderByRaw('reviews.id = ' . $request->review_id . ' DESC');
            $query->orderBy('reviews.created_at', 'desc');
        }

        if ($request->has('review_order')) {
            $query = $this->generateReviewOrderQuery($request, $query);
        } else {
            $query->orderBy('created_at', 'DESC');
        }

        $result = $query->paginate($request->has('items_per_page') ?
            $request->items_per_page : config('common.searchPagingLength'));

        if ($result instanceof LengthAwarePaginator) {
            $pageResults = $result->getCollection();
        } else {
            $pageResults = $result;
        }

        $pageResults = AnalyticService::appendLikesCounts($pageResults, UserService::getLoggedUserLikes());
        $reviewer = $pageResults->pluck('reviewer');
        ImageService::appendUsersAvatars($reviewer);

        if ($result instanceof LengthAwarePaginator) {
            $result->setCollection($pageResults);
        } else {
            $result = $pageResults;
        }

        return ReviewResource::collection($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{company}/product",
     *     operationId="company/{company}/product/showAll",
     *     tags={"ProductController"},
     *     summary="Show all products for a company",
     *     description="Show all products for a company",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{company}/product
    // needs Bearer Token
    public function showAll(Company $company): AnonymousResourceCollection
    {
        $products = Product::with(
            'categories',
            'reviews',
            'reviews.answers',
            'reviews.answers.question',
            'reviews.answers.option',
            'reviews.reviewer',
            'reviews.reviewer.userProfileType'
        )
            ->where('company_id', $company->id)
            ->orderBy('order')
            ->get();

        $products = ProductService::appendImages($products);
        $products = ProductService::appendVideos($products);
        $products = ProductService::calculateProductReviewsStatistics($products);
        $products = ProductService::appendReviewersAvatars($products);

        return ProductFullResource::collection($products);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{company}/product/{product}",
     *     operationId="company/{company}/product/findById",
     *     tags={"ProductController"},
     *     summary="Show a product for a company",
     *     description="Show a product for a company",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{company}/product/{product}
    // needs Bearer Token
    public function findById(Company $company, Product $product): ProductFullResource
    {
        $product->images = $product->loadImages();
        $product->videos = $product->loadVideos();
        $product->load(['categories', 'languages']);
        ProductService::calculateProductReviewsStatistics(collect([$product]));

        return new ProductFullResource($product);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/{company}/product/{product}/store",
     *     operationId="company/{company}/product/store",
     *     tags={"ProductController"},
     *     summary="store a product for a company",
     *     description="store a product for a company",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/{company}/product/{product}/store
    /*BODY
    {
        "name": "required|string|max:255|Profanity",
        "description": "required|string|Profanity",
        "url": "nullable|max:2048",
        "product_categories_ids": ["CATEGORY_ID_1", "CATEGORY_ID_2", "CATEGORY_ID_3"],
        'features' => 'nullable|array',
        'features.*.title' =>  ['sometimes', 'string', new Profanity],
        'features.*.description' =>  ['sometimes', 'nullable', 'string', new Profanity],
        'features.*.display_on_feed' => ['sometimes', 'nullable', 'boolean'],
        'pricing' => 'nullable|array',
        'pricing.*.title' =>  ['string', 'max:50', new Profanity,'required_with:pricing'],
        'pricing.*.description' =>  ['sometimes','nullable', 'string', 'max:125', new Profanity, 'required_with:pricing'],
        'pricing.*.price' => ['numeric', 'min:0', 'required_with:pricing'],
        'pricing.*.pricing_type' => [new AllowedValues(ProductPricingType::getValues()), 'required_with:pricing'],
    }*/
    // needs Bearer Token
    public function store(ProductStoreRequest $request, Company $company): ProductFullResource
    {
        $response = Gate::inspect('add-company-product', $company);
        if (!$response->allowed()) {
            abort(403, $response->message());
        }
        $loggedUser = AuthService::getAuthUser();
        $product = ProductService::storeProduct($request->validated(), $company, $loggedUser);

        return new ProductFullResource($product);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/company/{company}/product/{product}/update",
     *     operationId="company/{company}/product/update",
     *     tags={"ProductController"},
     *     summary="update a product for a company",
     *     description="update a product for a company",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: PUT
    // http://127.0.0.1:8000/api/v1/company/{company}/product/{product}/update
    /*BODY
    {
        "id": "required|numeric|exists:products,id",
        "name": "required|string|max:255|Profanity",
        "description": "required|string|Profanity",
        "url": "nullable|max:2048",
        "product_categories_ids": ["CATEGORY_ID_1", "CATEGORY_ID_2", "CATEGORY_ID_3"],
        'features' => 'nullable|array',
        'features.*.title' =>  ['required_with:features', 'string', new Profanity],
        'features.*.description' =>  [ 'nullable', 'string', new Profanity],
        'features.*.display_on_feed' => ['sometimes', 'nullable', 'boolean'],
        'pricing' => 'nullable|array',
        'pricing.*.title' =>  ['required_with:pricing', 'string', 'max:50', new Profanity],
        'pricing.*.description' =>  ['sometimes', 'required_with:pricing', 'nullable', 'string', 'max:125', new Profanity],
        'pricing.*.price' => ['required_with:pricing', 'numeric', 'min:0'],
        'pricing.*.pricing_type' => [new AllowedValues(ProductPricingType::getValues()), 'required_with:pricing'],
    }*/
    // needs Bearer Token
    public function update(ProductUpdateRequest $request, Company $company): ProductFullResource
    {
        $product = Product::findOrFail($request->id);
        $attributes = $request->validated();
        // if request has name and name is different from the current name then generate a new friendly url
        if ($request->has('name') && $request->name !== $product->name) {
            $friendlyUrl = UtilityHelper::generateUniqueWord('products', 'friendly_url', $request->name, '-');
            $attributes['friendly_url'] = $friendlyUrl;
        }
        $product->update($attributes);
        if ($request->has('product_categories_ids')) {
            $subCategoryIds = Category::whereIn('id', $request->product_categories_ids)->whereNotNull('parent_id')
                ->pluck('id');
            $product->categories()->sync($subCategoryIds);
            SyncHubspotCompanyCategory::dispatch($company);
        }
        if ($request->has('features') && !empty($request->features)) {
            // delete all existing product features
            $product->features()->delete();
            // create new product features
            $product->features()->createMany($request->features);
        }
        if ($request->has('pricing')) {
            $product->pricing()->delete();
            $product->pricing()->createMany($request->pricing);
        }

        return new ProductFullResource($product);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Delete(
     *     path="/api/v1/company/{company}/product/delete/{product}",
     *     operationId="company/{company}/product/delete",
     *     tags={"ProductController"},
     *     summary="delete a product for a company",
     *     description="delete a product for a company",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/{company}/product/delete/{product}
    /*BODY
    {
        "id": "required|numeric|exists:products,id",
    }*/
    // needs Bearer Token
    public function delete(Company $company, Product $product): JsonResponse
    {
        ProductService::checkProductUsage($product);
        $product->features()->delete();
        $product->pricing()->delete();
        $product->reviewsAll()->delete();
        $product->delete();

        return response()->json();
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/{company}/product/{product}/image/upload",
     *     operationId="company/{company}/product/uploadImage",
     *     tags={"ProductController"},
     *     summary="Uploads an image to a product",
     *     description="Uploads an image to a product",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/{company}/product/{product}/image/upload
    /* BODY
    {
    	"image": "required|image|max:5000|mimes:jpeg,jpg,png,gif,svg,tif,tiff,webp",
        "title": "sometimes|required|string|max:100|Profanity check",
        "description": "sometimes|required|string|max:100|Profanity check"
    }*/
    // needs Bearer Token
    public function uploadImage(ProductUploadImageRequest $request, Company $company, Product $product): ImageResource
    {
        $customProperties = MediaHelper::setCustomProperties($request);
        $media = $product->addMedia($request->image)
            ->withCustomProperties($customProperties)
            ->toMediaCollection(config('custom.media_collections.product_main_image'));

        return new ImageResource($media);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Delete(
     *     path="/api/v1/company/{company}/product/{product}/image/delete",
     *     operationId="company/{company}/product/deleteImage",
     *     tags={"ProductController"},
     *     summary="Deletes an image from a product",
     *     description="Deletes an image from a product",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: DELETE
    // http://127.0.0.1:8000/api/v1/company/{company}/product/{product}/image/delete
    /* BODY
    {
    	"id": "required|numeric|exists:media,id"
    }*/
    // needs Bearer Token
    public function deleteImage(ProductDeleteImageRequest $request): JsonResponse
    {
        $image = Media::findOrFail($request->id);
        $image->delete();

        return response()->json();
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/{company}/product/{product}/video/upload",
     *     operationId="company/{company}/product/uploadVideo",
     *     tags={"ProductController"},
     *     summary="Uploads a video to a product",
     *     description="Uploads a video to a product",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/{company}/product/{product}/video/upload
    /* BODY
    {
    	"video" : "required|file|mimes:mp4,ogx,oga,ogv,ogg,webm,mov,avi,wmv,mkv",
        "title" : ["sometimes", "string", new Profanity],
        "description" : ["sometimes", "string", new Profanity],
        "thumbnail" : "required|image|max:5000|mimes:jpeg,jpg,png,gif,svg,tif,tiff,webp",
        "is_partner_content": "sometimes|boolean"
    }*/
    // needs Bearer Token
    public function uploadVideo(ProductUploadVideoRequest $request, Company $company, Product $product): JsonResponse
    {
        $customProperties = MediaHelper::setCustomProperties($request);
        $media = $product->addMedia($request->video)
            ->withCustomProperties($customProperties)
            ->toMediaCollection(config('custom.media_collections.product_main_video'));

        if ($request->thumbnail) {
            $thumbnailMedia = VideoService::addThumbnailToMedia($product, $request->thumbnail, $media);
            $media->thumbnail = $thumbnailMedia;
        }

        return response()->json([new VideoResource($media)], 201);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Delete(
     *     path="/api/v1/company/{company}/product/{product}/video/delete",
     *     operationId="company/{company}/product/deleteVideo",
     *     tags={"ProductController"},
     *     summary="Deletes a video from a product",
     *     description="Deletes a video from a product",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: DELETE
    // http://127.0.0.1:8000/api/v1/company/{company}/product/{product}/video/delete
    /* BODY
    {
    	"id": "required|numeric|exists:media,id"
    }*/
    // needs Bearer Token
    public function deleteVideo(ProductDeleteVideoRequest $request, Company $company, Product $product): JsonResponse
    {
        $video = Media::findOrFail($request->id);
        MediaService::deleteMedia($video);

        return response()->json();
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/{company}/product/{product}/feature/store",
     *     operationId="company/{company}/product/storeProductFeature",
     *     tags={"ProductController"},
     *     summary="to add product feature",
     *     description="to add product feature",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/{company}/product/{product}/feature/store
    /* BODY
    {
        "title": "required|string|max:100|Profanity check",
        "description": "sometimes|required|string|max:100|Profanity check"
    }*/
    // needs Bearer Token
    public function storeProductFeature(ProductFeatureRequest $request, Company $company, Product $product): ProductFeatureResource
    {
        $product_feature = $product->features()->create($request->validated());

        return new ProductFeatureResource($product_feature);
    }
    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{company}/product/{product}/feature/{product_feature}",
     *     operationId="company/{company}/product/viewProductFeature",
     *     tags={"ProductController"},
     *     summary="view product feature",
     *     description="view product feature",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{company}/product/{product}/feature/{product_feature}

    // needs Bearer Token
    public function viewProductFeature(Company $company, Product $product, ProductFeature $product_feature): ProductFeatureResource
    {
        return new ProductFeatureResource($product_feature);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/company/{company}/product/{product}/feature/{product_feature}",
     *     operationId="company/{company}/product/updateProductFeature",
     *     tags={"ProductController"},
     *     summary="update product feature",
     *     description="update product feature",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: PUT
    // http://127.0.0.1:8000/api/v1/company/{company}/product/{product}/feature/{product_feature}
    /* BODY
    {
        "title": "sometimes|required|string|max:100|Profanity check",
        "description": "sometimes|required|string|max:100|Profanity check"
    }*/
    // needs Bearer Token
    public function updateProductFeature(ProductFeatureRequest $request, Company $company, Product $product, ProductFeature $product_feature): ProductFeatureResource
    {
        $product_feature->update($request->validated());

        return new ProductFeatureResource($product_feature);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Delete(
     *     path="/api/v1/company/{company}/product/{product}/feature/{product_feature}",
     *     operationId="company/{company}/product/deleteProductFeature",
     *     tags={"ProductController"},
     *     summary="to delete product feature",
     *     description="to delete product feature",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: DELETE
    // http://127.0.0.1:8000/api/v1/company/{company}/product/{product}/feature/{product_feature}

    // needs Bearer Token
    public function deleteProductFeature(Company $company, Product $product, ProductFeature $product_feature): JsonResponse
    {
        $product_feature->delete();

        return response()->json();
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{company}/product/{product}/pricing",
     *     operationId="company/{company}/product/showAllProductPricing",
     *     tags={"ProductController"},
     *     summary="to display all product pricing for current product",
     *     description="to display all product pricing for current product",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{company}/product/{product}/pricing
    /* BODY
    {
        "search": "sometimes|array|Allowed search fields: 'title'=>'like', 'description'=>'like' , 'price', 'pricing_type'",
    }*/
    // needs Bearer Token
    public function showAllProductPricing(ProductPricingRequest $request, Company $company, Product $product)
    {
        $query = $product->pricing();
        $product_pricing = UtilityHelper::prepareSearchQuery($query, $request->search, $query->getModel())->get();

        return ProductPricingResource::collection($product_pricing);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/{company}/product/{product}/pricing/store",
     *     operationId="company/{company}/product/storeProductPricing",
     *     tags={"ProductController"},
     *     summary="to add product pricing",
     *     description="to add product pricing",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/{company}/product/{product}/pricing/store
    /* BODY
    {
        "title": "required|string|max:50|Profanity check",
        "description": "sometimes|required|string|max:150|Profanity check"
        "price": "required|numeric|min:0"
        "pricing_type": "required|AllowedValues(ProductPricingType::getValues())|unique:pricing_type for this product"
    }*/
    // needs Bearer Token
    public function storeProductPricing(ProductPricingRequest $request, Company $company, Product $product): ProductPricingResource
    {
        $product = $product->pricing()->create($request->validated());

        return new ProductPricingResource($product);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{company}/product/{product}/pricing/{product_pricing}",
     *     operationId="company/{company}/product/viewProductPricing",
     *     tags={"ProductController"},
     *     summary="view product pricing",
     *     description="view product pricing",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{company}/product/{product}/pricing/{product_pricing}

    // needs Bearer Token
    public function viewProductPricing(Company $company, Product $product, ProductPricing $product_pricing): ProductPricingResource
    {
        return new ProductPricingResource($product_pricing);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/company/{company}/product/{product}/pricing/{product_pricing}",
     *     operationId="company/{company}/product/updateProductPricing",
     *     tags={"ProductController"},
     *     summary="update product pricing",
     *     description="update product pricing",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: PUT
    // http://127.0.0.1:8000/api/v1/company/{company}/product/{product}/pricing/{product_pricing}
    /* BODY
    {
        "title": "sometimes|string|max:50|Profanity check",
        "description": "sometimes|string|max:150|Profanity check"
        "price": "sometimes|numeric|min:0"
        "pricing_type": "sometimes|AllowedValues(ProductPricingType::getValues())|unique:pricing_type for this product"

    }*/
    // needs Bearer Token
    public function updateProductPricing(ProductPricingRequest $request, Company $company, Product $product, ProductPricing $product_pricing): ProductPricingResource
    {
        $product_pricing->update($request->validated());

        return new ProductPricingResource($product_pricing);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Delete(
     *     path="/api/v1/company/{company}/product/{product}/pricing/{product_pricing}",
     *     operationId="company/{company}/product/deleteProductPricing",
     *     tags={"ProductController"},
     *     summary="to delete pricing",
     *     description="to delete pricing",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: DELETE
    // http://127.0.0.1:8000/api/v1/company/{company}/product/{product}/pricing/{product_pricing}

    // needs Bearer Token
    public function deleteProductPricing(Company $company, Product $product, ProductPricing $product_pricing): JsonResponse
    {
        $product_pricing->delete();

        return response()->json();
    }

    private function prepareShowAllProductsQuery(AdminSearchRequest $request)
    {
        return Product::with('company.avatar', 'categories')
            ->select(['products.*', DB::raw('true as "show_as_sponsored"'), 'companies.company_profile_types_id'])
            ->whereHas('categories', function ($query) use ($request) {
                $query->whereRaw("(lower(categories.name) like '%" .
                    strtolower($request->search_word) . "%')");
            })->orWhereRaw("(lower(companies.name) like '%" .
                strtolower($request->search_word) . "%')")
            ->orWhereRaw("(lower(products.name) like '%" . strtolower($request->search_word) . "%')")
            ->join('companies', 'products.company_id', '=', 'companies.id')
            ->join('company_profile_types', 'company_profile_types_id', '=', 'company_profile_types.id')
            ->when(empty($request->order_by), function ($query) {
                $query->orderBy('company_profile_types.order')
                    ->orderByRaw('UPPER(products.name) ASC');
            });
    }

    private function getSponsoredVendors(AdminSearchRequest $request)
    {
        $useFuzzy = $request->has('search_word') && $request->has('use_fuzzy_search') && (bool)$request->use_fuzzy_search;
        $searchTerms = $useFuzzy
            ? explode(' ', strtolower($request->search_word))
            : null;
        $result = Product::with('company.companyProfileType', 'company.avatar', 'categories.parentCategory')
            ->select([DB::raw('companies.name AS company_name'), 'products.*',
                'company_profile_types.value', DB::raw('company_profile_types.id as "case"')])
            ->join('companies', 'products.company_id', '=', 'companies.id')
            ->join('product_categories',
                'product_categories.product_id', '=', 'products.id')
            ->join('advertisements',
                'advertisements.subject_id', '=', 'companies.id')
            ->join('advertisement_card_types',
                'advertisement_card_types.id', '=', 'advertisements.advertisement_card_type_id')
            ->join('company_profile_types',
                'company_profile_types.id', '=', 'companies.company_profile_types_id')
            ->when($request->has('ignore_ids'), function ($query) use ($request) {
                $query->whereNotIn('products.id', explode(',', $request->ignore_ids));
            })
            ->where(function ($q) use ($request, $useFuzzy, $searchTerms) {
                $q->where('advertisements.status', AdvertisementStatus::active)
                    ->whereRaw('now() BETWEEN advertisements.start_date and advertisements.end_date')
                    ->where('advertisement_card_types.key', 'NAVISTACKSPONSORED')
                    ->when($request->has('search_word'), function ($query) use ($request, $useFuzzy, $searchTerms) {
                        $query->where(function ($query) use ($request, $useFuzzy, $searchTerms) {
                            $searchWordLikeParameter = '%' . strtolower($request->search_word) . '%';
                            $query->whereRaw('lower(companies.name) like ?', [$searchWordLikeParameter])
                                ->orWhereRaw('lower(products.name) like ?', [$searchWordLikeParameter]);
                            // Applying fuzzy search
                            if ($useFuzzy) {
                                foreach ($searchTerms as $term) {
                                    $query->orWhereRaw("similarity(products.name, '" . $term . "') > 0");
                                }
                            }
                        });
                    })
                    ->when($request->has('category_id'), function ($query) use ($request) {
                        $query->where('product_categories.category_id', $request->category_id)
                            ->where('advertisements.category_id', $request->category_id);
                    });
            });

        if (!empty($request->company_id)) {
            $result->where(['company_id' => $request->company_id]);
        }

        return $result->distinct()
            ->orderBy('company_name')
            ->orderBy('products.name')
            ->get();
    }

    private function getNotSponsoredVendors(AdminSearchRequest $request, array $sponsoredVendorsIds = [])
    {
        $useFuzzy = $request->has('search_word') && $request->has('use_fuzzy_search') && (bool)$request->use_fuzzy_search;
        $searchTerms = $useFuzzy
            ? explode(' ', strtolower($request->search_word))
            : null;
        $query = Product::with('company.companyProfileType', 'company.avatar', 'categories.parentCategory')
            ->select([DB::raw('companies.name AS company_name'), 'products.*',
                DB::raw("CASE WHEN company_profile_types.value = '"
                    . CompanyProfileTypes::VendorEnterprise . "' THEN 1
                        WHEN company_profile_types.value = '" . CompanyProfileTypes::VendorPremium . "'  THEN 2
                        WHEN company_profile_types.value = '" . CompanyProfileTypes::VendorPlus . "' THEN 3
                        WHEN company_profile_types.value = '" . CompanyProfileTypes::VendorBasic . "' THEN 4
                        WHEN company_profile_types.value = '" . CompanyProfileTypes::VendorFree . "' THEN 5
                        ELSE 6 END")])
            ->join('companies', 'products.company_id', '=', 'companies.id')
            ->join('product_categories', 'product_categories.product_id', '=', 'products.id')
            ->join('company_profile_types', 'company_profile_types.id', '=', 'companies.company_profile_types_id')
            ->when($request->has('ignore_ids'), function ($query) use ($request) {
                $query->whereNotIn('products.id', explode(',', $request->ignore_ids));
            })
            ->where(function ($q) use ($request, $sponsoredVendorsIds, $useFuzzy, $searchTerms) {
                $q->whereNotIn('companies.id', $sponsoredVendorsIds)
                    ->when($request->has('search_word'), function ($query) use ($request, $useFuzzy, $searchTerms) {
                        $query->where(function ($query) use ($request, $useFuzzy, $searchTerms) {
                            $searchWordLikeParameter = '%' . strtolower($request->search_word) . '%';
                            $query->whereRaw('lower(companies.name) like ?', [$searchWordLikeParameter])
                                ->orWhereRaw('lower(products.name) like ?', [$searchWordLikeParameter]);
                            // Applying fuzzy search
                            if ($useFuzzy) {
                                foreach ($searchTerms as $term) {
                                    $query->orWhereRaw("similarity(products.name, '" . $term . "') > 0");
                                }
                            }
                        });
                    })
                    ->when($request->has('category_id'), function ($query) use ($request) {
                        $query->where('product_categories.category_id', $request->category_id);
                    });
            });

        if (!empty($request->company_id)) {
            $query->where(['company_id' => $request->company_id]);
        }

        $query->distinct()
            ->orderByRaw("CASE WHEN company_profile_types.value = '"
                . CompanyProfileTypes::VendorEnterprise . "' THEN 1
                    WHEN company_profile_types.value = '" . CompanyProfileTypes::VendorPremium . "'  THEN 2
                    WHEN company_profile_types.value = '" . CompanyProfileTypes::VendorPlus . "' THEN 3
                    WHEN company_profile_types.value = '" . CompanyProfileTypes::VendorBasic . "' THEN 4
                    WHEN company_profile_types.value = '" . CompanyProfileTypes::VendorFree . "' THEN 5
                    ELSE 6 END")
            ->orderBy('company_name')
            ->orderBy('products.name');

        return $query->get();
    }

    /**
     * @throws InvalidEnumMemberException
     * @throws ValidationException
     */
    private function generateReviewOrderQuery($request, $query)
    {
        switch ($request->review_order) {
            case ReviewOrderFilter::getKey(ReviewOrderFilter::HighestSatisfactionRating):
                $query->orderBy('rating', 'DESC');

                break;
            case ReviewOrderFilter::getKey(ReviewOrderFilter::LowestSatisfactionRating):
                $query->orderBy('rating', 'ASC');

                break;
            case ReviewOrderFilter::getKey(ReviewOrderFilter::MostRecent):
                $query->orderByRaw('reviews.created_at DESC');

                break;
            case ReviewOrderFilter::getKey(ReviewOrderFilter::MostLiked):
                $query->orderByRaw('total_likes DESC');

                break;
            default:
                throw ValidationException::withMessages([
                    'ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::Logic not implemented for review_order value:' . $request->review_order,
                ]);
        }

        return $query;
    }
}
