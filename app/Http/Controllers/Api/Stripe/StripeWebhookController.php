<?php

namespace App\Http\Controllers\Api\Stripe;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class StripeWebhookController extends Controller
{
    public function handle(Request $request): JsonResponse
    {
        Log::info('Stripe Webhook:', $request->all());
        return response()->json(['status' => 'ok']);
    }
}
