<?php

namespace App\Http\Controllers\Auth;

use App\Enums\Company\CompanyType;
use App\Enums\ProfileImageType;
use App\Enums\UserCountNotificationAction;
use App\Enums\UserStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Http\Resources\ClaimedCompaniesResource;
use App\Http\Resources\Company\CompanyClaimerResponseSimpleResource;
use App\Http\Resources\JobTitleResource;
use App\Http\Resources\UserResource;
use App\Models\AffiliateBrand\AffiliateBrand;
use App\Models\Company\Company;
use App\Models\Company\CompanyClaimer;
use App\Models\Company\CompanyClaimerResponse;
use App\Models\Company\CompanyClient;
use App\Services\Analytics\AnalyticService;
use App\Services\AuthService;
use App\Services\Company\CompanyClientService;
use App\Services\Company\CompanyService;
use App\Services\ImageService;
use App\Services\Notification\AdminUserNotificationService;
use App\Services\UserService;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use Illuminate\View\View;

class AuthenticatedSessionController extends Controller
{
    /**
     * Display the login view.
     */
    public function create(): View
    {
        return view('auth.login');
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/login",
     *     operationId="login",
     *     tags={"AuthenticatedSessionController"},
     *     summary="Log user",
     *     description="Handle an incoming authentication request",
     *
     *     @OA\RequestBody(
     *         required=true,
     *
     *         @OA\JsonContent(ref="#/components/schemas/LoginRequest")
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/login
    // BODY
    // {
    //	'email': '<EMAIL>',
    //  'password': 'P@ssw0rd!',
    //  'parent_id': 'sometimes|required|numeric|exists:companies,id',
    //  'affiliate_brand_id': 'sometimes|required|numeric|exists:affiliate_brands,id',
    // }
    // no Bearer Token
    /**
     * Handle an incoming authentication request.
     *
     *
     * @throws ValidationException|Exception
     */
    public function login(LoginRequest $request): JsonResponse
    {
        $token = $request->authenticate();
        $user = AuthService::getAuthUser();

        if (is_null($user->company)) {
            throw ValidationException::withMessages([
                'message' => config('genericMessages.error.USER_HAS_NO_COMPANY'),
            ]);
        }

        if ($user->getAttributeValue('status') === UserStatus::Inactive) {
            abort(403, config('genericMessages.error.USER_IS_INACTIVE'));
        }
        if ($user->handle === null || $user->friendly_url === null) {
            UserService::updateUserFields($user);
        }
        $lastLoggedInBeforeActualLogin = $user->last_logged_in_at;
        $user->last_logged_in_at = Carbon::now();
        $user->save();
        AnalyticService::storeLoginAction($request, $user);
        AdminUserNotificationService::notificationAdminWithUserCount(
            $user->id,
            UserCountNotificationAction::LOGIN
        );
        // If the signature is received it means that the login is being done by a user that clicked an invitation
        if ($request->has('signature')) {
            $parts = CompanyClientService::decodeInviteSignature($request->signature);
            if (isset($parts->type)) {
                $companyTypeValue = $parts->type;
            } else {
                $childCompanyId = $parts->child_company_id;
                $childCompany = Company::findOrFail($childCompanyId);
                $companyTypeValue = $childCompany->enumType->value;
            }
        } else {
            $companyTypeValue = $user->company->enumType->value;
        }
        // Invites needs to be updated with the corresponding info
        CompanyService::ActivateCompanyInvites($user, $companyTypeValue, $request->parent_id,
            $request->affiliate_brand_id);

        return $this->getUserLoggedIn($user, $token, $lastLoggedInBeforeActualLogin);
    }

    /**
     * Creates the json answer with the logged user
     *
     *
     * @throws ValidationException
     */
    private function getUserLoggedIn($user, $token, $lastLoggedInBeforeActualLogin): JsonResponse
    {
        // If a user does not have a main company id it can not log in
        if ($user->company_id === null) {
            throw ValidationException::withMessages([
                'message' => config('genericMessages.error.USER_HAS_NO_MAIN_COMPANY'),
            ]);
        }

        $userCompanies = $user->allCompanies()
            ->with([
                'avatar',
                'parentCompany',
                'companyProfileType',
                'enumType',
                'affiliateBrand',
                'clientParent',
            ])
            ->get();

        // Find the main company of the user, if there is not, we take the first one
        $company = $userCompanies->where('id', $user->company_id)->first() ?? $userCompanies->first();

        if (!$company) {
            throw ValidationException::withMessages([
                'message' => config('genericMessages.error.USER_HAS_NO_COMPANY'),
            ]);
        }

        if (empty($user->email_verified_at) && empty($user->phone_verified_at)) {
            if ($user->verification_code_verified) {
                throw ValidationException::withMessages([
                    'message' => config('genericMessages.error.VERIFICATION_NEEDED'),
                    'user' => new UserResource($user),
                ]);
            }

            throw ValidationException::withMessages([
                'message' => config('genericMessages.error.VERIFICATION_NEEDED'),
            ]);
        }

        $affiliateBrand = null;
        $isAffiliated = false;

        if ($company->enumType->value === CompanyType::FranchiseMsp
            || $company->enumType->value === CompanyType::FRANCHISE_CORPORATE_MSP) {
            $isAffiliated = true;
            if (!empty($company->affiliateBrand)) {
                $affiliateBrand = [
                    'id' => '' . $company->affiliateBrand->id,
                    'name' => $company->affiliateBrand->name,
                    'main_company_id' => '' . $company->affiliateBrand->main_company_id,
                ];
            } else {
                $domain = explode('@', $user->email)[1];
                $brandName = explode('.', $domain)[0];
                $checkBrandName = AffiliateBrand::where('name', $brandName)->first();
                if (!empty($checkBrandName)) {
                    $affiliateBrand = [
                        'id' => '' . $checkBrandName->id,
                        'name' => $checkBrandName->name,
                        'main_company_id' => '' . $checkBrandName->main_company_id,
                    ];
                }
            }
        }

        $companyClaimerResponses = CompanyClaimerResponse::query()
            ->where('user_id', $user->id)
            ->select(['id', 'company_id', 'status', 'response', 'referral', 'user_id'])
            ->get();

        $user->load('jobTitle');
        $jobTitle = $user->jobTitle ? new JobTitleResource($user->jobTitle) : null;

        $additionalInfo = new \stdClass();

        // Checking if the user's company is a ClientMSP and seting a subdomain for the user to be redirected
        $additionalInfo->plaid_integration_enabled = null;
        if ($company->enumType->value === CompanyType::MSP_CLIENT) {
            $additionalInfo->parent_subdomain = $company->clientParent->subdomain;
            // Checking plaid integration flag
            $client = CompanyClient::select('company_id', 'plaid_integration_enabled')
                ->where('client_id', $company->id)
                ->first();
            $additionalInfo->plaid_integration_enabled = (bool)$client?->plaid_integration_enabled;
        }

        // @todo rename all these claimed stuff coordinating with FE team
        $additionalInfo->claimer_responses = CompanyClaimerResponseSimpleResource::collection($companyClaimerResponses);
        $additionalInfo->user = [
            'business_phone' => $user->phone,
            'job_title' => $jobTitle,
        ];

        $additionalInfo->prm_active = $company->partner_flag;
        $additionalInfo->is_distributor = $company->is_distributor;
        $additionalInfo->manage_clients = $company->manage_clients;
        $additionalInfo->manage_affiliates = $company->manage_affiliates;
        $additionalInfo->show_distributor_banner = $company->show_distributor_banner;
        $additionalInfo->show_manage_clients_banner = $company->show_manage_clients_banner;
        $additionalInfo->show_affiliate_popup = $company->show_affiliate_popup;
        $additionalInfo->show_manage_affiliates_banner = $company->show_manage_affiliates_banner;
        $base64AdditionalInfo = base64_encode(gzdeflate(json_encode($additionalInfo), 9));

        return response()->json([
            'last_logged_in_at' => $lastLoggedInBeforeActualLogin,
            'access_token' => $token,
            'token_type' => 'bearer',
            'additional_info' => $base64AdditionalInfo,
            'affiliate_brand' => $affiliateBrand,
            'is_affiliated' => $isAffiliated,
        ]);
    }

    /**
     * Destroy an authenticated session.
     *
     * @throws ValidationException
     */
    public function destroy(): JsonResponse
    {
        $userId = AuthService::getLoggedInUserId();
        AdminUserNotificationService::notificationAdminWithUserCount(
            $userId,
            UserCountNotificationAction::LOGOUT
        );
        Auth::guard('api')->logout(); // Token remove

        return response()->json(['message' => 'Token deleted']);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/user-avatars",
     *     operationId="getUserAvatars",
     *     tags={"AuthenticatedSessionController"},
     *     summary="Returns avatars from logged in user and it's claimed companies",
     *     description="Returns avatars from logged in user and it's claimed companies",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/user-avatars
    // Bearer Token needed
    public function getUserAvatars(): JsonResponse
    {
        $user = AuthService::getAuthUser();
        $claimedCompanies = CompanyClaimer::with('company.avatar', 'company.companyProfileType', 'company.enumType')
            ->where('user_id', $user->id)->get();
        $avatar = ImageService::findAvatarInDB($user, ProfileImageType::UserAvatar);

        return response()->json([
            'avatar' => $avatar,
            'claimed_companies' => ClaimedCompaniesResource::collection($claimedCompanies),
        ]);
    }
}
