<?php

namespace App\Http\Middleware;

use App\Helpers\PermissionsHelper;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class DomainRequestMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @return mixed
     */
    public function handle(Request $request, Closure $next): Response
    {
        // we need to set the request domain to be used in the BE to know if the request is coming from CP or bettertracker
        PermissionsHelper::setRequestDomain($request);

        return $next($request);
    }
}
