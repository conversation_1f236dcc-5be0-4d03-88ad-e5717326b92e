<?php

namespace App\Http\Middleware;

use App\Enums\UserStatus;
use App\Helpers\PermissionsHelper;
use App\Models\Permission\Permission;
use App\Services\AuthService;
use App\Services\Permission\PermissionService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Validation\ValidationException;
use PHPOpenSourceSaver\JWTAuth\Facades\JWTAuth;
use Symfony\Component\HttpFoundation\Response;

class ValidateRolesPermissionsMiddleware
{
    protected array $exceptionRoutesForAsCompanyId = [
        'POST:api/v1/cyclr/save-result',
        'POST:api/v1/two-factor-challenge',
        'POST:api/v1/login',
        'POST:api/v1/plaid/webhook',
    ];

    protected array $exceptionRoutes = [
        'POST:api/v1/logout',
    ];

    /**
     * Handle an incoming request.
     *
     * @return mixed
     *
     * @throws ValidationException
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = AuthService::getAuthUser();
        if ($user !== null) {
            if ($user->getAttributeValue('status') === UserStatus::Inactive) {
                JWTAuth::invalidate(JWTAuth::getToken());
                abort(403, config('genericMessages.error.USER_IS_INACTIVE'));
            }
            $routePermission = PermissionsHelper::makePermissionFromRoute();
            // ignore the validations if request is for the exception routes
            if (in_array($routePermission->title, $this->exceptionRoutes, true)) {
                return $next($request);
            }
            $userIsSuperAdmin = AuthService::userIsSuperAdmin($user);
            $this->processRouteExceptionsForAsCompanyId($request, $routePermission);
            $companyId = PermissionsHelper::getCompanyId($request);
            App::instance('asCompanyId', $companyId);
            if (!$userIsSuperAdmin) {
                $permission = PermissionService::getPermissionByTitle($routePermission->title);
                // Only if the permission exists it must be validated
                if (!empty($permission)) {
                    $userIsAdmin = AuthService::userIsAdmin($user);
                    // If the user is an admin we will validate permissions not only for the received as_company_id
                    // but also for channel program
                    $companyIds = [$companyId];
                    if ($userIsAdmin) {
                        $companyIds[] = config('custom.channel_program_company.id');
                    }
                    AuthService::validatePermission($permission->id, $user->id, $companyIds);
                }
            }
        }

        return $next($request);
    }

    /**
     * This method adds the "as_company_id" to the request object only if the route from the request
     * is on the list of routes.
     */
    private function processRouteExceptionsForAsCompanyId(Request $request, Permission $permission): void
    {
        // Here you need to add all the routes that will not need the as_company_id
        if (in_array($permission->title, $this->exceptionRoutesForAsCompanyId, true)) {
            $request->merge(['as_company_id' => config('custom.channel_program_company.id')]);
        }
    }
}
