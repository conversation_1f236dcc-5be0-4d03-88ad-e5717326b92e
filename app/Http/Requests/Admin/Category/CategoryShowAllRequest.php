<?php

namespace App\Http\Requests\Admin\Category;

use App\Http\Requests\Admin\AdminSearchRequest;
use App\Rules\AllowedValues;
use App\Services\CategoryService;

class CategoryShowAllRequest extends AdminSearchRequest
{
    public function __construct()
    {
        $this->validOrderByColumnNames = ['name', 'created_at', 'is_hidden'];
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $allowedValues = array_merge(
            CategoryService::getCategoriesCompanyTypes()->pluck('value')->toArray(),
            ['DEFAULT']
        );

        return array_merge(parent::rules(), [
            'only_parents' => 'sometimes|required|boolean',
            'is_hidden' => 'sometimes|required|boolean',
            'company_type' => ['sometimes', 'required', new AllowedValues($allowedValues)],
            'parent_id' => 'sometimes|required|numeric|exists:categories,id',
            'created_at' => 'sometimes|required|array',
        ]);
    }
}
