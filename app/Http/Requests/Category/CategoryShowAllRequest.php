<?php

namespace App\Http\Requests\Category;

use Illuminate\Foundation\Http\FormRequest;

class CategoryShowAllRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'parent_id' => 'sometimes|required|numeric|exists:categories,id',
            'load_my_stack_categories' => 'sometimes|required|boolean',
            'section' => 'sometimes|required|string',
            'default_categories' => 'sometimes|required|boolean',
            'is_hidden' => 'sometimes|required|boolean',
            'company_type' => 'sometimes|required|numeric|exists:company_types,id',
            'company_type_categories' => 'sometimes|string|in:include,exclude,restrict',
        ];
    }
}
