<?php

namespace App\Http\Requests\Company\Invite;

use App\Enums\Company\CompanyType;
use App\Http\Requests\Admin\AdminSearchRequest;
use App\Rules\AllowedValues;
use Illuminate\Validation\Rule;

class CompanyShowAllAffiliateInviteRequest extends AdminSearchRequest
{
    public function __construct()
    {
        $this->validOrderByColumnNames = ['email', 'created_at', 'roles.display_name', 'last_sent_at', 'updated_at'];
    }

    public function rules(): array
    {
        $allowedValues = [CompanyType::FranchiseMsp, CompanyType::MSP_LOCATION];

        return array_merge(parent::rules(), [
            'status' => 'sometimes|required|boolean',
            'company_type' => ['sometimes', 'required', new AllowedValues($allowedValues)],
            'start_date' => ['nullable', 'date'],
            'end_date' => ['nullable', 'date'],
            'order_by' => ['sometimes', 'required', 'string', Rule::in(['email', 'last_sent_at', 'created_at', 'updated_at', 'roles.display_name'])],
        ]);
    }
}
