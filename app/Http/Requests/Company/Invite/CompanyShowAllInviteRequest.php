<?php

namespace App\Http\Requests\Company\Invite;

use App\Http\Requests\Admin\AdminSearchRequest;

class CompanyShowAllInviteRequest extends AdminSearchRequest
{
    public function __construct()
    {
        $this->validOrderByColumnNames = ['email', 'created_at', 'roles.display_name', 'updated_at'];
    }

    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'activated' => 'sometimes|required|boolean',
        ]);
    }
}
