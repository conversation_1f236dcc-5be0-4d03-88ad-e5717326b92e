<?php

namespace App\Http\Requests\CompanyDashboard;

use Illuminate\Foundation\Http\FormRequest;

class CategoryDetailCountRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'category' => 'sometimes|array',
            'category.*' => 'exists:categories,id',
            'sub_category' => 'sometimes|array',
            'sub_category.*' => 'exists:categories,id',
            'start_date' => 'sometimes|required|date',
            'end_date' => 'sometimes|required|date|after_or_equal:start_date',
        ];
    }
}
