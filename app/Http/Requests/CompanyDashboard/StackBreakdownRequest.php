<?php

namespace App\Http\Requests\CompanyDashboard;

use Illuminate\Foundation\Http\FormRequest;

class StackBreakdownRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'start_date' => 'required|date|before:end_date',
            'end_date' => 'required|date|after:start_date',
            'order_by' => 'sometimes|required|string|in:name,product_count,customer_count,contract_cost,amount',
            'sort' => 'sometimes|required|string|in:ASC,DESC',
        ];
    }
}
