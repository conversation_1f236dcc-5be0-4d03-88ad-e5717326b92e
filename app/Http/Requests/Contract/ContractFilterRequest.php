<?php

namespace App\Http\Requests\Contract;

use App\Http\Requests\Admin\AdminSearchRequest;

class ContractFilterRequest extends AdminSearchRequest
{
    public function __construct()
    {
        $this->validOrderByColumnNames = ['name', 'initial_payment_date', 'cost_after_discount', 'billing_type'];
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|after:start_date|date',
            'categories' => ['sometimes', 'array', 'min:1'],
            'categories.*' => ['nullable', 'numeric'],
            'sub_categories' => ['sometimes', 'array', 'min:1'],
            'sub_categories.*' => ['nullable', 'numeric'],
            'expiration_dates' => ['sometimes', 'array', 'min:1'],
            'expiration_dates.*' => ['nullable', 'date'],
            'types' => ['sometimes', 'array', 'min:1'],
            'types.*' => ['required', 'numeric', 'exists:contract_types,id'],
            'billing_types' => ['sometimes', 'array', 'min:1'],
            'billing_types.*' => ['required', 'numeric', 'exists:contract_billing_types,id'],
            'vendors' => ['sometimes', 'array', 'min:1'],
            'vendors.*' => ['required', 'numeric', 'exists:companies,id'],
        ]);
    }
}
