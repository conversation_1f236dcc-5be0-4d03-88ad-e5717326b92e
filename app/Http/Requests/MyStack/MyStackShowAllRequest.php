<?php

namespace App\Http\Requests\MyStack;

use App\Enums\Partner\PartnerPortalInvitationStatus;
use App\Enums\SearchModelParams;
use App\Http\Requests\Admin\AdminSearchRequest;
use App\Rules\AllowedValues;

class MyStackShowAllRequest extends AdminSearchRequest
{
    public function __construct()
    {
        $this->validFilterColumns = $this->getSearchParams(SearchModelParams::MyStackCategory);
        $this->validOrderByColumnNames = [
            'parent_categories.name',
            'sub_categories.name',
            'products.name',
            'companies.name',
            'my_stack.created_at',
            'partner_status',
        ];

        $this->columnNamesAliases = [
            'partner_status' => 'my_stack.partner_status',
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                'rating' => 'sometimes|required|numeric',
                'partner_status' => ['sometimes', 'required', new AllowedValues(PartnerPortalInvitationStatus::getKeys())],
                'is_recommended_stack' => 'sometimes|required|boolean',
                'category' => 'sometimes|required|array',
                'category.*' => 'sometimes|required|numeric|exists:categories,id',
                'sub_category' => 'sometimes|required|array',
                'sub_category.*' => 'sometimes|required|numeric|exists:categories,id',
            ]
        );
    }
}
