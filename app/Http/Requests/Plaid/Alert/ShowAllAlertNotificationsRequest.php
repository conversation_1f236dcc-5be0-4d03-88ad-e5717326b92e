<?php

namespace App\Http\Requests\Plaid\Alert;

use App\Http\Requests\Admin\AdminSearchRequest;

class ShowAllAlertNotificationsRequest extends AdminSearchRequest
{
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'alert_type' => 'sometimes|array',
            'alert_type.*' => 'required|nullable|string',
            'period' => 'sometimes|array',
            'period.*' => 'required|nullable|string',
            'vendor' => 'sometimes|array',
            'vendor.*' => 'required|nullable|string',
            'date' => 'sometimes|array',
            'date.start_date' => 'nullable|required_with:date.end_date|date|before:date.end_date',
            'date.end_date' => 'nullable|required_with:date.start_date|date|after:date.start_date',
        ]);
    }
}
