<?php

namespace App\Http\Requests\Plaid;

use Illuminate\Foundation\Http\FormRequest;

class ExpensesOverviewBreakdownRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'start_date' => 'required|date|before:end_date',
            'end_date' => 'required|date|after:start_date',
            'filter' => 'sometimes|required|string|in:all,stack,subscription',
            'order_by' => 'sometimes|required|string|in:name,amount,percentage,change_percentage',
            'sort' => 'sometimes|required|string|in:ASC,DESC',
            'hide_uncategorized' => 'sometimes|required|boolean',
            'stack_only' => 'sometimes|required|boolean',
        ];
    }
}
