<?php

namespace App\Http\Requests\Plaid;

use Illuminate\Foundation\Http\FormRequest;

class ExpensesSummaryRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'start_date' => 'required|date|before:end_date',
            'end_date' => 'required|date|after:start_date',
            'category' => 'sometimes|array',
            'category.*' => 'exists:categories,id',
            'sub_category' => 'sometimes|array',
            'sub_category.*' => 'exists:categories,id',
        ];
    }
}
