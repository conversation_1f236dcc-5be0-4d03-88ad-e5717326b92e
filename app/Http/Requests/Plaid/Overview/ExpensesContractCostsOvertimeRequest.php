<?php

namespace App\Http\Requests\Plaid\Overview;

use Illuminate\Foundation\Http\FormRequest;

class ExpensesContractCostsOvertimeRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'id' => 'sometimes|exists:contracts,id',
            'year' => 'nullable|required_without:start_date,end_date|date_format:Y',
            'start_date' => 'nullable|required_with:end_date|date|before:end_date',
            'end_date' => 'nullable|required_with:start_date|date|after:start_date',
            'category' => 'sometimes|array',
            'category.*' => 'exists:categories,id',
            'sub_category' => 'sometimes|array',
            'sub_category.*' => 'exists:categories,id',
        ];
    }
}
