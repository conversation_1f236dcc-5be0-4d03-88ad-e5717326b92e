<?php

namespace App\Http\Requests\Register;

use App\Models\Company\CompanyType;
use App\Rules\AllowedValues;
use App\Rules\Profanity;
use App\Rules\ValidEmail;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Str;
use Illuminate\Validation\Validator;

class RegisterStepOneRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'email' => [
                'required',
                new ValidEmail(),
            ],
            'password' => [
                'required',
                'string',
                'confirmed',
                'min:8',
                "regex:/((?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[\W_]).{8,})/",
            ],
            'password_confirmation' => 'required',
            'first_name' => 'required|string|max:100',
            'last_name' => 'required|string|max:100',
            'first_send' => 'required|boolean',
            'create_request' => 'sometimes|boolean',
            'company_type' => ['required', new AllowedValues(CompanyType::all()->pluck('value')->toArray())],
            'company_name' => ['required', new Profanity()],
            'job_title_id' => 'required|numeric|exists:job_titles,id',
            'country' => 'sometimes|required|string',
            'state' => 'sometimes|required|string',
            'city' => 'sometimes|required|string',
            'company_id' => 'sometimes|required|numeric|exists:companies,id',
            'parent_id' => 'sometimes|required|numeric|exists:companies,id',
        ];
    }

    /**
     * Transforms email with the lowercase value
     */
    protected function getValidatorInstance(): Validator
    {
        $data = $this->all();
        $data['email'] = isset($data['email']) ? Str::lower($data['email']) : null;
        $this->getInputSource()->replace($data);

        return parent::getValidatorInstance();
    }
}
