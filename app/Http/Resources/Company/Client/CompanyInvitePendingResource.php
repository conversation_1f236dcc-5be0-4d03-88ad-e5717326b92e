<?php

namespace App\Http\Resources\Company\Client;

use App\Http\Resources\Company\CompanySimpleResource;
use App\Http\Resources\Permission\Role\RoleSimpleResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CompanyInvitePendingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => '' . $this->id,
            'child_company_id' => '' . $this->child_company_id,
            'child_company' => new CompanySimpleResource($this->whenLoaded('childCompany')),
            'email' => $this->email,
            'type' => $this->type,
            'role_id' => '' . $this->role_id,
            'role' => new RoleSimpleResource($this->whenLoaded('role')),
            'last_sent_at' => $this->last_sent_at,
            'created_at' => $this->created_at,
        ];
    }
}
