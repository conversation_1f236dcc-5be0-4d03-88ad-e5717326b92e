<?php

namespace App\Http\Resources\Company\Client;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CompanyInviteResendResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'message' => config('genericMessages.success.INVITE_WERE_SENT'),
            'resent_count' => $this['resent_count'],
            'errors' => $this['errors'],
        ];
    }
}
