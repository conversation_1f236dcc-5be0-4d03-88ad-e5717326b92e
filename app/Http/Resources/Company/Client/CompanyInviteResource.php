<?php

namespace App\Http\Resources\Company\Client;

use App\Http\Resources\AuthorResource;
use App\Http\Resources\Company\CompanySimpleResource;
use App\Http\Resources\Permission\Role\RoleSimpleResource;
use App\Http\Resources\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CompanyInviteResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => '' . $this->id,
            'parent_company_id' => '' . $this->parent_company_id,
            'parent_company' => new CompanySimpleResource($this->whenLoaded('parentCompany')),
            'child_company_id' => '' . $this->child_company_id,
            'child_company' => new CompanySimpleResource($this->whenLoaded('childCompany')),
            'email' => $this->email,
            'invited_user' => new UserResource($this->whenLoaded('invitedUser')),
            'activated' => $this->activated,
            'activated_at' => $this->activated_at,
            'type' => $this->type,
            'author_id' => $this->author_id ? '' . $this->author_id : null,
            'affiliate_brand_id' => $this->affiliate_brand_id ? '' . $this->affiliate_brand_id : null,
            'author' => new AuthorResource($this->whenLoaded('author')),
            'user_invite' => $this->user_invite,
            'role_id' => '' . $this->role_id,
            'role' => new RoleSimpleResource($this->whenLoaded('role')),
            'last_sent_at' => $this->last_sent_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
