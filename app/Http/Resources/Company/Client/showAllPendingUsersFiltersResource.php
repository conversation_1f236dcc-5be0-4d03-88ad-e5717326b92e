<?php

namespace App\Http\Resources\Company\Client;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class showAllPendingUsersFiltersResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'filters' => [
                'type' => [
                    'placeholder' => 'Type',
                    'multiple' => true,
                    'is_public' => true,
                    'items' => $this['typeItems'] ?? [],
                ],
                'sent_at' => [
                    'type' => 'date_range',
                    'multiple' => false,
                    'placeholder' => 'Date Sent',
                    'is_public' => true,
                    'items' => [],
                ],
                'roles' => [
                    'placeholder' => 'Role',
                    'multiple' => true,
                    'is_public' => true,
                    'items' => $this['roleItems'] ?? [],
                ],
            ],
        ];
    }
}
