<?php

namespace App\Http\Resources\Company\Dashboard;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class StackBreakdownCategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => '' . $this->id,
            'name' => $this->name,
            'color' => $this->color ?? '#000000',
            'product_count' => $this->product_count,
            'percentage' => '' . round(($this->product_count / ($this->totalProductCount ?: 1)) * 100, 2),
            'sub_categories' => StackBreakdownSubCategoryResource::collection($this->sub_categories),
        ];
    }
}
