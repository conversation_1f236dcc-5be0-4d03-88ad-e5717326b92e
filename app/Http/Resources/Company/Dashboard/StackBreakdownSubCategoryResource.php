<?php

namespace App\Http\Resources\Company\Dashboard;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class StackBreakdownSubCategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => '' . $this->id,
            'name' => $this->name,
            'color' => $this->color ?? '#000000',
            'product_count' => $this->product_count,
        ];
    }
}
