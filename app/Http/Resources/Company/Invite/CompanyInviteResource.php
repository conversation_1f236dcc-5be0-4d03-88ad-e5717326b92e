<?php

namespace App\Http\Resources\Company\Invite;

use App\Http\Resources\AuthorResource;
use App\Http\Resources\Company\CompanySimpleResource;
use App\Http\Resources\Permission\Role\RoleResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CompanyInviteResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => '' . $this->id,
            'email' => $this->email,
            'activated' => $this->activated,
            'activated_at' => $this->activated_at,
            'type' => $this->type,
            'author_id' => '' . $this->author_id,
            'author' => new AuthorResource($this->whenLoaded('author')),
            'parent_company_id' => '' . $this->author_id,
            'parent_company' => new CompanySimpleResource($this->whenLoaded('parentCompany')),
            'child_company_id' => '' . $this->author_id,
            'child_company' => new CompanySimpleResource($this->whenLoaded('childCompany')),
            'role_id' => '' . $this->role_id,
            'role' => new RoleResource($this->whenLoaded('role')),
            'last_sent_at' => $this->last_sent_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
