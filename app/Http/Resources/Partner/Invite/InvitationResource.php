<?php

namespace App\Http\Resources\Partner\Invite;

use App\Http\Resources\AuthorResource;
use App\Http\Resources\Company\CompanySimpleResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class InvitationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray(Request $request): array
    {
        return
            [
                'id' => '' . $this->id,
                'followed_partner_id' => '' . $this->followed_partner_id,
                'follower_partner_id' => $this->follower_partner_id ? '' . $this->follower_partner_id : null,
                'follower_partner' => new CompanySimpleResource($this->whenLoaded('followerPartner')),
                'partner' => new CompanySimpleResource($this->whenLoaded('partner')),
                'status' => $this->status,
                'initiator' => $this->initiator,
                'invited_by' => new AuthorResource($this->whenLoaded('invitedBy')),
                'invited_at' => $this->invited_at,
                'accepted_by' => new AuthorResource($this->whenLoaded('acceptedBy')),
                'accepted_at' => $this->accepted_at,
                'rejected_by' => new AuthorResource($this->whenLoaded('rejectedBy')),
                'rejected_at' => $this->rejected_at,
                'rejected_reason' => $this->rejected_reason,
                'rejected_reason_other' => $this->rejected_reason_other,
                'accepted_reason' => $this->accepted_reason,
                'accepted_reason_other' => $this->accepted_reason_other,
                'email' => $this->email,
                'source' => $this->source,
                'last_sent_invite_at' => $this->last_sent_invite_at,
                'accepted_reason_name' => $this->accepted_reason_name ?? null,
                'is_in_contact_list' => $this->whenNotNull($this->is_in_contact_list),
            ];
    }
}
