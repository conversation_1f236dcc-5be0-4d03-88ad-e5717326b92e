<?php

namespace App\Http\Resources\Product;

use App\Http\Resources\Company\CompanySimpleResource;
use App\Http\Resources\StatusScope\StatusScopeResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductSimpleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        $rating = $this->rating ?? null;
        $totalReviews = $this->total_reviews ?? null;

        $status = $this->whenLoaded("statusRelation");
        $statusResource = !empty($status) ? new StatusScopeResource($status) : null;

        return [
            'id' => '' . $this->id,
            'company_id' => '' . $this->company_id,
            'company' => new CompanySimpleResource($this->whenLoaded('company')),
            'name' => $this->name,
            'url' => $this->url ?? null,
            'friendly_url' => $this->friendly_url,
            'categories' => !empty($this->categories) ? collect($this->categories)->map(function ($category) {
                if (empty($category?->parent_id) && !empty($category?->id)) {
                    return [
                        'id' => null,
                        'parent_id' => '' . $category->id,
                        'parent' => [
                            'id' => '' . $category->id,
                            'parent_id' => null,
                            'parent' => null,
                            'name' => $category->name,
                            'color' => $category->color,
                            'friendly_url' => $category->friendly_url,
                        ],
                        'name' => null,
                        'color' => null,
                        'friendly_url' => null,
                    ];
                }

                return new ProductCategoryResource($category);
            }) : [],
            'rating' => $rating,
            'total_reviews' => $totalReviews,
            'description' => $this->description,
            'status_scope' => $this->whenNotNull($statusResource),
            'created_at' => $this->whenNotNull($this->created_at),
        ];
    }
}
