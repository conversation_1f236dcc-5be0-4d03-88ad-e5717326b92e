<?php

namespace App\Jobs;

use App\Models\User;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;

class FinishUserConfirmation implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        public User $user,
        public bool $isMSP = false,
        public bool $useBetterTrackerConfig = false
    ) {}

    /**
     * Execute the job.
     *
     *
     * @throws Exception
     */
    public function handle(): void
    {
        $this->isMSP
            ? ($this->useBetterTrackerConfig
                ? $this->user->sendDIrectEmailSuccessfullyVerifiedNotification()
                : $this->user->sendMSPEmailSuccessfullyVerifiedNotification()
            )
            : $this->user->sendEmailSuccessfullyVerifiedNotification();
        $this->user->companiesIFollow()->syncWithoutDetaching([config('custom.channel_program_company.id')]);
        // Make the user attend to all the future pitch events
        if ($this->user->registered_all_pitches === true) {
            SyncFutureEventsRegistration::dispatch($this->user);
        }
    }

    public function middleware(): array
    {
        return [new WithoutOverlapping($this->user->id)];
    }
}
