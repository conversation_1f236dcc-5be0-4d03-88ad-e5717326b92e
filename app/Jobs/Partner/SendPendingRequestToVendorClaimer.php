<?php

namespace App\Jobs\Partner;

use App\Enums\GeneralRedirectUrl;
use App\Enums\Partner\PartnerPortalInvitationStatus;
use App\Enums\UserStatus;
use App\Models\MSPFollowingPartner;
use App\Notifications\Partner\VendorPendingRequestNotification;
use App\Services\AppConfig;
use App\Services\FeatureFlagService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SendPendingRequestToVendorClaimer implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::debug('SendPendingRequestToVendorClaimer: Job started.');
        $featureFlag = FeatureFlagService::findByName('SEND_PENDING_PRM_REQUEST_VENDOR_CLAIMER');
        if ($featureFlag->activated) {
            $vendorsPendingRequests = $this->getVendorsPendingRequests();
            Log::debug('SendPendingRequestToVendorClaimer: Sending email to ' . $vendorsPendingRequests->count() . ' companies.');

            foreach ($vendorsPendingRequests as $vendorRequests) {
                $vendor = $vendorRequests->first()->partner;
                Log::debug('SendPendingRequestToVendorClaimer: Sending email to ' . $vendor->name . '(' . $vendor->id . ') company with ' . $vendorRequests->count() . ' pending requests.');
                Log::debug('SendPendingRequestToVendorClaimer: Total Claimers: ' . $vendor->claimers->count());
                $vendor->claimers->each(function ($claimer) use ($vendorRequests) {
                    Log::debug('SendPendingRequestToVendorClaimer: Sending email to ' . $claimer->email . '(' . $claimer->id . ') claimer.');
                    if ($vendorRequests->count() > 15) {
                        $vendorRequests = $vendorRequests->take(15)->sortBy('invited_at', 'desc');
                    }
                    $claimer->notify(
                        new VendorPendingRequestNotification(
                            $vendorRequests,
                            GeneralRedirectUrl::channelCommand
                        )
                    );
                });
            }

            Log::debug('SendPendingRequestToVendorClaimer: Job finished.');
        }
    }

    private function getVendorsPendingRequests()
    {
        $pendingRequests = MSPFollowingPartner::with(
            'partner:id,name,subdomain,friendly_url,partner_flag',
            'partner.claimers:id,status,first_name,last_name,email,company_id',
            'followerPartner:id,name,subdomain,friendly_url',
            'invitedBy:id,first_name,last_name,email,company_id,status',
        )
            ->whereHas('partner', function ($query) {
                $query->where('partner_flag', true)->whereHas('claimers');
            })
            ->whereHas('invitedBy', function ($query) {
                $query->where('status', UserStatus::Active);
            })
            ->whereBetween('invited_at', [
                today()->subDays(
                    AppConfig::loadAppConfigByKey('PRM_REQUEST_CLAIMER_REMINDER_FREQUENCY_DAYS', 21)->value
                ),
                today()->subDays(7)->endOfDay(),
            ])
            ->where('status', PartnerPortalInvitationStatus::Requested)
            ->whereNull('deleted_at')
            ->orderBy('invited_at', 'desc')
            ->get();

        return $pendingRequests->groupBy('followed_partner_id');
    }

    public function middleware(): array
    {
        return [new WithoutOverlapping('' . now()->valueOf())];
    }
}
