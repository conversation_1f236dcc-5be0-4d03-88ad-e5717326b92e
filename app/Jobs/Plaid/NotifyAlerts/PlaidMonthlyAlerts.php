<?php

namespace App\Jobs\Plaid\NotifyAlerts;

use App\Enums\Lookup\Values\Plaid\PlaidNotifyAlertPeriodValueEnum;
use App\Jobs\Plaid\PlaidCalculateExpenses;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class PlaidMonthlyAlerts implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $source = __CLASS__ . '::' . __FUNCTION__;
        Log::debug($source . '::JOB STARTED');

        try {
            PlaidCalculateExpenses::dispatch(PlaidNotifyAlertPeriodValueEnum::MONTHLY);
        } catch (\Exception $ex) {
            Log::error($source . '::' . $ex->getMessage() . ' >>>> ' . json_encode($ex->getTrace(), JSON_PRETTY_PRINT));
        }
        Log::debug($source . '::JOB ENDED');
    }

    public function middleware(): array
    {
        return [new WithoutOverlapping('' . now())];
    }
}
