<?php

namespace App\Jobs\Plaid;

use App\Enums\Company\CompanyType;
use App\Enums\FeatureFlagEnum;
use App\Enums\Lookup\LookupOptionsEnum;
use App\Enums\Lookup\Values\Plaid\PlaidNotifyAlertPeriodValueEnum;
use App\Enums\Lookup\Values\Plaid\PlaidNotifyAlertTypesValueEnum;
use App\Models\Plaid\Alert\PlaidCompanyNotification;
use App\Models\Plaid\Alert\PlaidNotifyAlert;
use App\Notifications\CompanyExpenses\CompanyExpensesDecreaseOverTimeframeNotification;
use App\Notifications\CompanyExpenses\CompanyExpensesIncreaseOverTimeframeNotification;
use App\Notifications\CompanyExpenses\CompanyExpensesUpcomingRecurringChargesNotification;
use App\Services\FeatureFlagService;
use App\Services\LookupService\LookupService;
use App\Services\Plaid\PlaidExpensesService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Str;
use stdClass;

class PlaidCalculateExpenses implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private Carbon $previousStart;
    private Carbon $previousEnd;
    private Carbon $currentStart;
    private Carbon $currentEnd;

    public function __construct(protected $periodKey, protected $companyAlertSettings = null) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $source = __CLASS__ . '::' . __FUNCTION__;
        Log::debug($source . '::JOB STARTED');

        try {
            $this->setDateRange();
            if ($this->companyAlertSettings) {
                Log::debug($source . '::Single company calculation: COMPANY ID: ' . $this->companyAlertSettings[0]->company->id);

                foreach ($this->companyAlertSettings as $notifyAlertSetting) {
                    if ($notifyAlertSetting->active) {
                        $this->calculateAlertExpenses($notifyAlertSetting);
                    }
                }
            } else {
                Log::debug($source . '::All company calculation');

                $periodId = LookupService::getLookupOptionValueId(LookupOptionsEnum::PLAID_NOTITY_ALERT_PERIODS,
                    $this->periodKey);
                $notifyAlertSettings = PlaidNotifyAlert::select('company_id', 'type_id', 'period_id')
                    ->where('period_id', $periodId)
                    ->where('active', true)
                    ->with(['type:id,value_key', 'company'])
                    ->get();

                foreach ($notifyAlertSettings as $notifyAlertSetting) {
                    $this->calculateAlertExpenses($notifyAlertSetting);
                }
            }
        } catch (\Exception $ex) {
            Log::error($source . '::' . $ex->getMessage() . ' >>>> ' . json_encode($ex->getTrace(), JSON_PRETTY_PRINT));
        }
        Log::debug($source . '::JOB ENDED');
    }

    private function setDateRange(): void
    {
        $previousDate = match ($this->periodKey) {
            PlaidNotifyAlertPeriodValueEnum::DAILY => Carbon::now()->subDay(),
            PlaidNotifyAlertPeriodValueEnum::MONTHLY => Carbon::now()->subDay()->subMonth(),
            PlaidNotifyAlertPeriodValueEnum::QUARTERLY => Carbon::now()->subDay()->subQuarter(),
            default => Carbon::now()->subWeek(),
        };

        if ($this->periodKey === PlaidNotifyAlertPeriodValueEnum::DAILY) {
            $this->previousStart = $previousDate->copy()->startOfDay();
            $this->previousEnd = $previousDate->copy()->endOfDay();
            $this->currentStart = Carbon::now()->copy()->startOfDay();
            $this->currentEnd = Carbon::now()->copy()->endOfDay();
        } else if ($this->periodKey === PlaidNotifyAlertPeriodValueEnum::MONTHLY) {
            $this->previousStart = $previousDate->copy()->startOfMonth();
            $this->previousEnd = $previousDate->copy()->endOfMonth();
            // We have to use subDay because the cron job run the first day of the month
            $this->currentStart = Carbon::now()->copy()->subDay()->startOfMonth();
            $this->currentEnd = Carbon::now()->copy()->subDay()->endOfMonth();
        } elseif ($this->periodKey === PlaidNotifyAlertPeriodValueEnum::QUARTERLY) {
            $this->previousStart = $previousDate->copy()->startOfQuarter();
            $this->previousEnd = $previousDate->copy()->endOfQuarter();
            // We have to use subDay because the cron job run the first day of the month
            $this->currentStart = Carbon::now()->copy()->subDay()->startOfQuarter();
            $this->currentEnd = Carbon::now()->copy()->subDay()->endOfQuarter();
        } else {
            $this->previousStart = $previousDate->copy()->startOfWeek();
            $this->previousEnd = $previousDate->copy()->endOfWeek();
            $this->currentStart = Carbon::now()->copy()->startOfWeek();
            $this->currentEnd = Carbon::now()->copy()->endOfWeek();
        }
    }

    private function calculateAlertExpenses(PlaidNotifyAlert $notifyAlertSetting): void
    {
        $source = __CLASS__ . '::' . __FUNCTION__;

        $alertTypeKey = $notifyAlertSetting->type->value_key;

        $company = $notifyAlertSetting->company;

        Log::debug($source . '::Company ID: ' . $company->id . ' Alert Type: ' . $alertTypeKey . ' Period Key: ' . $this->periodKey . ' Period Previous Date: ' . $this->previousStart . '---' . $this->previousEnd . ' Period Current Date: ' . $this->currentStart . ' ==== ' . $this->currentEnd);

        $periodName = match ($this->periodKey) {
            PlaidNotifyAlertPeriodValueEnum::DAILY => 'Day',
            PlaidNotifyAlertPeriodValueEnum::MONTHLY => 'Month',
            PlaidNotifyAlertPeriodValueEnum::QUARTERLY => 'Quarter',
            default => 'Week',
        };

        $allExpensesAlert = $this->addPreviousCurrentAmount(PlaidExpensesService::getAllExpenses($company))->first();

        $allExpensesOfThisperiod = PlaidExpensesService::getAllExpenses($company,
            [$this->currentStart, $this->currentEnd])
            ->select('plaid_transactions.*', 'categories.name as plaid_category_name')
            ->join('categories', 'categories.id', 'plaid_transactions.plaid_category_id')
            ->get();

        $getLargestExpenses = function () use ($allExpensesOfThisperiod) {
            $largestExpenses = collect();

            foreach ($allExpensesOfThisperiod as $expenseOfThisPeriod) {
                $expense = new StdClass();
                $expense->showFirstColumn = true;
                $expense->companyVendorIco = true;

                $expense->firstRow = Carbon::parse($expenseOfThisPeriod->date)->format('F d, Y');
                $expense->secondRow = $expenseOfThisPeriod->merchant_name;
                $expense->expenses = round($expenseOfThisPeriod->amount, 2);

                $largestExpenses[] = $expense;
            }

            return $largestExpenses;
        };

        Log::debug($source . '::Company ID: ' . $company->id . ' Previous Amount: ' . $allExpensesAlert->previous_total . ' Current Amount: ' . $allExpensesAlert->current_total);

        $allExpensesAlert->percentage_diff = PlaidExpensesService::getPercentageDiff($allExpensesAlert->previous_total,
            $allExpensesAlert->current_total);

        $allSubscriptionsExpenses = $this->addPreviousCurrentAmount(PlaidExpensesService::getSubscriptions($company))->get();
        $allSubscriptionsExpenses->transform(function ($model) {
            $model->percentage_diff = PlaidExpensesService::getPercentageDiff($model->previous_total,
                $model->current_total);

            return $model;
        });

        $dates = [
            'previous_date_range' => [
                'start' => $this->previousStart->format('m-d-Y'),
                'end' => $this->previousEnd->format('m-d-Y'),
            ],
            'current_date_range' => [
                'start' => $this->currentStart->format('m-d-Y'),
                'end' => $this->currentEnd->format('m-d-Y'),
            ],
        ];

        $getAllExpenseExtraData = function ($expenseResult, $extraData = []) {
            return array_merge([
                'previous_total' => $expenseResult->previous_total,
                'current_total' => $expenseResult->current_total,
                'percentage_diff' => $expenseResult->percentage_diff,
                'previous_date_range' => [
                    'start' => $this->previousStart->toISOString(),
                    'end' => $this->previousEnd->toISOString(),
                ],
                'current_date_range' => [
                    'start' => $this->currentStart->toISOString(),
                    'end' => $this->currentEnd->toISOString(),
                ],
            ], $extraData);
        };

        $storeSubscriptionExpense = function ($subscriptionExpense, $extraData = []) use ($notifyAlertSetting) {
            $this->storeCompanyNotification(
                $notifyAlertSetting,
                $subscriptionExpense->id,
                array_merge(['last_billed_date' => $subscriptionExpense->last_date], $extraData)
            );
        };

        $getSubscriptionTransactions = function ($subscription) {
            return $subscription->transactions()
                ->select('plaid_transactions.date', 'plaid_transactions.amount')
                ->orderByDesc('plaid_transactions.date')
                ->get();
        };

        // Apply filter based on the required status
        if ($alertTypeKey === PlaidNotifyAlertTypesValueEnum::INCREASES_IN_MY_EXPENSES) {
            $sendIncreaseExpenseEmail = false;
            $increaseEmailNotifications = new StdClass();
            $largestExpenses = $getLargestExpenses();

            if ($allExpensesAlert->percentage_diff->increased) {
                Log::debug($source . ':: INCREASES IN MY EXPENSES SUMMARY');

                $sendIncreaseExpenseEmail = true;
                $this->storeCompanyNotification($notifyAlertSetting,
                    extraData: $getAllExpenseExtraData($allExpensesAlert));

                $increaseEmailNotifications->showFirstColumn = true;
                $increaseEmailNotifications->firstRow = 'Total Expenses';
                $increaseEmailNotifications->secondRow = 'Current Period';
                $increaseEmailNotifications->expenses = round($allExpensesAlert->current_total ?? 0, 2);
                $increaseEmailNotifications->oldExpenses = round($allExpensesAlert->previous_total ?? 0, 3);
                $increaseEmailNotifications->alertType = 'increase';
                $increaseEmailNotifications->expensesVariation = $allExpensesAlert->percentage_diff->change_percentage;
            }

            foreach ($allSubscriptionsExpenses as $subscriptionsExpense) {
                $transactions = $getSubscriptionTransactions($subscriptionsExpense);

                $currentAmount = $transactions[0]->amount ?? 0;
                $previousAmount = $transactions[1]->amount ?? 0;
                $pecentageDiff = PlaidExpensesService::getPercentageDiff($previousAmount, $currentAmount);

                if ($transactions->count() > 1 && $currentAmount > $previousAmount) {
                    $storeSubscriptionExpense($subscriptionsExpense, [
                        'previous_total' => $previousAmount,
                        'current_total' => $currentAmount,
                        'percentage_diff' => $pecentageDiff,
                    ]);
                }
            }

            if ($sendIncreaseExpenseEmail) {
                Log::debug($source . ':: SEND INCREASE EXPENSE EMAIL');

                $higherCategoryExpense = $allExpensesOfThisperiod->groupBy('plaid_category_name')
                    ->map(fn ($items) => $items->sum('amount'))
                    ->sortDesc()
                    ->take(1)
                    ->map(fn ($sum, $category) => ['category' => $category, 'total' => $sum])
                    ->values()
                    ->first();

                $largestExpenses = $largestExpenses->sortByDesc('expenses')->take(5)->values()->all();
                $percentageDiff = $allExpensesAlert->percentage_diff->change_percentage . '%' . ' ($' . number_format($allExpensesAlert->current_total - $allExpensesAlert->previous_total,
                    2) . ')';

                $higherCategoryName = $higherCategoryExpense['category'] ?? '';
                $higherCategoryAmount = $higherCategoryExpense['total'] ?? 0;
                $increaseCategory = empty($higherCategoryName) || empty($higherCategoryAmount) ? '$0.00' : $higherCategoryName . ' ($' . number_format($higherCategoryAmount, 2) . ')';

                $useBetterTrackerConfig = in_array($company->enumType->value, [CompanyType::MSP_CLIENT, CompanyType::DIRECT]);
                $this->sendEmailNotification($notifyAlertSetting, new CompanyExpensesIncreaseOverTimeframeNotification(
                    $useBetterTrackerConfig,
                    $company->id,
                    $percentageDiff,
                    $increaseCategory,
                    $increaseEmailNotifications,
                    $largestExpenses,
                    $periodName,
                    null,
                    $dates
                ));
            }
        } elseif ($alertTypeKey === PlaidNotifyAlertTypesValueEnum::DECREASES_IN_MY_EXPENSES) {
            $sendDecreaseExpenseEmail = false;
            $decreaseEmailNotifications = new StdClass();
            $largestExpenses = $getLargestExpenses();

            if (!$allExpensesAlert->percentage_diff->increased && $allExpensesAlert->previous_total !== $allExpensesAlert->current_total) {
                Log::debug($source . ':: DECREASES IN MY EXPENSES SUMMARY');

                $this->storeCompanyNotification($notifyAlertSetting,
                    extraData: $getAllExpenseExtraData($allExpensesAlert));
                $sendDecreaseExpenseEmail = true;
                $decreaseEmailNotifications->showFirstColumn = true;
                $decreaseEmailNotifications->firstRow = 'Total Expenses';
                $decreaseEmailNotifications->secondRow = 'Current Period';
                $decreaseEmailNotifications->expenses = round($allExpensesAlert->current_total ?? 0, 2);
                $decreaseEmailNotifications->oldExpenses = round($allExpensesAlert->previous_total ?? 0, 3);
                $decreaseEmailNotifications->alertType = 'decrease';
                $decreaseEmailNotifications->expensesVariation = $allExpensesAlert->percentage_diff->change_percentage;
            }

            foreach ($allSubscriptionsExpenses as $subscriptionsExpense) {
                $transactions = $getSubscriptionTransactions($subscriptionsExpense);

                $currentAmount = $transactions[0]->amount ?? 0;
                $previousAmount = $transactions[1]->amount ?? 0;
                $pecentageDiff = PlaidExpensesService::getPercentageDiff($previousAmount, $currentAmount);

                if ($currentAmount < $previousAmount && $transactions->count() > 1) {
                    $storeSubscriptionExpense($subscriptionsExpense, [
                        'previous_total' => $previousAmount,
                        'current_total' => $currentAmount,
                        'percentage_diff' => $pecentageDiff,
                    ]);
                }
            }

            if ($sendDecreaseExpenseEmail) {
                Log::debug($source . ':: SEND DECREASES IN MY EXPENSES EMAIL');

                $higherCategoryExpense = $allExpensesOfThisperiod->groupBy('plaid_category_name')
                    ->map(fn ($items) => $items->sum('amount'))
                    ->sortDesc()
                    ->take(1)
                    ->map(fn ($sum, $category) => ['category' => $category, 'total' => $sum])
                    ->values()
                    ->first();

                $largestExpenses = $largestExpenses->sortByDesc('expenses')->take(5)->values()->all();

                $decreaseInfo = $allExpensesAlert->percentage_diff->change_percentage . '%' . ' ($' . number_format($allExpensesAlert->previous_total - $allExpensesAlert->current_total,
                    2) . ')';

                $higherCategoryName = $higherCategoryExpense['category'] ?? '';
                $higherCategoryAmount = $higherCategoryExpense['total'] ?? 0;
                $decreaseCategoryInfo = empty($higherCategoryName) || empty($higherCategoryAmount) ? '$0.00' : $higherCategoryName . ' ($' . number_format($higherCategoryAmount, 2) . ')';

                $useBetterTrackerConfig = in_array($company->enumType->value, [CompanyType::MSP_CLIENT, CompanyType::DIRECT]);
                $this->sendEmailNotification($notifyAlertSetting, new CompanyExpensesDecreaseOverTimeframeNotification(
                    $useBetterTrackerConfig,
                    $company->id,
                    $decreaseInfo,
                    $decreaseCategoryInfo,
                    $decreaseEmailNotifications,
                    $largestExpenses,
                    $periodName,
                    null,
                    $dates
                ));
            }
        } elseif ($alertTypeKey === PlaidNotifyAlertTypesValueEnum::STABLE_EXPENSES) {
            foreach ($allSubscriptionsExpenses as $subscriptionsExpenses) {
                $transactions = $subscriptionsExpenses->transactions()
                    ->select('plaid_transactions.date', 'plaid_transactions.amount')
                    ->orderByDesc('plaid_transactions.date')
                    ->get();

                if ($transactions->count() > 1 && $transactions[0]->amount === $transactions[1]->amount) {
                    $lastAmountChange = $transactions->where('amount', '!=', $transactions[0]->amount)->first();

                    $storeSubscriptionExpense($subscriptionsExpenses, [
                        'stable_since' => $lastAmountChange ? $lastAmountChange->date : $subscriptionsExpenses->first_date,
                        'amount' => $transactions[0]->amount,
                    ]);
                }
            }
        } elseif ($alertTypeKey === PlaidNotifyAlertTypesValueEnum::UPCOMING_EXPENSES) {
            $upComingExpenses = PlaidExpensesService::getUpcomingExpenses($company);

            if ($upComingExpenses->count() > 0) {
                $today = Carbon::now();

                $upComingExpensesCount = $upComingExpenses->count();
                $upcomingDate = PlaidExpensesService::getUpcomingExpensesDate($company);
                $totalUpcomingAmount = $upComingExpenses->sum('last_amount');
                $upcomingDaysDiff = ceil($today->diffInDays(Carbon::parse($upcomingDate)));

                $this->storeCompanyNotification($notifyAlertSetting, extraData: [
                    'count' => $upComingExpensesCount,
                    'upcoming_date' => $upcomingDate,
                    'total_amount' => $totalUpcomingAmount,
                ]);

                $dayWord = Str::plural('day', $upcomingDaysDiff);

                $upComingExpensesSummaryInfo = new StdClass();
                $upComingExpensesSummaryInfo->showFirstColumn = true;  // use this element to show/hide the first column, if note send is false by default
                $upComingExpensesSummaryInfo->firstRow = 'Total Upcoming Expenses';
                $upComingExpensesSummaryInfo->secondRow = 'Next ' . $upcomingDaysDiff . ' ' . $dayWord;
                $upComingExpensesSummaryInfo->expenses = $totalUpcomingAmount;

                $useBetterTrackerConfig = in_array($company->enumType->value, [CompanyType::MSP_CLIENT, CompanyType::DIRECT]);
                $this->sendEmailNotification($notifyAlertSetting,
                    new CompanyExpensesUpcomingRecurringChargesNotification(
                        $useBetterTrackerConfig,
                        $company->id,
                        $upComingExpensesCount,
                        $upcomingDaysDiff . ' ' . $dayWord,
                        $upComingExpensesSummaryInfo
                    ));
            }
        }
    }

    private function addPreviousCurrentAmount($query)
    {
        return $query->selectRaw('
        ROUND(SUM(CASE WHEN plaid_transactions.date BETWEEN ? AND ? THEN plaid_transactions.amount ELSE 0 END), 2) as previous_total,
        ROUND(SUM(CASE WHEN plaid_transactions.date BETWEEN ? AND ? THEN plaid_transactions.amount ELSE 0 END), 2) as current_total',
            [
                // Previous period range
                $this->previousStart,
                $this->previousEnd,
                // Current period range
                $this->currentStart,
                $this->currentEnd,
            ]);
    }

    private function sendEmailNotification(PlaidNotifyAlert $notifyAlertSetting, $notificationClass): void
    {
        $source = __CLASS__ . '::' . __FUNCTION__;
        $flagComapnyExpenseNotification = FeatureFlagService::findByName(FeatureFlagEnum::COMPANY_EXPENSES_EMAIL_NOTIFICATIONS);

        // Don't send email when flag is disabled or when we are refreshing alert settings of specific company
        if ($flagComapnyExpenseNotification->activated && !$this->companyAlertSettings) {
            $recipients = $notifyAlertSetting->recipients;

            if ($recipients) {
                $emails = $recipients->emails;
                $roleNotificationId = $recipients->role_id;

                foreach ($emails as $email) {
                    Log::debug($source . '::Send Notification to: ' . $email);
                    $notificationClass->email = $email;
                    Notification::route('mail', $email)
                        ->notify($notificationClass);
                }

                if ($roleNotificationId) {
                    $companyRoleUsers = $notifyAlertSetting->company->allUsers()
                        ->where('roles.id', $roleNotificationId)
                        ->get();

                    foreach ($companyRoleUsers as $user) {
                        Log::debug($source . '::Send Notification to user with role ID:: ' . $roleNotificationId . ' User ID::' . $user->id);
                        $user->notify($notificationClass);
                    }
                }
            }
        }
    }

    private function storeCompanyNotification(
        PlaidNotifyAlert $notifyAlertSetting,
        $subcriptionId = null,
        $extraData = []
    ): void {
        if ($notifyAlertSetting->period_id && $notifyAlertSetting->type_id) {
            PlaidCompanyNotification::create([
                'company_id' => $notifyAlertSetting->company->id,
                'period_id' => $notifyAlertSetting->period_id,
                'type_id' => $notifyAlertSetting->type_id,
                'subscription_id' => $subcriptionId,
                'extra_info' => $extraData,
            ]);
        }
    }

    public function middleware(): array
    {
        return [new WithoutOverlapping(self::class . now())];
    }
}
