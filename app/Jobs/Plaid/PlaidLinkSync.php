<?php

namespace App\Jobs\Plaid;

use App\Enums\UserNotificationActions;
use App\Jobs\StoreUsersNotifications;
use App\Models\Plaid\PlaidBankAccount;
use App\Models\Plaid\PlaidBankLink;
use App\Models\Plaid\PlaidSubscription;
use App\Models\Plaid\PlaidTransaction;
use App\Models\User;
use App\Services\Plaid\PlaidAlertService;
use App\Services\Plaid\PlaidCategoryService;
use App\Services\Plaid\PlaidExpensesService;
use App\Services\Plaid\PlaidService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class PlaidLinkSync implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 1;

    public function __construct(protected PlaidBankLink $plaidBankLink, protected ?string $userId = null, protected $firstLink = false) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $source = __CLASS__ . '::' . __FUNCTION__;
        Log::debug($source . '::JOB STARTED' . ' USER ID ' . $this->userId . ' First Link => ' . $this->firstLink);

        if (!$this->plaidBankLink->accounts()->count()) {
            Log::debug($source . ":: Bank link doesn't have any account. BANK LINK ID: " . $this->plaidBankLink->id . ' COMPANY ID: ' . $this->plaidBankLink->company_id);
            PlaidService::removeItem($this->plaidBankLink->access_token);
            $this->plaidBankLink->delete();

            return;
        }

        $finishPlaidLinkSync = function () {
            $syncTime = now();
            $this->plaidBankLink->last_sync = $syncTime;
            $this->plaidBankLink->is_syncing = false;
            $this->plaidBankLink->save();
            $this->plaidBankLink->accounts()->update(['last_sync' => $syncTime, 'is_syncing' => false]);
            $this->plaidBankLink->refresh();

            if ($this->userId) {
                dispatch(new StoreUsersNotifications(
                    $this->userId,
                    $this->userId,
                    User::class,
                    UserNotificationActions::EXPENSES_SYNCED,
                    [
                        'company_id' => '' . $this->plaidBankLink->company->id,
                        'friendly_url' => $this->plaidBankLink->company->friendly_url,
                    ]
                ));
            }
        };

        try {
            /**
             * @todo remove this workaround and make everything work with webhook call
             *
             * Workaround give time to plaid to prepare the first group of transactions
             */
            if ($this->firstLink) {
                sleep(30);
            }

            $this->expensesTransactions($this->plaidBankLink->transaction_sync_cursor);
            $this->expensesSubscriptionTransactions();
            $finishPlaidLinkSync();
            if ($this->firstLink) {
                PlaidAlertService::refreshCompanyAlertNotification($this->plaidBankLink->company);
            }
        } catch (\Exception $ex) {
            Log::error($source . '::' . $ex->getMessage() . ' >>>> ' . json_encode($ex->getTrace(), JSON_PRETTY_PRINT));
            $finishPlaidLinkSync();
        }

        Log::debug($source . '::JOB ENDED');
    }

    /**
     * @link $cursor https://plaid.com/docs/api/products/transactions/#transactions-sync-request-cursor
     */
    private function expensesTransactions(?string $cursor): void
    {
        $source = __CLASS__ . '::' . __FUNCTION__;

        $response = PlaidService::transactionSync($this->plaidBankLink->access_token, [
            'cursor' => $cursor,
            'count' => 400,
            'options' => ['include_original_description' => true],
        ]);

        if (isset($response['error_message'])) {
            Log::warning($source . $response['error_message'] . ' Company ID:: ' . $this->plaidBankLink->company_id);

            return;
        }

        foreach ($response['added'] as $transactionAdded) {
            Log::debug($source . '::Adding transaction:: ' . $transactionAdded['transaction_id'] . ' Company ID:: ' . $this->plaidBankLink->company_id);
            PlaidExpensesService::updateorCreateTransaction($transactionAdded, $this->plaidBankLink->company);
        }

        foreach ($response['modified'] as $transactionModified) {
            Log::debug($source . '::Modifing transaction:: ' . $transactionModified['transaction_id'] . ' Company ID:: ' . $this->plaidBankLink->company_id);
            PlaidExpensesService::updateorCreateTransaction($transactionModified, $this->plaidBankLink->company);
        }

        foreach ($response['removed'] as $transactionRemoved) {
            Log::debug($source . '::Removing transaction:: ' . $transactionRemoved['transaction_id'] . ' Company ID:: ' . $this->plaidBankLink->company_id);
            PlaidTransaction::where('account_id', $transactionRemoved['account_id'])
                ->where('transaction_id', $transactionRemoved['transaction_id'])
                ->delete();
        }

        if ($response['has_more']) {
            $this->expensesTransactions($response['next_cursor']);
            Log::debug($source . '::Has Next Cursor for Plaid request:: ' . $response['next_cursor'] . ' Company ID:: ' . $this->plaidBankLink->company_id);
        } else {
            $this->plaidBankLink->transaction_sync_cursor = $response['next_cursor'];
            $this->plaidBankLink->save();
            Log::debug($source . '::Transaction loop end with cursor :: ' . $response['next_cursor'] . ' Company ID:: ' . $this->plaidBankLink->company_id);
        }
    }

    private function expensesSubscriptionTransactions(): void
    {
        $source = __CLASS__ . '::' . __FUNCTION__;

        $response = PlaidService::transactionRecurringGet($this->plaidBankLink->access_token);

        if (isset($response['error_message'])) {
            Log::warning(__CLASS__ . '::' . __FUNCTION__ . '::' . $response['error_message'] . ' Company ID:: ' . $this->plaidBankLink->company_id);

            return;
        }

        foreach ($response['outflow_streams'] as $subscriptionTransactions) {
            if (!PlaidBankAccount::where('account_id', $subscriptionTransactions['account_id'])->exists()) {
                Log::debug($source . '::Skipping recurring subscription because account doesn\'t exists' . 'Company ID => ' . $this->plaidBankLink->company_id . ' Subscription Transaction Data  >>> ' . json_encode($subscriptionTransactions, JSON_PRETTY_PRINT));

                continue;
            }
            // Recurring transactions doesn't provide any logo_url, so let's use a children transaction that has logo instead
            $transactionLogo = PlaidTransaction::whereIn('transaction_id', $subscriptionTransactions['transaction_ids'])
                ->whereNotNull('logo_url')
                ->first('logo_url');

            $description = $subscriptionTransactions['description'];
            $merchantName = !empty($subscriptionTransactions['merchant_name']) ? $subscriptionTransactions['merchant_name'] : $subscriptionTransactions['description'];

            $subscription = PlaidSubscription::updateOrCreate([
                'stream_id' => $subscriptionTransactions['stream_id'],
                'company_id' => $this->plaidBankLink->company_id,
            ], [
                'account_id' => $subscriptionTransactions['account_id'],
                'description' => PlaidExpensesService::removeBlackListWords($description),
                'first_date' => $subscriptionTransactions['first_date'],
                'last_date' => $subscriptionTransactions['last_date'],
                'logo_url' => $transactionLogo ? $transactionLogo->logo_url : null,
                'predicted_next_date' => $subscriptionTransactions['predicted_next_date'],
                'frequency' => $subscriptionTransactions['frequency'],
                'average_amount' => $subscriptionTransactions['average_amount']['amount'],
                'last_amount' => $subscriptionTransactions['last_amount']['amount'],
                'iso_currency_code' => $subscriptionTransactions['last_amount']['iso_currency_code'] ?? null,
                'is_active' => $subscriptionTransactions['is_active'],
                'status' => $subscriptionTransactions['status'],
                'merchant_name' => PlaidExpensesService::removeBlackListWords($merchantName),
                'request_id' => $response['request_id'],
                'plaid_category_id' => PlaidCategoryService::getPlaidCategoryId($subscriptionTransactions),
                'deleted_at' => null,
                'extra_info' => $subscriptionTransactions['personal_finance_category'],
            ]);

            foreach ($subscriptionTransactions['transaction_ids'] as $transactionId) {
                $transaction = PlaidTransaction::where('transaction_id', $transactionId)->first();
                if ($transaction) {
                    $transaction->plaid_subscription_id = $subscription->id;
                    $transaction->save();
                }
            }

            Log::debug($source . '::Subscription Transaction Created' . 'Company ID => ' . $this->plaidBankLink->company_id . ' Subscription Transaction Data  >>> ' . json_encode($subscriptionTransactions, JSON_PRETTY_PRINT));

            PlaidExpensesService::syncSubscriptionTransactionsData($subscription);
        }
    }

    public function middleware(): array
    {
        Log::debug(__CLASS__ . '::' . __FUNCTION__ . ':: Call on middleware: ' . $this->plaidBankLink->id . ' COMPANY ID: ' . $this->plaidBankLink->company_id);

        return [new WithoutOverlapping('plaid-link-sync-' . $this->plaidBankLink->id)];
    }
}
