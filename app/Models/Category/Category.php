<?php

namespace App\Models\Category;

use App\Models\BaseModel;
use App\Models\Category\Section\CategorySection;
use App\Models\Company\Company;
use App\Models\Company\CompanyType;
use App\Models\Contract\Contract;
use App\Models\Product;
use App\Models\ProductCategory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Category extends BaseModel implements HasMedia
{
    use HasFactory;
    use InteractsWithMedia;

    protected $table = 'categories';

    public $timestamps = true;

    public $fillable = [
        'parent_id', 'name', 'is_hidden', 'color',
        'description', 'featured', 'friendly_url', 'icon_name', 'is_top_category',
        'stack_chart_min_y', 'stack_chart_max_y', 'stack_chart_min_x', 'stack_chart_max_x', 'default_my_stack_category', 'navistack_reminder',
    ];

    protected function casts(): array
    {
        return [
            'is_hidden' => 'boolean',
            'featured' => 'boolean',
            'is_top_category' => 'boolean',
        ];
    }

    public function companies(): BelongsToMany
    {
        return $this->belongsToMany(Company::class, 'company_categories')
            ->withTimestamps();
    }

    public function companyTypes(): HasManyThrough
    {
        return $this->hasManyThrough(CompanyType::class, CategoryCompanyType::class,
            'category_id', 'id', 'id', 'company_type_id');
    }

    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'product_categories')
            ->withTimestamps();
    }

    public function parentCategory(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'parent_id', 'id');
    }

    public function subCategories(): HasMany
    {
        return $this->hasMany(Category::class, 'parent_id', 'id');
    }

    public function chartSeoImage()
    {
        return $this->media()
            ->whereIn('collection_name', [config('custom.media_collections.category.chart_seo_img')])
            ->first();
    }

    public function contracts(): HasMany
    {
        return $this->hasMany(Contract::class, 'category_id', 'id');
    }

    public function scopeCastIds(Builder $query): Builder
    {
        $this->castIdsForResource();

        return $query;
    }

    public function sections(): BelongsToMany
    {
        return $this->BelongsToMany(CategorySection::class, 'category_section_categories');
    }

    public function productCategories()
    {
        return $this->hasMany(ProductCategory::class, 'category_id');
    }

    public function castIdsForResource(): void
    {
        $this->mergeCasts([
            'id' => 'string',
            'parent_id' => 'string',
        ]);
    }
}
