<?php

namespace App\Models\Company;

use App\Enums\ActivityLogAction;
use App\Enums\AnalyticAction;
use App\Enums\BlogStatus;
use App\Enums\BulletinStatus;
use App\Enums\Partner\PartnerPortalInvitationInitiator;
use App\Enums\Partner\PartnerPortalInvitationStatus;
use App\Enums\RoleKeys;
use App\Enums\SearchModelParams;
use App\Enums\UserStatus;
use App\Events\CompanySavedEvent;
use App\Events\CompanyUpdatedEvent;
use App\Models\ActivityLog\ActivityLog;
use App\Models\AffiliateBrand\AffiliateBrand;
use App\Models\Analytics\Analytics;
use App\Models\BaseModel;
use App\Models\Blog\Blog;
use App\Models\Bulletin\Bulletin;
use App\Models\Category\Category;
use App\Models\ChannelDeals\ChannelDeal;
use App\Models\ChannelDeals\ChannelDealsClaimed;
use App\Models\Chart\Vendor\ChartHiddenVendor;
use App\Models\CompanySupportSchedule\CompanySupportSchedule;
use App\Models\Contract\ClientVendor;
use App\Models\Contract\Contract;
use App\Models\Country;
use App\Models\IndustryEvent\IndustryEvent;
use App\Models\Language;
use App\Models\Lookup\LookupOptionValue;
use App\Models\MediaGallery;
use App\Models\MSPFollowingPartner;
use App\Models\MyStack\CustomerStack;
use App\Models\MyStack\MyStack;
use App\Models\NavigationFavorites;
use App\Models\Partner\PartnerPage;
use App\Models\Partner\PartnerTemplate;
use App\Models\PartnerAsset;
use App\Models\PartnerBrandableContactInfo;
use App\Models\Permission\Role\Role;
use App\Models\Plaid\Alert\PlaidCompanyNotification;
use App\Models\Plaid\Alert\PlaidNotifyAlert;
use App\Models\Plaid\Alert\PlaidNotifyRecipient;
use App\Models\Plaid\PlaidBankAccount;
use App\Models\Plaid\PlaidBankLink;
use App\Models\Plaid\PlaidSubscription;
use App\Models\Plaid\PlaidTransaction;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ProductFeature;
use App\Models\Profile\CompanyProfileType;
use App\Models\ProfileEnrichment\ProfileEnrichmentAnswer;
use App\Models\Review\Review;
use App\Models\ShoutOut\ShoutOut;
use App\Models\State;
use App\Models\Subscription\CompaniesSubscriptionHistory;
use App\Models\User;
use App\Services\BlogService;
use App\Services\Company\CompanyService;
use App\Services\MediaService;
use App\Services\Redis\RedisService;
use App\Traits\CompanyConfigurationTrait;
use App\Traits\HasStatusScope;
use App\Traits\UseProfileTypePolicies;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Staudenmeir\EloquentHasManyDeep\HasManyDeep;
use Staudenmeir\EloquentHasManyDeep\HasRelationships;

class Company extends BaseModel implements HasMedia
{
    use CompanyConfigurationTrait;
    use HasFactory;
    use HasRelationships;
    use HasStatusScope;
    use InteractsWithMedia;
    use UseProfileTypePolicies;

    public $fillable = [
        'id', 'name', 'description', 'revenue',
        'address', 'address2', 'city', 'state_id', 'zip', 'country_id',
        'phone', 'industry', 'employee_range', 'founded', 'manage_expenses',
        'profile_company_website_url', 'profile_vendor_handle', 'profile_company_friendly_name',
        'type', 'features', 'company_profile_types_id', 'friendly_url', 'subdomain', 'partner_flag',
        'is_distributor', 'show_distributor_banner', 'descriptions', 'parent_id', 'affiliate_id', 'affiliate_brand_id',
        'manage_clients', 'manage_affiliates', 'show_manage_clients_banner', 'show_manage_affiliates_banner',
        'show_affiliate_popup', 'application_source', 'application_source_id', 'hide_expenses', 'created_by', 'updated_by',
    ];

    public $searchable = SearchModelParams::CompanySearch;

    /**
     * The event map for the model.
     *
     * @var array
     */
    protected $dispatchesEvents = [
        'updated' => CompanyUpdatedEvent::class,
        'saved' => CompanySavedEvent::class,
    ];

    protected function casts(): array
    {
        return [
            'features' => 'array', 'partner_flag' => 'boolean', 'is_distributor' => 'boolean',
            'show_distributor_banner' => 'boolean', 'manage_clients' => 'boolean',
            'show_manage_clients_banner' => 'boolean', 'show_manage_affiliates_banner' => 'boolean',
            'hide_expenses' => 'boolean',
        ];
    }

    public function getIsDistributorAttribute($value)
    {
        return $value ?? false;
    }

    public function lastUpdatedBy(): HasOne
    {
        return $this->hasOne(ActivityLog::class, 'subject_id', 'id')
            ->where('action', ActivityLogAction::updated)->orderBy('created_at')
            ->limit(1);
    }

    public function configurations(): HasOne
    {
        return $this->hasOne(CompanyConfigurations::class, 'company_id');
    }

    public function whitelabeling(): HasOne
    {
        return $this->hasOne(CompanyWhitelabeling::class, 'company_id');
    }

    public function affiliateBrand(): BelongsTo
    {
        return $this->belongsTo(AffiliateBrand::class, 'affiliate_brand_id', 'id');
    }

    public function affiliateBrandAsMainCompany(): HasOne
    {
        return $this->hasOne(AffiliateBrand::class, 'main_company_id');
    }

    public function isAffiliateBrandMainCompany(): bool
    {
        return AffiliateBrand::where('main_company_id', $this->id)->exists();
    }

    public function parentCompany(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'parent_id', 'id');
    }

    public function affiliates(): HasMany
    {
        return $this->hasMany(Company::class, 'parent_id', 'id');
    }

    public function affiliatesCount(): HasMany
    {
        return $this->hasMany(Company::class, 'parent_id', 'id');
    }

    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class, 'state_id', 'id');
    }

    public function coNotificationRecipients(): HasMany
    {
        return $this->hasMany(CoNotificationRecipient::class, 'company_id', 'id');
    }

    public function scopeOfType($query, $type)
    {
        return $query->whereCompanyType($type);
    }

    public function users(): HasMany
    {
        return $this->hasMany(User::class, 'company_id', 'id')
            ->where('status', UserStatus::Active);
    }

    public function allUsers(): Builder
    {
        return User::join('roles_users', 'roles_users.user_id', '=', 'users.id')
            ->join('roles', 'roles.id', '=', 'roles_users.role_id')
            ->where('roles.company_id', $this->id)
            ->select('users.*');
    }

    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'company_id', 'id');
    }

    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'company_categories')
            ->withTimestamps();
    }

    public function stackCategories(): HasManyThrough
    {
        return $this->hasManyThrough(
            Category::class,
            MyStack::class,
            'company_id',
            'id',
            'id',
            'category_id'
        );
    }

    public function productsCategories(): HasManyDeep
    {
        return $this->hasManyDeep(
            Category::class,
            [Product::class, ProductCategory::class],
            ['company_id', 'product_id', 'id'],
            ['id', 'id', 'category_id']
        )->distinct()->orderByRaw('LOWER(categories.name)')
            ->select('categories.*', DB::raw('LOWER(categories.name) as lowercase_name'));
    }

    public function enumType(): BelongsTo
    {
        return $this->belongsTo(CompanyType::class, 'type', 'id');
    }

    public function vendor(): BelongsTo
    {
        return $this->belongsTo(CompanyType::class, 'type', 'id')
            ->isVendor();
    }

    public function scopeMsp()
    {
        return $this->whereHas('enumType', function ($query) {
            $query->where('type_is_of_vendor', false);
        });
    }

    public function contacts(): HasMany
    {
        return $this->hasMany(CompanyContact::class, 'company_id', 'id');
    }

    public function contracts(): HasMany
    {
        return $this->hasMany(Contract::class, 'owner_id', 'id');
    }

    public function addonsContracts(): HasMany
    {
        return $this->hasMany(Contract::class, 'owner_id', 'id')
            ->whereNotNull('parent_id');
    }

    public function contractsWithoutAddons(): HasMany
    {
        return $this->hasMany(Contract::class, 'owner_id', 'id')
            ->whereNull('parent_id');
    }

    public function recentContracts(): HasMany
    {
        return $this->hasMany(Contract::class, 'owner_id', 'id')
            ->whereNull('parent_id')
            ->where('created_at', '>=', now()->subDays(30));
    }

    public function contactInformation(): HasOne
    {
        return $this->hasOne(CompanyContactInformation::class, 'company_id', 'id');
    }

    public function companyClaimers(): HasMany
    {
        return $this->hasMany(CompanyClaimer::class, 'company_id', 'id');
    }

    /**
     * @throws ValidationException
     */
    public function superAdminRole(): Role
    {
        $key = (string)$this->id === config('custom.channel_program_company.id') ?
            RoleKeys::CLAIMER : 'SUPER_ADMIN_' . $this->getTypeKeyForRole() . '_' . $this->id;
        $role = CompanyService::findRoleByKey($key);

        return $role;
    }

    /**
     * @throws ValidationException
     */
    public function readOnlyRole(): Role
    {
        $key = (string)$this->id === config('custom.channel_program_company.id') ?
            RoleKeys::SYSTEMUSER : 'READ_ONLY_' . $this->getTypeKeyForRole() . '_' . $this->id;
        $role = CompanyService::findRoleByKey($key);

        return $role;
    }

    /**
     * @throws ValidationException
     */
    public function restrictedAccessRole(): Role
    {
        $key = (string)$this->id === config('custom.channel_program_company.id') ?
            RoleKeys::SYSTEMUSER : RoleKeys::RESTRICTED_ACCESS . '_' . $this->id;
        $role = CompanyService::findRoleByKey($key);

        return $role;
    }

    public function claimers(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'company_claimers', 'company_id', 'user_id')
            ->where('status', UserStatus::Active)
            ->withTimestamps();
    }

    public function countriesSupported(): BelongsToMany
    {
        return $this->belongsToMany(Country::class, 'company_countries_supported', 'company_id', 'country_id')
            ->withTimestamps();
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    public function languagesSupported(): BelongsToMany
    {
        return $this->belongsToMany(Language::class, 'company_languages_supported', 'company_id', 'language_id')
            ->withTimestamps();
    }

    public function usersFollowingMe(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'users_following_companies', 'followed_company_id', 'follower_user_id')
            ->where('users.status', UserStatus::Active)
            ->withTimestamps();
    }

    public function usersFollowingMeCount(): int
    {
        return $this->belongsToMany(User::class, 'users_following_companies', 'followed_company_id', 'follower_user_id')
            ->select('id')
            ->where('users.status', UserStatus::Active)
            ->count();
    }

    public function userIsFollowingMe(string $userId): bool
    {
        return $this->belongsToMany(User::class, 'users_following_companies', 'followed_company_id', 'follower_user_id')
            ->select('users.id')
            ->where('users.status', UserStatus::Active)
            ->where('users.id', $userId)
            ->exists();
    }

    public function profileType(): BelongsTo
    {
        return $this->companyProfileType();
    }

    public function companyProfileType(): BelongsTo
    {
        return $this->belongsTo(CompanyProfileType::class, 'company_profile_types_id', 'id');
    }

    public function profileEnrichmentAnswers(): HasMany
    {
        return $this->hasMany(ProfileEnrichmentAnswer::class, 'company_id', 'id');
    }

    public function profileVideos(): MorphMany
    {
        return $this->media()->where('collection_name', config('custom.media_collections.videos'))
            ->where(function ($q) {
                $q->whereRaw("custom_properties->>'is_active' = 'true' OR custom_properties->>'is_active' IS NULL");
            })->where(function ($q) {
                $q->whereRaw("custom_properties->>'is_partner_content' = '0' OR custom_properties->>'is_partner_content' IS NULL");
            });
    }

    public function documents(): MorphMany
    {
        return $this->media()->where('collection_name', config('custom.media_collections.documents'))
            ->where(function ($q) {
                $q->whereRaw("custom_properties->>'is_active' = 'true' OR custom_properties->>'is_active' IS NULL");
            })
            ->where(function ($q) {
                $q->whereRaw("custom_properties->>'is_partner_content' = '0' OR custom_properties->>'is_partner_content' IS NULL");
            });
    }

    public function template(): MorphMany
    {
        return $this->media()->where('collection_name', config('custom.media_collections.brandable'))
            ->where(function ($q) {
                $q->whereRaw("custom_properties->>'is_active' = 'true' OR custom_properties->>'is_active' IS NULL");
            })
            ->where(function ($q) {
                $q->whereRaw("custom_properties->>'is_partner_content' = '0' OR custom_properties->>'is_partner_content' IS NULL");
            });
    }

    public function myStack(): BelongsToMany
    {
        return $this->belongsToMany(Company::class, 'my_stack', 'company_id', 'stack_company_id')
            ->with('avatar')
            ->withPivot(['id', 'category_id', 'product_id', 'user_id', 'partner_status', 'parent_stack_id', 'created_at', 'updated_at'])
            ->where('is_recommended_stack', false)
            ->whereNull('deleted_at')
            ->using(MyStack::class)
            ->withTimestamps();
    }

    public function customerStack(): BelongsToMany
    {
        return $this->belongsToMany(ClientVendor::class, 'customer_stack', 'company_id', 'stack_company_id')
            ->withPivot(['id', 'category_id', 'product_id', 'user_id', 'partner_status', 'created_at', 'updated_at'])
            ->whereNull('deleted_at')
            ->using(CustomerStack::class)
            ->withTimestamps();
    }

    public function rawMyStack(): HasMany
    {
        return $this->hasMany(MyStack::class, 'company_id', 'id')
            ->where('is_recommended_stack', false)
            ->whereNull('deleted_at');
    }

    public function recommendedMyStack(): BelongsToMany
    {
        return $this->belongsToMany(Company::class, 'my_stack', 'company_id', 'stack_company_id')
            ->with('avatar')
            ->withPivot(['id', 'category_id', 'product_id', 'user_id', 'partner_status', 'parent_stack_id', 'created_at', 'updated_at'])
            ->where('is_recommended_stack', true)
            ->whereNull('deleted_at')
            ->using(MyStack::class)
            ->withTimestamps();
    }

    public function rawRecommendedMyStack(): HasMany
    {
        return $this->hasMany(MyStack::class, 'company_id', 'id')
            ->where('is_recommended_stack', true)
            ->whereNull('deleted_at');
    }

    public function topBanner(): MorphOne
    {
        return $this->morphOne(Media::class, 'model')
            ->where('collection_name', config('custom.media_collections.top_banner'));
    }

    public function avatar(): MorphOne
    {
        return $this->morphOne(Media::class, 'model')
            ->whereIn('collection_name', [config('custom.media_collections.avatar.company')]);
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection(config('custom.media_collections.avatar.company'))->singleFile();
        $this->addMediaCollection(config('custom.media_collections.avatar.manager'))->singleFile();
        $this->addMediaCollection(config('custom.media_collections.top_banner'))->singleFile();
    }

    /**
     * Get all the company"s media galleries.
     */
    public function mediaGalleries(): MorphMany
    {
        return $this->morphMany(MediaGallery::class, 'model')
            ->where('is_active', true)
            ->where(function ($q) {
                $q->whereRaw("media_galleries.custom_properties->>'is_partner_content' = '0' OR media_galleries.custom_properties->>'is_partner_content' IS NULL");
            });
    }

    /**
     * Get all the user"s media galleries.
     */
    public function partnerAssets(): MorphMany
    {
        return $this->morphMany(PartnerAsset::class, 'model');
    }

    public function partnerTemplate(): MorphMany
    {
        return $this->morphMany(PartnerTemplate::class, 'model');
    }

    public function shoutOuts(): HasMany
    {
        return $this->hasMany(ShoutOut::class, 'subject_id', 'id')
            ->with('author');
    }

    public function productFeatures(): HasManyThrough
    {
        return $this->hasManyThrough(ProductFeature::class, Product::class, 'company_id',
            'product_id', 'id', 'id')
            ->where('display_on_feed', 1);
    }

    public function clients(): BelongsToMany
    {
        return $this->belongsToMany(Company::class, 'company_clients', 'company_id', 'client_id');
    }

    public function clientParent(): HasOneThrough
    {
        return $this->hasOneThrough(Company::class, CompanyClient::class, 'client_id',
            'id', 'id', 'company_id');
    }

    public function industryEvents(): HasMany
    {
        return $this->hasMany(IndustryEvent::class, 'subject_id', 'id');
    }

    public function blogs(bool $withRelations = true): HasMany
    {
        $query = $this->hasMany(Blog::class, 'subject_id', 'id');
        if ($withRelations) {
            $query->with('tags', 'categories', 'author');
        }
        $query->where('is_partner_content', false);
        $query->where('is_active', true);

        return BlogService::prepareStatusQuery($query, $this);
    }

    public function publishedBlogs(): HasMany
    {
        return $this->hasMany(Blog::class, 'subject_id', 'id')
            ->with('tags', 'categories', 'author')
            ->where('status', BlogStatus::Published)
            ->where('is_active', true)
            ->where('is_partner_content', false);
    }

    public function likes(): HasMany
    {
        return $this->hasMany(Analytics::class, 'subject_id', 'id')
            ->where('action', AnalyticAction::like);
    }

    public function productReviews(): HasManyThrough
    {
        return $this->hasManyThrough(
            Review::class,
            Product::class,
            'company_id',
            'model_id'
        );
    }

    public function hiddenFromCharts(): HasMany
    {
        return $this->hasMany(ChartHiddenVendor::class, 'company_id', 'id');
    }

    public function partnerFollowingMe(): BelongsToMany
    {
        return $this->belongsToMany(
            Company::class,
            'company_partners',
            'followed_partner_id',
            'follower_partner_id'
        )
            ->withPivot([
                'id', 'status', 'initiator', 'invited_by', 'invited_at', 'accepted_by', 'accepted_at',
                'rejected_by', 'rejected_at', 'rejected_reason', 'email', 'accepted_reason',
                'accepted_reason_other', 'rejected_reason', 'rejected_reason_other', 'deleted_at',
            ])
            ->whereNull('company_partners.deleted_at')
            ->withTimestamps()
            ->using(MSPFollowingPartner::class);
    }

    public function usersFollowingMyPortal(): BelongsToMany
    {
        return $this->belongsToMany(
            User::class,
            'company_partners',
            'followed_partner_id',
            'follower_partner_id',
            'id',
            'company_id'
        )
            ->where('company_partners.status', PartnerPortalInvitationStatus::Accepted)
            ->whereNull('company_partners.deleted_at')
            ->withTimestamps();
    }

    public function partnerIFollow(): BelongsToMany
    {
        return $this->belongsToMany(
            Company::class,
            'company_partners',
            'follower_partner_id',
            'followed_partner_id'
        )
            ->withPivot([
                'id', 'status', 'initiator', 'invited_by', 'invited_at', 'accepted_by', 'accepted_at',
                'rejected_by', 'rejected_at', 'rejected_reason', 'email', 'accepted_reason',
                'accepted_reason_other', 'rejected_reason', 'rejected_reason_other', 'deleted_at',
            ])
            ->whereNull('company_partners.deleted_at')
            ->withTimestamps()
            ->using(MSPFollowingPartner::class);
    }

    public function partnerPage(): HasOne
    {
        return $this->hasOne(PartnerPage::class, 'company_id', 'id');
    }

    public function subject(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'id')->whereNull('id');
    }

    public function supportSchedule(): HasOne
    {
        return $this->hasOne(CompanySupportSchedule::class, 'company_id', 'id');
    }

    /**
     * Get all the socials for the Company
     */
    public function socials(): HasMany
    {
        return $this->hasMany(CompanySocialMedia::class, 'company_id');
    }

    /**
     * Get all invites for a company filtering by status and companyType (CompanyType enum)
     */
    public function invites(?bool $status = null, ?string $companyType = null): HasMany
    {
        return $this->hasMany(CompanyInvite::class, 'child_company_id', 'id')
            ->where('activated', $status ?? false)
            ->where('type', $companyType ?? $this->enumType->value);
    }

    /**
     * Get all the subscription history for the Company
     */
    public function subscriptionHistory(): HasMany
    {
        return $this->hasMany(CompaniesSubscriptionHistory::class, 'company_id')
            ->orderBy('started_at', 'DESC');
    }

    /**
     * Get all the brandableContactInfo for the Company
     */
    public function brandableContactInfo(): HasMany
    {
        return $this->hasMany(PartnerBrandableContactInfo::class, 'company_id');
    }

    public function roles(): HasMany
    {
        return $this->hasMany(Role::class, 'company_id', 'id');
    }

    public function channelDeals(): HasMany
    {
        return $this->hasMany(ChannelDeal::class, 'company_id', 'id');
    }

    public function activeBulletins(): HasMany
    {
        return $this->hasMany(Bulletin::class, 'company_id', 'id')
            ->whereRaw("
                CASE 
                    WHEN time_zone IN (SELECT name FROM pg_timezone_names) 
                    THEN now() AT TIME ZONE time_zone
                    ELSE now() AT TIME ZONE 'UTC'
                END 
                BETWEEN start_date AND end_date
            ")
            ->where('status', BulletinStatus::active);
    }

    public function plaidBankLinks(): HasMany
    {
        return $this->hasMany(PlaidBankLink::class, 'company_id', 'id');
    }

    public function plaidBankAcounts(): HasMany
    {
        return $this->hasMany(PlaidBankAccount::class, 'company_id', 'id');
    }

    public function plaidSubscriptions(): HasMany
    {
        return $this->hasMany(PlaidSubscription::class, 'company_id', 'id');
    }

    public function plaidSubscriptionTransactions(): HasMany
    {
        return $this->hasMany(PlaidTransaction::class, 'company_id', 'id')
            ->join('plaid_subscriptions', 'plaid_subscriptions.id', '=', 'plaid_transactions.plaid_subscription_id');
    }

    public function plaidTransactions(): HasMany
    {
        return $this->hasMany(PlaidTransaction::class, 'company_id', 'id');
    }

    public function marketplacePartners(): BelongsToMany
    {
        return $this->belongsToMany(Company::class, 'company_marketplace_partners',
            'distributor_id', 'partner_id')
            ->withPivot(['id', 'description', 'external_url', 'created_at', 'updated_at'])
            ->using(CompanyMarketplacePartner::class)
            ->withTimestamps();
    }

    public function distributors(): BelongsToMany
    {
        return $this->belongsToMany(Company::class, 'company_marketplace_partners',
            'partner_id', 'distributor_id')
            ->withPivot(['id', 'description', 'external_url', 'created_at', 'updated_at'])
            ->using(CompanyMarketplacePartner::class)
            ->withTimestamps();
    }

    public function followChannelProgram(): void
    {
        if (!$this->partnerIFollow()
            ->where('followed_partner_id', config('custom.channel_program_company.id'))
            ->exists()
        ) {
            $acceptedReason = LookupOptionValue::where('is_active', true)->where('name', config('custom.prm.mutual_accept_reason'))->first()->id;

            $this->partnerIFollow()
                ->attach(
                    config('custom.channel_program_company.id'),
                    [
                        'status' => PartnerPortalInvitationStatus::Accepted,
                        'initiator' => PartnerPortalInvitationInitiator::Partner,
                        'accepted_reason' => $acceptedReason,
                        'invited_at' => now(),
                        'accepted_at' => now(),
                        'email' => $this->claimers()->first()->email ?? null,
                    ]
                );
        }
    }

    private function getTypeKeyForRole(): string
    {
        if ($this->enumType->value === \App\Enums\Company\CompanyType::MSP_CLIENT) {
            $typeKey = \App\Enums\Company\CompanyType::MSP_CLIENT;
        } elseif ($this->enumType->value === \App\Enums\Company\CompanyType::DIRECT) {
            $typeKey = \App\Enums\Company\CompanyType::DIRECT;
        } elseif ($this->enumType->type_is_of_vendor) {
            $typeKey = 'VENDOR';
        } else {
            $typeKey = 'MSP';
        }

        return $typeKey;
    }

    protected function avatarSource(): Attribute
    {
        return Attribute::make(
            get: fn (): ?string => $this->avatar ? Storage::temporaryUrlForDisk($this->avatar->getPath(), MediaService::getExpirationTime(), $this->avatar->disk) : null,
        );
    }

    protected function hasApprovedDeals(): Attribute
    {
        return Attribute::make(
            get: fn (): bool => ChannelDeal::approved()->where('company_id', $this->id)->exists(),
        );
    }

    protected function hasApprovedDealRequests(): Attribute
    {
        return Attribute::make(
            get: fn (): bool => ChannelDealsClaimed::approved()->where('company_id', $this->id)->exists(),
        );
    }

    protected function rating(): Attribute
    {
        return Attribute::make(
            get: function (): ?float {
                $val = round((float)RedisService::getCompanyRating($this), 2);

                return $val === 0.0 ? null : $val;
            },
        );
    }

    public function navigationFavorites(): HasMany
    {
        return $this->hasMany(NavigationFavorites::class, 'company_id', 'id');
    }

    public function plaidNotifyAlerts(): HasMany
    {
        return $this->hasMany(PlaidNotifyAlert::class);
    }

    public function plaidCompanyNotifications(): HasMany
    {
        return $this->hasMany(PlaidCompanyNotification::class);
    }

    public function plaidNotifyRecipient(): HasOne
    {
        return $this->hasOne(PlaidNotifyRecipient::class);
    }
}
