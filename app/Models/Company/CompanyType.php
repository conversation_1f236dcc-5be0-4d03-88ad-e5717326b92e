<?php

namespace App\Models\Company;

use App\Models\Category\Category;
use App\Models\Category\CategoryCompanyType;
use App\Models\Profile\CompanyProfileType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

class CompanyType extends Model
{
    protected $fillable = ['label', 'value', 'order', 'type_is_of_vendor', 'default_company_profile_type_id'];

    protected function casts(): array
    {
        return [
            'order' => 'integer',
            'type_is_of_vendor' => 'boolean',
        ];
    }

    public function getEnumAttribute(): array
    {
        return [$this->value => $this->label];
    }

    public function scopeIsVendor($query)
    {
        return $query->where('type_is_of_vendor', true);
    }

    public function scopeIsMsp($query)
    {
        return $query->where('type_is_of_vendor', false);
    }

    public function defaultCompanyProfileType(): BelongsTo
    {
        return $this->belongsTo(CompanyProfileType::class, 'default_company_profile_type_id', 'id');
    }

    public function categories(): HasManyThrough
    {
        return $this->hasManyThrough(
            Category::class,
            CategoryCompanyType::class,
            'company_type_id',
            'id',
            'id',
            'category_id'
        );
    }
}
