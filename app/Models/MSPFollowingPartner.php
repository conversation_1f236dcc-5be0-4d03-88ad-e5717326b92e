<?php

namespace App\Models;

use App\Enums\AnalyticAction;
use App\Enums\SearchModelParams;
use App\Enums\ShortenableType;
use App\Jobs\StoreUsersNotifications;
use App\Models\Company\Company;
use App\Models\Lookup\LookupOptionValue;
use App\Notifications\Invite\PartnerInviteNotification;
use App\Services\ShortenerService;
use App\Traits\UseTokenTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;
use Illuminate\Notifications\Notifiable;

class MSPFollowingPartner extends Pivot
{
    // SoftDelete doesn't work for Pivot table so we need to check for null where needed
    use HasFactory, Notifiable, UseTokenTrait;

    public $incrementing = true;

    protected $table = 'company_partners';

    public $searchable = SearchModelParams::PartnerInviteSearch;

    protected function casts(): array
    {
        return [
            'invited_at' => 'datetime',
            'accepted_at' => 'datetime',
            'deleted_at' => 'datetime',
            'last_sent_invite_at' => 'datetime',
        ];
    }

    /**
     * Send Invite notification to the user.
     */
    public function sendInviteNotification(): void
    {
        $partner = $this->partner;
        $url = config('app.fe_url') . '/partner-invitation/' . $partner->subdomain . '/' . $this->token;

        $this->notify(new PartnerInviteNotification($this->email, $url, $partner->name));
    }

    public function sendRequestedNotification(): void
    {
        dispatch(new StoreUsersNotifications($this->invitedBy?->id, $this->partner->id, Company::class, AnalyticAction::partnerPortalRequestInvite));
    }

    public function getShortUrl()
    {
        return ShortenerService::getFEUrlforModelType(ShortenableType::coerce('PartnerInvite'), $this);
    }

    /**
     * Get the partner that owns the MSPFollowingPartner
     */
    public function partner(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'followed_partner_id');
    }

    /**
     * Get the follower_partner that owns the MSPFollowingPartner
     */
    public function followerPartner(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'follower_partner_id');
    }

    /**
     * Get the invitedBy that owns the MSPFollowingPartner
     */
    public function invitedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'invited_by');
    }

    /**
     * Get the acceptedBy that owns the MSPFollowingPartner
     */
    public function acceptedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'accepted_by');
    }

    /**
     * Get the rejectedBy that owns the MSPFollowingPartner
     */
    public function rejectedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'rejected_by');
    }

    /**
     * Get the user by the email in the invitation
     */
    public function invitedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'email', 'email');
    }

    public function partnershipType(): BelongsTo
    {
        return $this->belongsTo(LookupOptionValue::class, 'accepted_reason', 'id');
    }
}
