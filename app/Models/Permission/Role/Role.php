<?php

namespace App\Models\Permission\Role;

use App\Models\Company\Company;
use App\Models\Permission\Group\PermissionGroup;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

class Role extends Model
{
    use HasFactory;

    public $fillable = ['company_id', 'title', 'display_name', 'description', 'key', 'template_role_id', 'is_admin'];

    protected function casts(): array
    {
        return [
            'is_admin' => 'boolean',
        ];
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function templateRole(): BelongsTo
    {
        return $this->belongsTo(TemplateRole::class, 'template_role_id', 'id');
    }

    public function permissionGroups(): HasManyThrough
    {
        return $this->hasManyThrough(PermissionGroup::class, PermissionRole::class,
            'role_id', 'id', 'id', 'permission_group_id');
    }

    /**
     * Get the users that belong to the role.
     * This method is cached for better performance.
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'roles_users', 'role_id', 'user_id');
    }
}
