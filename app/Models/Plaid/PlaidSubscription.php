<?php

namespace App\Models\Plaid;

use App\Enums\Company\CompanyType as CompanyTypeEnum;
use App\Models\BaseModel;
use App\Models\Category\Category;
use App\Models\Company\Company;
use App\Models\Contract\ClientProduct;
use App\Models\Contract\Contract;
use App\Models\MyStack\CustomerStack;
use App\Models\MyStack\MyStack;
use App\Models\Plaid\Alert\PlaidCompanyNotification;
use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class PlaidSubscription extends BaseModel
{
    use HasFactory;

    public $timestamps = true;

    protected $fillable = [
        'stream_id',
        'description',
        'first_date',
        'last_date',
        'predicted_next_date',
        'frequency',
        'average_amount',
        'last_amount',
        'is_active',
        'request_id',
        'merchant_name',
        'notes',
        'logo_url',
        'account_id',
        'company_id',
        'category_id',
        'product_id',
        'sub_category_id',
        'contract_id',
        'vendor_id',
        'ignore',
        'plaid_category_id',
        'plaid_sub_category_id',
        'productable_type',
        'my_stack_id',
        'stackable_type',
        'extra_info',
    ];

    protected function casts(): array
    {
        return [
            'id' => 'string',
            'company_id' => 'string',
            'category_id' => 'string',
            'sub_category_id' => 'string',
            'plaid_category_id' => 'string',
            'plaid_sub_category_id' => 'string',
            'product_id' => 'string',
            'vendor_id' => 'string',
            'contract_id' => 'string',
            'first_date' => 'date',
            'last_date' => 'date',
            'predicted_next_date' => 'date',
            'request_id' => 'encrypted',
            'notes' => 'encrypted',
            'average_amount' => 'encrypted',
            'my_stack_id' => 'string',
            'extra_info' => 'array',
        ];
    }

    public function plaidBankAccount(): BelongsTo
    {
        return $this->belongsTo(PlaidBankAccount::class, 'account_id', 'account_id');
    }

    /**
     * @todo remove this relation and plaid_subscription_transactions after migration is done on production
     */
    public function transactionsExpenses(): BelongsToMany
    {
        return $this->BelongsToMany(
            PlaidTransaction::class,
            'plaid_subscription_transactions',
            'subscription_stream_id',
            'transaction_id',
            'stream_id',
            'transaction_id'
        );
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(PlaidTransaction::class);
    }

    public function product(): MorphTo
    {
        return $this->morphTo(null, 'productable_type', 'product_id');
    }

    public function myStack(): MorphTo
    {
        return $this->morphTo(null, 'stackable_type', 'my_stack_id');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'vendor_id', 'id');
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    public function subCategory(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'sub_category_id');
    }

    public function contract(): BelongsTo
    {
        return $this->belongsTo(Contract::class, 'contract_id', 'id');
    }

    public function plaidCompanyNotifications(): HasMany
    {
        return $this->hasMany(PlaidCompanyNotification::class,  'subscription_id', 'id');
    }

    public function plaidCategory(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'plaid_category_id');
    }

    public function scopeNotIgnored($query)
    {
        return $query->where('plaid_subscriptions.ignore', false);
    }

    public function scopeStackOnly($query)
    {
        return $query->whereNotNull('plaid_subscriptions.my_stack_id');
    }

    public function scopeUnmappedExpenses($query)
    {
        return $query->whereNull('plaid_subscriptions.my_stack_id');
    }

    public function scopeProductJoin($query, Company $company)
    {
        if ($company->enumType->value === CompanyTypeEnum::MSP_CLIENT) {
            $query->leftJoin('client_products as products', 'products.id', '=', 'plaid_subscriptions.product_id');
        } else {
            $query->leftJoin('products', 'products.id', '=', 'plaid_subscriptions.product_id');
        }
    }

    protected static function boot(): void
    {
        parent::boot();

        $handleMorphTypes = function ($model) {
            if (!$model->productable_type && $model->product_id) {
                if (Product::where('id', $model->product_id)->exists()) {
                    $model->productable_type = Product::class;
                } elseif (ClientProduct::where('id', $model->product_id)->exists()) {
                    $model->productable_type = ClientProduct::class;
                }
            }

            if (!$model->stackable_type && $model->my_stack_id) {
                if (MyStack::where('id', $model->my_stack_id)->exists()) {
                    $model->stackable_type = MyStack::class;
                } elseif (CustomerStack::where('id', $model->my_stack_id)->exists()) {
                    $model->stackable_type = CustomerStack::class;
                }
            }
        };

        static::creating($handleMorphTypes);
        static::updating($handleMorphTypes);
    }
}
