<?php

namespace App\Models\Plaid;

use App\Enums\Company\CompanyType as CompanyTypeEnum;
use App\Models\BaseModel;
use App\Models\Category\Category;
use App\Models\Company\Company;
use App\Models\Contract\ClientProduct;
use App\Models\Contract\Contract;
use App\Models\MyStack\CustomerStack;
use App\Models\MyStack\MyStack;
use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class PlaidTransaction extends BaseModel
{
    use HasFactory;

    protected $fillable = [
        'transaction_id',
        'amount',
        'date',
        'ignore',
        'iso_currency_code',
        'merchant_entity_id',
        'merchant_name',
        'description',
        'notes',
        'account_id',
        'company_id',
        'category_id',
        'product_id',
        'sub_category_id',
        'contract_id',
        'vendor_id',
        'logo_url',
        'plaid_category_id',
        'plaid_sub_category_id',
        'my_stack_id',
        'productable_type',
        'stackable_type',
        'plaid_subscription_id',
        'extra_info',
    ];

    protected function casts(): array
    {
        return [
            'id' => 'string',
            'company_id' => 'string',
            'category_id' => 'string',
            'product_id' => 'string',
            'sub_category_id' => 'string',
            'plaid_category_id' => 'string',
            'plaid_sub_category_id' => 'string',
            'contract_id' => 'string',
            'vendor_id' => 'string',
            'date' => 'date',
            'notes' => 'encrypted',
            'my_stack_id' => 'string',
            'plaid_subscription_id' => 'string',
            'extra_info' => 'array',
        ];
    }

    public function plaidBankAccount(): BelongsTo
    {
        return $this->belongsTo(PlaidBankAccount::class, 'account_id', 'account_id');
    }

    public function product(): MorphTo
    {
        return $this->morphTo(null, 'productable_type', 'product_id');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'vendor_id', 'id');
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    public function subCategory(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'sub_category_id');
    }

    public function plaidCategory(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'plaid_category_id');
    }

    public function contract(): BelongsTo
    {
        return $this->belongsTo(Contract::class, 'contract_id', 'id');
    }

    public function myStack(): MorphTo
    {
        return $this->morphTo(null, 'stackable_type', 'my_stack_id');
    }

    public function subscription(): BelongsTo
    {
        return $this->belongsTo(PlaidSubscription::class, 'plaid_subscription_id');
    }

    public function scopeStackOnly($query)
    {
        return $query->whereNotNull('plaid_transactions.my_stack_id');
    }

    public function scopeNotIgnored($query)
    {
        return $query->where('plaid_transactions.ignore', false);
    }

    public function scopeUnmappedExpenses($query)
    {
        return $query->whereNull('plaid_transactions.my_stack_id');
    }

    public function scopeProductJoin($query, Company $company)
    {
        if ($company->enumType->value === CompanyTypeEnum::MSP_CLIENT) {
            $query->leftJoin('client_products as products', 'products.id', '=', 'plaid_transactions.product_id');
        } else {
            $query->leftJoin('products', 'products.id', '=', 'plaid_transactions.product_id');
        }
    }

    protected static function boot(): void
    {
        parent::boot();

        $handleMorphTypes = function ($model) {
            if (!$model->productable_type && $model->product_id) {
                if (Product::where('id', $model->product_id)->exists()) {
                    $model->productable_type = Product::class;
                } elseif (ClientProduct::where('id', $model->product_id)->exists()) {
                    $model->productable_type = ClientProduct::class;
                }
            }

            if (!$model->stackable_type && $model->my_stack_id) {
                if (MyStack::where('id', $model->my_stack_id)->exists()) {
                    $model->stackable_type = MyStack::class;
                } elseif (CustomerStack::where('id', $model->my_stack_id)->exists()) {
                    $model->stackable_type = CustomerStack::class;
                }
            }
        };

        static::creating($handleMorphTypes);
        static::updating($handleMorphTypes);
    }
}
