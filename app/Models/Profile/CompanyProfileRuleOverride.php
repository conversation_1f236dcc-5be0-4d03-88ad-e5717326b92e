<?php

namespace App\Models\Profile;

use App\Models\BaseModel;
use App\Models\Company\Company;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CompanyProfileRuleOverride extends BaseModel
{
    protected $fillable = [
        'company_id',
        'profile_rules_id',
        'rule_value',
        'expires_at',
        'notes',
    ];

    protected $casts = [
        'expires_at' => 'datetime',
    ];

    protected $table = 'company_profile_rule_overrides';

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function profileRule(): BelongsTo
    {
        return $this->belongsTo(ProfileRule::class, 'profile_rules_id', 'id');
    }
}
