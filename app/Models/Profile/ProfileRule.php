<?php

namespace App\Models\Profile;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ProfileRule extends Model
{
    protected $fillable = ['id', 'rule', 'description', 'value_type'];

    public function values(): HasMany
    {
        return $this->hasMany(ProfileRuleValue::class, 'profile_rules_id', 'id');
    }
    public function customValues(): HasMany
    {
        return $this->hasMany(ProfileCustomRuleValue::class, 'profile_rules_id', 'id');
    }
}
