<?php

namespace App\Models;

use App\Enums\AnalyticAction;
use App\Enums\BlogStatus;
use App\Enums\Cache\CacheTTLEnum;
use App\Enums\FeatureFlagEnum;
use App\Enums\ProfileImageType;
use App\Enums\SearchModelParams;
use App\Enums\UserStatus;
use App\Enums\VerificationMode;
use App\Events\UserUpdatedEvent;
use App\Models\Analytics\Analytics;
use App\Models\Blog\Blog;
use App\Models\Company\Company;
use App\Models\Company\CompanyClaimer;
use App\Models\Company\CompanyClient;
use App\Models\Permission\Group\PermissionGroup;
use App\Models\Permission\Role\Role;
use App\Models\PitchEvent\PitchEvent;
use App\Models\Review\Review;
use App\Models\ShoutOut\ShoutOut;
use App\Notifications\ClaimerNotification;
use App\Notifications\EmailConfirmedNotification;
use App\Notifications\EmailConfirmNotification;
use App\Notifications\EmailRegisteredDirectNotification;
use App\Notifications\EmailRegisteredMSPNotification;
use App\Notifications\MSPClaimerNotification;
use App\Notifications\ResetPasswordNotification;
use App\Notifications\VerificationCodeNotification;
use App\Permissions\TwoFactorAuthenticatableTrait;
use App\Services\BlogService;
use App\Services\Company\CompanyClientService;
use App\Services\ImageService;
use App\Services\Notification\AdminUserNotificationService;
use App\Services\Permission\RoleService;
use App\Services\Plivo\PlivoService;
use App\Traits\ActivityLogTrait;
use App\Traits\UseProfileTypePolicies;
use Illuminate\Auth\Passwords\CanResetPassword;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use PHPOpenSourceSaver\JWTAuth\Contracts\JWTSubject;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class User extends Authenticatable implements HasMedia, JWTSubject, MustVerifyEmail
{
    // We are calling ActivityLogTrait directly here because we are not able to extends Basemodel because it"s already extends with Authenticatable
    use ActivityLogTrait;
    use CanResetPassword;
    use HasFactory;
    use InteractsWithMedia;
    use Notifiable;
    use TwoFactorAuthenticatableTrait;
    use UseProfileTypePolicies;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'id',
        'first_name',
        'last_name',
        'email',
        'password',
        'email_verified_at',
        'failed_login_lock',
        'company_id',
        'referred_by',
        'hubspot_contact_id',
        'two_factor_confirmed',
        'handle',
        'friendly_url',
        'description',
        'user_profile_types_id',
        'registered_all_pitches',
        'is_private',
        'phone',
        'mobile_phone',
        'job_title_id',
        'show_on_vendor_related_section',
        'verification_code',
        'verification_code_expiration_date',
        'verification_code_verified',
        'phone_verified_at',
        'verification_mode',
        'last_logged_in_at',
        'status',
        'registration_url',
        'email_verify_results',
        'email_verified_manually',
        'event_email_subscribed',
        'country',
        'state',
        'city',
        'red_flagged', 'red_flagged_by', 'red_flag_reason', 'is_under_review',
    ];

    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_recovery_codes',
        'two_factor_secret',
    ];

    public array $searchable = SearchModelParams::User;

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function claimedCompanies(): BelongsToMany
    {
        return $this->belongsToMany(Company::class, 'company_claimers', 'user_id', 'company_id')
            ->withTimestamps();
    }

    public function companyClaimers(): HasMany
    {
        return $this->hasMany(CompanyClaimer::class, 'user_id', 'id');
    }

    public function permissionGroups(string $companyId): Collection
    {
        $rolesIds = $this->roles()->where('company_id', $companyId)->pluck('roles.id');
        $permissionsGroupsCacheKey = "pgfuc_{$this->id}_{$companyId}";

        return Cache::remember($permissionsGroupsCacheKey, now()->addSeconds(CacheTTLEnum::CACHE_TTL_WEEK),
            function () use ($rolesIds) {
                return
                     PermissionGroup::select('permission_groups.id', 'permission_feature_id', 'key', 'title', 'description')
                         ->join('permissions_roles', 'permissions_roles.permission_group_id', 'permission_groups.id')
                         ->whereIn('role_id', $rolesIds)
                         ->get();
            });
    }

    public function adminPermissionGroups(string $companyId): Collection
    {
        $rolesIds = $this->roles()->where('company_id', $companyId)->pluck('roles.id');
        $permissionsGroupsCacheKey = "apgfuc_{$this->id}_{$companyId}";

        return Cache::remember($permissionsGroupsCacheKey, now()->addSeconds(CacheTTLEnum::CACHE_TTL_WEEK),
            function () use ($rolesIds) {
                return PermissionGroup::select('permission_groups.id', 'permission_feature_id', 'key', 'title', 'description')
                    ->join('permissions_roles', 'permissions_roles.permission_group_id', 'permission_groups.id')
                    ->whereIn('role_id', $rolesIds)
                    ->where('key', 'like', 'ADMIN_%')
                    ->get();
            });
    }

    public function getJWTIdentifier(): string
    {
        return $this->getKey();
    }

    /**
     * Return a key value array, containing any custom claims to be added to the JWT.
     */
    public function getJWTCustomClaims(): array
    {
        $companyName = '';
        $companyIsVendor = false;
        $companyId = '';
        $companyProfileClaimerId = '';
        $companyAvatar = '';
        $companySubdomain = '';
        $companyFriendlyUrl = '';
        $companyType = null;
        $companyProfileType = null;
        $userCompanies = $this->allCompanies()->with('avatar', 'enumType', 'companyClaimers')->get();
        $company = $userCompanies->where('id', $this->company_id)->first() ?? $userCompanies->first();

        if ($company) {
            $companyName = $company->profile_company_friendly_name ?: $company->name;
            $company->load('enumType');
            $company->load('companyClaimers');
            $companyIsVendor = $company->enumType->type_is_of_vendor;
            $companyId = '' . $company->id;
            $companyFriendlyUrl = $company->friendly_url;
            $companySubdomain = $company->subdomain;
            $companyPartnerFlag = $company->partner_flag;
            $companyType = $company->enumType;
            $companyProfileType = $company->companyProfileType;
            $companyAvatar = ImageService::findAvatarInDB($company, ProfileImageType::CompanyAvatar);
            if ($company->companyClaimers->count() > 0) {
                $companyProfileClaimerId = '' . $company->companyClaimers->first()->user_id;
            }
        }

        $avatar = ImageService::findAvatarInDB($this, ProfileImageType::UserAvatar);
        $needToSetupProfile = false;

        if (empty($avatar) || empty($this->handle)) {
            $needToSetupProfile = true;
        }

        return [
            'id' => '' . $this->id,
            'firstName' => $this->first_name,
            'lastName' => $this->last_name,
            'email' => $this->email,
            'avatar' => $avatar,
            'authenticatedForInMinutes' => config('session.lifetime'),
            'twoFA' => $this->is_two_factor_confirmed,
            'company_display_name' => $companyName,
            'company_avatar' => $companyAvatar,
            'company_subdomain' => $companySubdomain,
            'company_partner_flag' => $companyPartnerFlag ?? false,
            'company_type' => $companyType,
            'company_profile_type' => $companyProfileType,
            'company_friendly_url' => $companyFriendlyUrl,
            'type_is_of_vendor' => $companyIsVendor,
            'company_id' => $companyId,
            'company_profile_claimer_id' => $companyProfileClaimerId,
            'handle' => $this->handle,
            'friendly_url' => $this->friendly_url,
            'need_to_setup_profile' => $needToSetupProfile,
            'registered_all_pitches' => $this->registered_all_pitches,
            'profile_type' => $this->userProfileType ? $this->userProfileType->value : null,
            'is_private' => $this->is_private,
            'status' => $this->status,
            'event_email_subscribed' => $this->event_email_subscribed,
        ];
    }

    /**
     * Calculated field, returns 2FA confirmation status.
     */
    public function getIsTwoFactorConfirmedAttribute(): bool
    {
        return !empty($this->two_factor_secret) && $this->two_factor_confirmed;
    }

    /**
     * Send a password reset notification to the user.
     *
     * @throws ValidationException
     */
    public function sendPasswordResetNotification($token): void
    {
        $redirect = request()?->has('redirect') ? '&redirect=' . urlencode(request()->redirect) : null;
        // this function is an override and gets called when PasswordResetLinkController.forgotPassword.sendResetLink is called
        $email = urlencode($this->email);
        $client = CompanyClient::with('client')->where('client_id', $this->company_id)->first();
        if (!$client) {
            $url = app('requestDomain') != '' ?
                CompanyClientService::getCustomerDomainUrl('app') . '/reset-password/?email=' . $email . '&token=' . $token . $redirect :
                config('app.fe_url') . '/reset-password/?email=' . $email . '&token=' . $token . $redirect;
        } else {
            $url = CompanyClientService::getCustomerDomainUrl($client->client->subdomain)
                . '/msp-client-reset/?email=' . $email . '&token=' . $token . $redirect;
        }
        $this->notify(new ResetPasswordNotification($url, app('requestDomain')));
    }

    /**
     * Send the email verification notification to the user.
     *
     * @param  null  $redirectUrl
     * @param  null  $pitchEventId
     */
    public function sendEmailVerificationNotification($redirectUrl = null, $pitchEventId = null): void
    {
        // this function is an override and gets called when RegisterUserController.registerUser.event is called
        $this->notify(new EmailConfirmNotification($this->email, $this->password, $redirectUrl, $pitchEventId));
    }

    /**
     * Send claimer notification to the user.
     */
    public function sendClaimerNotification(string $companyName): void
    {
        $this->notify(new ClaimerNotification($companyName));
    }

    /**
     * Send verification code.
     *
     * @throws ValidationException
     */
    public function sendVerificationCode(string $verificationMode): bool
    {
        switch ($verificationMode) {
            case VerificationMode::PHONE:
                PlivoService::sendSMS($this->mobile_phone, $this->verification_code);

                break;
            case VerificationMode::EMAIL:
                $this->notify(new VerificationCodeNotification($this->verification_code, app('requestDomain')));

                break;
            default:
                throw ValidationException::withMessages([
                    'ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::Logic not implemented for: ' . $verificationMode,
                ]);
        }

        return true;
    }

    /**
     * Send claimer notification to the user.
     */
    public function sendMSPClaimerNotification(string $companyName): void
    {
        $this->notify(new MSPClaimerNotification($companyName));
    }

    /**
     * Send the email success verified notification to the user.
     */
    public function sendEmailSuccessfullyVerifiedNotification(): void
    {
        // this function is an override and gets called when RegisterUserController.registerUser.event is called
        $featureFlag = FeatureFlag::where('name', 'CONFIRMED_EMAIL_NOTIFICATION')->first();
        if (empty($featureFlag)) {
            $featureFlag = FeatureFlag::create(['name' => 'CONFIRMED_EMAIL_NOTIFICATION', 'activated' => true]);
        }
        if ($featureFlag->activated) {
            $this->notify(new EmailConfirmedNotification(app('requestDomain')));
        }
    }

    /**
     * Send the MSP email success verified notification to the user.
     */
    public function sendMSPEmailSuccessfullyVerifiedNotification(): void
    {
        $featureFlag = FeatureFlag::where('name', 'CONFIRMED_MSP_EMAIL_NOTIFICATION')->first();
        if (empty($featureFlag)) {
            $featureFlag = FeatureFlag::create(['name' => 'CONFIRMED_MSP_EMAIL_NOTIFICATION', 'activated' => true]);
        }
        if ($featureFlag->activated) {
            $this->notify(new EmailRegisteredMSPNotification());
        }
    }

    /**
     * Send the Direct email success verified notification to the user.
     */
    public function sendDirectEmailSuccessfullyVerifiedNotification(): void
    {
        $featureFlag = FeatureFlag::where('name', FeatureFlagEnum::CONFIRMED_DIRECT_EMAIL_NOTIFICATION)->first();
        if (empty($featureFlag)) {
            $featureFlag = FeatureFlag::create(['name' => FeatureFlagEnum::CONFIRMED_DIRECT_EMAIL_NOTIFICATION, 'activated' => true]);
        }
        if ($featureFlag->activated) {
            $this->notify(new EmailRegisteredDirectNotification());
        }
    }

    /**
     * The event map for the model.
     *
     * @var array
     */
    protected $dispatchesEvents = [
        'updated' => UserUpdatedEvent::class,
        'saved' => UserUpdatedEvent::class,
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime', 'last_logged_in_at' => 'datetime',
        ];
    }

    public function avatar(): MorphOne
    {
        return $this->morphOne(Media::class, 'model')
            ->whereIn('collection_name', [config('custom.media_collections.avatar.user')]);
    }

    public function eventsAttendance(): BelongsToMany
    {
        return $this->belongsToMany(PitchEvent::class, 'pitch_events_attendants_users', 'user_id')
            ->withTimestamps();
    }

    public function usersFollowingMe(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'users_following_users', 'followed_user_id', 'follower_user_id')
            ->where('users.status', UserStatus::Active)
            ->withTimestamps();
    }

    public function usersIFollow(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'users_following_users', 'follower_user_id', 'followed_user_id')
            ->where('users.status', UserStatus::Active)
            ->withTimestamps();
    }

    public function companiesIFollow(): BelongsToMany
    {
        return $this->belongsToMany(Company::class, 'users_following_companies', 'follower_user_id', 'followed_company_id')
            ->withTimestamps();
    }

    public function profileType(): BelongsTo
    {
        return $this->userProfileType();
    }

    public function userProfileType(): BelongsTo
    {
        return $this->belongsTo(UserProfileType::class, 'user_profile_types_id', 'id');
    }

    public function profileVideos(): MorphMany
    {
        return $this->media()->where('collection_name', config('custom.media_collections.videos'));
    }

    public function myStack(): BelongsToMany
    {
        return $this->belongsToMany(Company::class, 'my_stack', 'user_id', 'stack_company_id')
            ->withTimestamps();
    }

    public function jobTitle(): BelongsTo
    {
        return $this->belongsTo(JobTitle::class, 'job_title_id', 'id');
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection(config('custom.media_collections.avatar.user'))->singleFile();
        $this->addMediaCollection(config('custom.media_collections.top_banner'))->singleFile();
    }

    public function scopeHasVideos($query, $type)
    {
        return $query->whereExists($type);
    }

    public function scopeIsVisibleOnVendorProfile($query)
    {
        return $query->where('show_on_vendor_related_section', true);
    }

    public function scopePublic($query)
    {
        return $query->where('is_private', false);
    }

    public function scopeStatusIs($query, $status)
    {
        return $query->where('status', $status);
    }

    public function hasRole(array $roleArray, ?string $asCompanyId = null): bool
    {
        foreach ($roleArray as $roleKey) {
            $userRoles = RoleService::getRolesByUserId($this->id, $asCompanyId);
            $hasRole = $userRoles->contains(function ($item) use ($roleKey) {
                return $item->key === $roleKey;
            });
            if ($hasRole) {
                return true;
            }
        }

        return false;
    }

    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'roles_users')->withTimestamps();
    }

    /**
     * Get all the user"s media galleries.
     */
    public function mediaGalleries(): MorphMany
    {
        return $this->morphMany(MediaGallery::class, 'model');
    }

    public function shoutOuts(): HasMany
    {
        return $this->hasMany(ShoutOut::class, 'subject_id', 'id');
    }

    public function blogs(bool $withRelations = true): HasMany
    {
        $query = $this->hasMany(Blog::class, 'subject_id', 'id');
        if ($withRelations) {
            $query->with('tags', 'categories', 'author');
        }

        return BlogService::prepareStatusQuery($query, $this);
    }

    public function publishedBlogs(): HasMany
    {
        return $this->hasMany(Blog::class, 'subject_id', 'id')
            ->with('tags', 'categories', 'author')
            ->where('status', BlogStatus::Published);
    }

    public function broadcastMessages(): HasMany
    {
        return $this->hasMany(BroadcastMessage::class, 'author_id', 'id');
    }

    public function reviews(): HasMany
    {
        return $this->hasMany(Review::class, 'reviewer_user_id', 'id');
    }

    public function likes(): HasMany
    {
        return $this->hasMany(Analytics::class, 'subject_id', 'id')
            ->where('action', AnalyticAction::like);
    }

    public function whatILike(): HasMany
    {
        return $this->hasMany(Analytics::class, 'author_id', 'id')
            ->where('action', AnalyticAction::like);
    }

    public function getCompleteNameAttribute(): string
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    public function getRegistrationStepAttribute(): int
    {
        $step = 1;
        if (($this->email_verified_at || $this->phone_verified_at) && $this->user_profile_types_id) {
            $step = 5;
        } elseif ($this->verification_code_verified) {
            $step = 3;
        } elseif ($this->verification_mode) {
            $step = 2;
        }

        return $step;
    }

    /**
     * Get basic user data and convert id to string as user_id
     */
    public function scopeBasic($query)
    {
        return $query->select(DB::raw("CONCAT('',id::STRING) AS user_id"), 'first_name', 'last_name', 'email');
    }

    /**
     * Checks if user is online and returns true
     */
    public function imOnline(): bool
    {
        $userIsActive = AdminUserNotificationService::getActiveUsers($this->id);

        return count($userIsActive) !== 0;
    }

    public function redFlaggedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'red_flagged_by');
    }

    public function allCompanies(): Builder
    {
        return Company::join('roles', 'roles.company_id', '=', 'companies.id')
            ->join('roles_users', 'roles_users.role_id', '=', 'roles.id')
            ->where('roles_users.user_id', $this->id)
            ->select('companies.*')
            ->distinct();
    }
}
