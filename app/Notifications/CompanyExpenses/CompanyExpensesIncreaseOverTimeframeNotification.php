<?php

namespace App\Notifications\CompanyExpenses;

use App\Enums\AppConfigEnum;
use App\Enums\Email\EmailBladeFiles;
use App\Enums\Email\EmailType;
use App\Enums\MailGunTags;
use App\Notifications\Messages\CustomMailMessage;
use App\Services\AppConfig;
use App\Services\AuthService;
use App\Services\Company\CompanyClientService;
use App\Services\Email\EmailService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;
use stdClass;

class CompanyExpensesIncreaseOverTimeframeNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(
        protected bool $useBetterTrackerConfig,
        protected string $asCompanyId,
        protected string $increasedValue,
        protected string $increasedCategoryAndValue,
        protected ?StdClass $totalExpensesSummaryInfo,
        protected ?array $largestExpenses,
        protected string $period,
        public ?string $email,
        public ?array $dates
    ) {}

    /**
     * Get the notification's delivery channels.
     */
    public function via(): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     *
     * @throws \Exception
     */
    public function toMail($notifiable): MailMessage
    {
        $source = __CLASS__ . '::' . __FUNCTION__;
        Log::debug($source . '::SENDING EMAIL');

        $databaseIds = isset($notifiable->id) ? [$notifiable->id] : [];

        Log::debug($source . '::DATABASE ID' . json_encode($databaseIds));

        $emailType = $this->useBetterTrackerConfig
            ? EmailType::COMPANY_EXPENSES_INCREASE_OVER_TIMEFRAME
            : EmailType::COMPANY_EXPENSES_INCREASE_OVER_TIMEFRAME_MSP;

        $emailDetails = AuthService::emailDetails($emailType);

        Log::debug($source . '::EMAIL DETAILS' . json_encode($emailDetails));

        $periodMessage = '';
        switch ($this->period) {
            case 'Day':
                $periodMessage .= '(Daily ';

                break;
            case 'Week':
                $periodMessage .= '(Weekly ';

                break;
            case 'Month':
                $periodMessage .= '(Monthly ';

                break;
            case 'Quarter':
                $periodMessage .= '(Quarterly ';

                break;
        }

        if ($this->period != 'Day') {
            $periodMessage .= '[';
        }
        $periodMessage .= $this->dates['previous_date_range']['start'];
        if ($this->period != 'Day') {
            $periodMessage .= ' / ';
            $periodMessage .= $this->dates['previous_date_range']['end'];
            $periodMessage .= ']';
        }
        $periodMessage .= ' - ';
        if ($this->period != 'Day') {
            $periodMessage .= '[';
        }
        $periodMessage .= $this->dates['current_date_range']['start'];
        if ($this->period != 'Day') {
            $periodMessage .= ' / ';
            $periodMessage .= $this->dates['current_date_range']['end'];
            $periodMessage .= ']';
        }
        $periodMessage .= ')';

        $emailParameters = [
            'increased_value' => $this->increasedValue,
            'increased_category_and_value' => $this->increasedCategoryAndValue == '$0.00' ? '' : 'Your most expensive category was ' . $this->increasedCategoryAndValue . '.',
            'period' => $periodMessage,
        ];

        if ($this->email && empty($databaseIds)) {
            $emailParameters['email'] = $this->email;
            $emailParameters['first_name'] = $this->email;
        }

        Log::debug($source . '::EMAIL PARAMETERS' . json_encode($emailParameters));

        $bladeFileName = $this->useBetterTrackerConfig
            ? EmailBladeFiles::COMPANY_EXPENSES_INCREASE_OVER_TIMEFRAME
            : EmailBladeFiles::COMPANY_EXPENSES_INCREASE_OVER_TIMEFRAME_MSP;

        $emailCustomData = EmailService::prepareEmailCustomData(
            $bladeFileName,
            $databaseIds,
            $emailParameters,
            $this->useBetterTrackerConfig ? $this->asCompanyId : null
        );

        Log::debug($source . '::EMAIL CUSTOM DATA' . json_encode($emailCustomData));

        $mailer = $this->useBetterTrackerConfig
            ? 'smtp_bettertracker'
            : 'smtp';
        $directSubdomain = AppConfig::loadAppConfigByKey(AppConfigEnum::DIRECT_SUBDOMAIN, 'app')->value;
        $buttonUrl = !empty($emailCustomData->subdomain)
            ? CompanyClientService::getCustomerDomainUrl($emailCustomData->subdomain) . '/msp-client-login?redirect_url=/company-expenses'
            : ($this->useBetterTrackerConfig
                ? CompanyClientService::getCustomerDomainUrl($directSubdomain) . '/login?redirect_url=/company-expenses'
                : config('app.fe_url') . '/login?redirect_url=/company-expenses'
            );
        $fromEmail = $this->useBetterTrackerConfig
            ? config('mail.mailers.smtp_bettertracker.from_address')
            : $emailDetails['fromEmail'];
        $fromEmailDisplayName = $this->useBetterTrackerConfig
            ? config('mail.mailers.smtp_bettertracker.from_name')
            : $emailDetails['fromDisplayName'];

        return (new CustomMailMessage())->mailer($mailer)
            ->markdown($emailDetails['view'], [
                'headerText' => $emailCustomData->header_text, 'headerImage' => $emailCustomData->header_image,
                'introText' => $emailCustomData->intro_text,
                'footerText' => $emailCustomData->footer_text, 'domain' => $emailDetails['domain'],
                'totalExpensesSummaryInfo' => $this->totalExpensesSummaryInfo,
                'largestExpenses' => $this->largestExpenses,
                'buttonUrl' => $buttonUrl,
            ])->subject($emailCustomData->subject)
            ->from($fromEmail, $fromEmailDisplayName)
            ->withSymfonyMessage(function ($message) {
                $headers = $message->getHeaders();
                $headers->addTextHeader('X-Mailgun-Tag', MailGunTags::COMPANY_EXPENSES);
            });
    }
}
