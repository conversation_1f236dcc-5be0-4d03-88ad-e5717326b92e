<?php

namespace App\Notifications;

use App\Enums\Company\CompanyType;
use App\Enums\Email\EmailBladeFiles;
use App\Enums\Email\EmailType;
use App\Notifications\Messages\CustomMailMessage;
use App\Services\AuthService;
use App\Services\Company\CompanyClientService;
use App\Services\Email\EmailService;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class EmailConfirmedNotification extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(protected string $domain = '') {}

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     *
     * @throws \Exception
     */
    public function toMail($notifiable): MailMessage
    {
        $emailDetails = AuthService::emailDetails(EmailType::ConfirmedEmail, $this->domain);
        $companyType = $notifiable->company->enumType;
        $url = '';
        $companyId = null;
        if ($companyType->type_is_of_vendor) {
            $emailDetails['view'] = 'mail.confirmedRegister';
            $emailBladeFile = EmailBladeFiles::ConfirmedEmail;
        } else if ($companyType->value === CompanyType::DIRECT) {
            $emailDetails['view'] = 'mail.bettertracker.confirmedRegisterDirect';
            $emailDetails['fromEmail'] = config('mail.mailers.smtp_bettertracker.from_address');
            $emailDetails['fromDisplayName'] = config('mail.mailers.smtp_bettertracker.from_name');
            $companyId = $notifiable->company->id;
            $emailBladeFile = EmailBladeFiles::ConfirmedEmailDirect;
            $url = CompanyClientService::getCustomerDomainUrl('app') . '/login';
        } else {
            $emailDetails['view'] = 'mail.confirmedRegisterMSP';
            $emailBladeFile = EmailBladeFiles::ConfirmedEmailMSP;
        }
        $emailCustomData = EmailService::prepareEmailCustomData(
            $emailBladeFile,
            [$notifiable->id],
            [
                'email' => $notifiable->email,
                'first_name' => $notifiable->first_name,
                'last_name' => $notifiable->last_name,
            ],
            $companyId,
            $this->domain
        );

        return (new CustomMailMessage())->mailer($emailDetails['mailer'])->markdown($emailDetails['view'], [
            'notifiable' => $notifiable,
            'headerText' => $emailCustomData->header_text,
            'headerImage' => $emailCustomData->header_image,
            'introText' => $emailCustomData->intro_text,
            'footerText' => $emailCustomData->footer_text, 'domain' => $emailDetails['domain'],
            'url' => $url,
        ])->subject($emailCustomData->subject)
            ->from($emailDetails['fromEmail'], $emailDetails['fromDisplayName']);
    }
}
