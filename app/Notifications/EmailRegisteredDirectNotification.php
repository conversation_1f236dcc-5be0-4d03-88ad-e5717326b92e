<?php

namespace App\Notifications;

use App\Enums\AppConfigEnum;
use App\Enums\Email\EmailBladeFiles;
use App\Enums\Email\EmailType;
use App\Notifications\Messages\CustomMailMessage;
use App\Services\AppConfig;
use App\Services\AuthService;
use App\Services\Company\CompanyClientService;
use App\Services\Email\EmailService;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Password;

class EmailRegisteredDirectNotification extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct() {}

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     *
     * @throws \Exception
     */
    public function toMail($notifiable): MailMessage
    {
        // Generate a password reset token for a given email
        $token = Password::createToken($notifiable);
        $bladeFileName = EmailBladeFiles::RegisteredEmailDirect;
        $directSubdomain = AppConfig::loadAppConfigByKey(AppConfigEnum::DIRECT_SUBDOMAIN, 'app')->value;
        $emailDetails = AuthService::emailDetails(EmailType::RegisteredEmailDirect, $directSubdomain);
        $emailParams = [
            'support_email' => AppConfig::loadAppConfigByKey(AppConfigEnum::DIRECT_SUPPORT_EMAIL)->value,
        ];
        $emailCustomData = EmailService::prepareEmailCustomData(
            $bladeFileName,
            [$notifiable->id],
            $emailParams,
            null, '', true
        );

        $buttonUrl = CompanyClientService::getCustomerDomainUrl($directSubdomain) . '/reset-password/?email=' . urlencode($notifiable->email) . '&token=' . $token;

        $markdownData = [
            'notifiable' => $notifiable,
            'headerText' => $emailCustomData->header_text,
            'headerImage' => $emailCustomData->header_image,
            'introText' => $emailCustomData->intro_text,
            'footerText' => $emailCustomData->footer_text,
            'domain' => $emailDetails['domain'],
            'url' => $buttonUrl,
        ];

        return (new CustomMailMessage())->mailer($emailDetails['mailer'])
            ->markdown($emailDetails['view'], $markdownData)
            ->subject($emailCustomData->subject)
            ->from($emailDetails['fromEmail'], $emailDetails['fromDisplayName']);
    }
}
