<?php

namespace App\Notifications\Invite;

use App\Enums\Email\EmailBladeFiles;
use App\Enums\Email\EmailType;
use App\Models\Company\Company;
use App\Models\Company\CompanyInvite;
use App\Models\User;
use App\Notifications\Messages\CustomMailMessage;
use App\Services\AuthService;
use App\Services\Company\CompanyClientService;
use App\Services\Email\EmailService;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class LocationInvitationNotification extends Notification
{
    use Queueable;

    protected User $loggedUser;
    protected Company $corporate;
    protected Company $client;
    protected CompanyInvite $invite;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(User $loggedUser, Company $corporate, Company $client, CompanyInvite $invite)
    {
        $this->loggedUser = $loggedUser;
        $this->corporate = $corporate;
        $this->client = $client;
        $this->invite = $invite;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     *
     * @throws \Exception
     */
    public function toMail($notifiable): MailMessage
    {
        $location = __CLASS__ . '::' . __FUNCTION__ . '::';
        Log::info($location . 'SENDING_AFFILIATE_INVITE_TO::' . $notifiable->routes['mail']);
        $emailBladeFile = EmailBladeFiles::MSPLocationInvite;
        $emailDetails = AuthService::emailDetails(EmailType::MSPLocationInvitation);
        $emailParameters = [
            'requester_first_name' => $this->loggedUser->first_name,
            'requester_last_name' => $this->loggedUser->last_name,
            'parent_company' => $this->corporate->name,
            'location_company' => $this->client->name,
            'email' => $notifiable->routes['mail'],
        ];
        $emailCustomData = EmailService::prepareEmailCustomData(
            $emailBladeFile,
            [],
            $emailParameters
        );

        return (new CustomMailMessage())->markdown($emailDetails['view'], [
            'companyName' => $this->client->name,
            'loginRoute' => $this->prepareLoginUrl(),
            'registerRoute' => $this->prepareRegisterUrl(),
            'headerText' => $emailCustomData->header_text, 'headerImage' => $emailCustomData->header_image,
            'introText' => $emailCustomData->intro_text,
            'footerText' => $emailCustomData->footer_text, 'domain' => $emailDetails['domain'],
        ])->subject($emailCustomData->subject)
            ->from($emailDetails['fromEmail'], $emailDetails['fromDisplayName']);
    }

    /**
     * This function generates the secure login url
     */
    private function prepareLoginUrl(): string
    {
        return config('app.fe_url') . '/login?signature='
            . CompanyClientService::encodeInviteSignature($this->invite->id);
    }

    /**
     * This function generates the secure register url
     */
    private function prepareRegisterUrl(): string
    {
        return config('app.fe_url') . '/register?signature='
            . CompanyClientService::encodeInviteSignature($this->invite->id);
    }
}
