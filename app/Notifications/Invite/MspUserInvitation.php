<?php

namespace App\Notifications\Invite;

use App\Enums\AppConfigEnum;
use App\Enums\Company\CompanyType;
use App\Enums\Email\EmailBladeFiles;
use App\Enums\Email\EmailType;
use App\Models\Company\Company;
use App\Models\Company\CompanyInvite;
use App\Models\User;
use App\Notifications\Messages\CustomMailMessage;
use App\Services\AppConfig;
use App\Services\AuthService;
use App\Services\Company\CompanyClientService;
use App\Services\Email\EmailService;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class MspUserInvitation extends Notification
{
    use Queueable;

    protected User $loggedUser;
    protected Company $company;
    protected CompanyInvite $companyInvite;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(User $loggedUser, Company $company, CompanyInvite $companyInvite)
    {
        $this->loggedUser = $loggedUser;
        $this->company = $company;
        $this->companyInvite = $companyInvite;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     *
     * @throws \Exception
     */
    public function toMail($notifiable): MailMessage
    {
        $location = __CLASS__ . '::' . __FUNCTION__ . '::';
        Log::info($location . 'SENDING_MSP_USER_INVITE_TO::' . $notifiable->routes['mail']);
        $emailBladeFile = EmailBladeFiles::MSPUserInvite;
        $emailDetails = AuthService::emailDetails(EmailType::MspUserInviteNotification);
        $emailParameters = [
            'requestor_first_name' => $this->loggedUser->first_name,
            'requestor_last_name' => $this->loggedUser->last_name,
            'corporate_name' => $this->company->name,
            'email' => $notifiable->routes['mail'],
        ];

        $useBetterTrackerConfig = in_array($this->company->enumType->value, [CompanyType::MSP_CLIENT, CompanyType::DIRECT]);

        if ($useBetterTrackerConfig) {
            $emailDetails['fromDisplayName'] = config('mail.mailers.smtp_bettertracker.from_name');
            $emailDetails['fromEmail'] = config('mail.mailers.smtp_bettertracker.from_address');
        }

        $emailCustomData = EmailService::prepareEmailCustomData($emailBladeFile, [], $emailParameters, null, '', $useBetterTrackerConfig);

        return (new CustomMailMessage())->markdown($emailDetails['view'], [
            'companyName' => $this->company->name,
            'route' => $this->prepareRegisterUrl($useBetterTrackerConfig),
            'headerText' => $emailCustomData->header_text, 'headerImage' => $emailCustomData->header_image,
            'introText' => $emailCustomData->intro_text,
            'footerText' => $emailCustomData->footer_text, 'domain' => $emailDetails['domain'],
        ])->subject($emailCustomData->subject)
            ->from($emailDetails['fromEmail'], $emailDetails['fromDisplayName']);
    }

    /**
     * This function generates the secure register url
     */
    private function prepareRegisterUrl($useBetterTrackerConfig = false): string
    {
        $directSubdomain = AppConfig::loadAppConfigByKey(AppConfigEnum::DIRECT_SUBDOMAIN, 'app')->value;
        $registerDomain = $useBetterTrackerConfig ? CompanyClientService::getCustomerDomainUrl($directSubdomain) : config('app.fe_url');

        return $registerDomain . '/register?signature=' .
            CompanyClientService::encodeInviteSignature($this->companyInvite->id);
    }
}
