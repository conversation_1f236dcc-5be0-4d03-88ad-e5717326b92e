<?php

namespace App\Observers;

use App\Enums\Company\CompanyProfileTypes;
use App\Enums\Company\CompanyType;
use App\Enums\DefaultEmail;
use App\Enums\MyStackPartnerStatus;
use App\Enums\Partner\PartnerPortalInvitationInitiator;
use App\Models\MSPFollowingPartner;
use App\Models\MyStack\MyStack;
use App\Notifications\MyStack\MyStackFreeVendorClaimerNotification;
use App\Notifications\MyStack\MyStackVendorNoPrmClaimerNotification;
use App\Notifications\MyStack\MyStackVendorPrmClaimerNotification;
use App\Services\AppConfig;
use App\Services\AuthService;
use App\Services\FeatureFlagService;
use App\Services\Partner\InvitePartnerService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

class MyStackCompanyObserver
{
    /**
     * Handle the created event.
     */
    public function created(MyStack $stack): void
    {
        Log::debug(__CLASS__ . '::' . __FUNCTION__ . ' - Entering method');
        $this->handleNotifications($stack);
    }

    /**
     * Handle the restored event.
     */
    public function restored(MyStack $stack): void
    {
        Log::debug(__CLASS__ . '::' . __FUNCTION__ . ' - Entering method');
        $this->handleNotifications($stack);
    }

    private function handleNotifications(MyStack $stack)
    {
        $stack->load([
            'company:id,type',
            'company.enumType:id,value',
        ]);
        // Not allowed for Direct and MSP Clients stack or recommended stack
        if ($stack->company->enumType->value === CompanyType::DIRECT || !empty($stack->parent_stack_id) || !empty($stack->is_recommended_stack)) {
            return;
        }
        $isAlreadyAddedToStack = MyStack::where('stack_company_id', $stack->stack_company_id)
            ->where('company_id', $stack->company_id)
            ->where('partner_status', $stack->partner_status)
            ->where('product_id', $stack->product_id)
            ->where('id', '<>', $stack->id)
            ->exists();
        if (empty($isAlreadyAddedToStack)) {
            Log::debug(__CLASS__ . '::' . __FUNCTION__ . '- Not added to stack');
            if ($stack->stackCompany?->companyProfileType->value === CompanyProfileTypes::VendorFree) {
                Log::debug(__CLASS__ . '::' . __FUNCTION__ . '- VENDOR FREE');
                $this->handleFreeVendorNotification($stack);

                return;
            }
            if ($stack->stackCompany->partner_flag) {
                Log::debug(__CLASS__ . '::' . __FUNCTION__ . '- PRM ENABLED');
                // Non free - PRM enabled
                $this->handleNotificationWithInvite(
                    $stack,
                    'MY_STACK_VENDOR_PRM_CLAIMER_NOTIFICATION',
                    new MyStackVendorPrmClaimerNotification($stack)
                );

                return;
            }

            Log::debug(__CLASS__ . '::' . __FUNCTION__ . '- PRM NOT ENABLED');
            // Non free - PRM Disabled
            $this->handleNotificationWithInvite(
                $stack,
                'MY_STACK_VENDOR_NOPRM_CLAIMER_NOTIFICATION',
                new MyStackVendorNoPrmClaimerNotification($stack)
            );
        }
    }

    private function handleNotificationWithInvite(MyStack $myStack, string $featureFlag, $notification)
    {
        $loggedUser = AuthService::getAuthUser();
        Log::debug(__CLASS__ . '::' . __FUNCTION__ . ' - Entering method ');
        $requestPending = MSPFollowingPartner::where('follower_partner_id', $myStack->company_id)
            ->where('followed_partner_id', $myStack->stack_company_id)
            ->whereNull('deleted_at')
            ->first();
        if (empty($requestPending) || $myStack->partner_status === MyStackPartnerStatus::lookingForInformation) {
            Log::debug(__CLASS__ . '::' . __FUNCTION__ . ' - Sending invite from ' . $myStack->company_id . ' to ' . $myStack->stackCompany->id);
            if (FeatureFlagService::findByName($featureFlag)->activated) {
                Notification::send($myStack->stackCompany->claimers, $notification);
            }
            InvitePartnerService::sendUsersInvite(
                $myStack->stackCompany,
                $myStack->company_id,
                PartnerPortalInvitationInitiator::User,
                $loggedUser?->id ?? $myStack->user_id,
                collect([$myStack->user])
            );

            return;
        }
        if (!empty($requestPending)) {
            Log::debug(__CLASS__ . '::' . __FUNCTION__ . ' - ABORTING REASON: Request already pending');
        } else {
            Log::debug(__CLASS__ . '::' . __FUNCTION__ . ' - ABORTING REASON: Partner status: ' . $myStack->partner_status);
        }
    }

    // send free vendor notification
    private function handleFreeVendorNotification(MyStack $myStack)
    {
        $companyClaimers = $myStack->stackCompany->claimers;
        if ($myStack->partner_status === MyStackPartnerStatus::currentPartner) {
            $isAlreadyAddedToStackWithStatus = MyStack::where('stack_company_id', $myStack->stack_company_id)
                ->where('company_id', $myStack->company_id)
                ->where('partner_status', $myStack->partner_status)
                ->where('id', '<>', $myStack->id)
                ->exists();
            if (!empty($isAlreadyAddedToStackWithStatus)) {
                return;
            }
        }

        if ($companyClaimers->count()) {
            if (FeatureFlagService::findByName('MY_STACK_FREE_VENDOR_CLAIMER_NOTIFICATION')->activated) {
                Notification::send($companyClaimers, new MyStackFreeVendorClaimerNotification($myStack));
            }
        } else {
            $salesEmail = AppConfig::loadAppConfigByKey('SALES_EMAIL', DefaultEmail::SALES_EMAIL)->value;
            if (!filter_var($salesEmail, FILTER_VALIDATE_EMAIL)) {
                $salesEmail = AppConfig::updateAppConfigByKey('SALES_EMAIL', DefaultEmail::SALES_EMAIL)->value;
            }
            if (FeatureFlagService::findByName('MY_STACK_FREE_VENDOR_CLAIMER_NOTIFICATION')->activated) {
                Notification::route('mail', $salesEmail)->notify(new MyStackFreeVendorClaimerNotification($myStack));
            }
        }
    }
}
