<?php

namespace App\Observers;

use App\Models\Permission\Permission;
use App\Services\Permission\PermissionService;

class PermissionObserver
{
    /**
     * Handle the permission "created" event.
     */
    public function created(Permission $permission): void
    {
        $this->updateCache($permission);
    }

    /**
     * Handle the permission "updated" event.
     */
    public function updated(Permission $permission): void
    {
        $this->updateCache($permission);
    }

    /**
     * Handle the permission "deleted" event.
     */
    public function deleted(Permission $permission): void
    {
        $this->updateCache($permission);
    }

    /**
     * Update company permissions in global cache.
     */
    private function updateCache(Permission $permission): void
    {
        PermissionService::updatePermissionInGlobalCache($permission);
    }
}
