<?php

namespace App\Observers;

use App\Models\Permission\Role\Role;
use App\Services\Permission\RoleService;

class RoleObserver
{
    /**
     * Handle the Role "created" event.
     */
    public function created(Role $role): void {}

    /**
     * Handle the Role "updated" event.
     */
    public function updated(Role $role): void
    {
        $this->updateCache($role);
    }

    /**
     * Handle the Role "deleted" event.
     */
    public function deleted(Role $role): void
    {
        $this->updateCache($role);
    }

    /**
     * Update company roles in global cache.
     */
    private function updateCache(Role $role): void
    {
        RoleService::refreshRoleInGlobalCache($role);
    }
}
