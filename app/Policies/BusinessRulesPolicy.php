<?php

namespace App\Policies;

use App\Helpers\RulesHelper;
use App\Log;
use App\Models\Blog\Blog;
use App\Models\Company\Company;
use App\Models\MediaGallery;
use App\Models\User;
use App\Services\AuthService;
use Illuminate\Auth\Access\HandlesAuthorization;
use Illuminate\Auth\Access\Response;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Illuminate\Validation\ValidationException;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class BusinessRulesPolicy
{
    use HandlesAuthorization;

    /**
     * Create a new policy instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the given post can be updated by the user.
     */
    public function postCompanyVideo(User $user, Company $company)
    {
        if (AuthService::userIsSuperAdmin($user)) {
            return Response::allow();
        }
        $ruleName = 'VIDEO_POSTING_MONTHLY_LIMIT';
        $ruleErrorMessage = config("profileRules.rule.{$ruleName}");
        $company->loadMissing('companyProfileType');
        $ruleValue = $company->getProfileRuleValue($ruleName);
        if ($ruleValue === null) {
            return Response::allow();
        }
        $monthStartDate = Carbon::now()->startOfMonth();
        $monthEndDate = Carbon::now()->endOfMonth();

        $query = Media::query()
            ->where('model_id', $company->id)
            ->where('model_type', Company::class)
            ->where('collection_name', config('custom.media_collections.videos'))
            ->where(function ($q) {
                $q->whereRaw('custom_properties->>\'is_partner_content\' = \'0\' OR custom_properties->>\'is_partner_content\' IS NULL');
            })
            ->where(function ($q) {
                $q->whereRaw('custom_properties->>\'is_active\' = \'true\' OR custom_properties->>\'is_active\' IS NULL');
            })
            ->whereRaw('custom_properties->>\'pitch_event_id\' IS NULL')
            ->whereBetween('created_at', [$monthStartDate, $monthEndDate]);

        return $company->validateRuleMaxValue($ruleName, $query->count())
            ? Response::allow()
            : Response::deny($ruleErrorMessage);
    }

    public function updateCompanyLogo(User $user, Company $company)
    {
        if (AuthService::userIsSuperAdmin($user)) {
            return Response::allow();
        }
        $ruleName = 'LOGO_EDITING';
        $ruleErrorMessage = config("profileRules.rule.{$ruleName}");
        $company->validateRuleBooleanValue($ruleName)
            ? Response::allow()
            : Response::deny($ruleErrorMessage);
    }

    public function updateCompanyBanner(User $user, Company $company)
    {
        if (AuthService::userIsSuperAdmin($user)) {
            return Response::allow();
        }
        $ruleName = 'BANNER_EDITING';
        $ruleErrorMessage = config("profileRules.rule.{$ruleName}");
        $company->validateRuleBooleanValue($ruleName)
            ? Response::allow()
            : Response::deny($ruleErrorMessage);
    }

    public function updateCompanyOverview(User $user, Company $company)
    {
        if (AuthService::userIsSuperAdmin($user)) {
            return Response::allow();
        }
        $ruleName = 'OVERVIEW_SECTION_EDITING';
        $ruleErrorMessage = config("profileRules.rule.{$ruleName}");
        $company->validateRuleBooleanValue($ruleName)
            ? Response::allow()
            : Response::deny($ruleErrorMessage);
    }

    public function updateCompanyName(User $user, Company $company)
    {
        if (AuthService::userIsSuperAdmin($user)) {
            return Response::allow();
        }
        $ruleName = 'NAME_EDITING';
        $ruleErrorMessage = config("profileRules.rule.{$ruleName}");
        $company->validateRuleBooleanValue($ruleName)
            ? Response::allow()
            : Response::deny($ruleErrorMessage);
    }

    public function updateCompanyHandle(User $user, Company $company)
    {
        if (AuthService::userIsSuperAdmin($user)) {
            return Response::allow();
        }
        $ruleName = 'HANDLE_EDITING';
        $ruleErrorMessage = config("profileRules.rule.{$ruleName}");
        $company->validateRuleBooleanValue($ruleName)
            ? Response::allow()
            : Response::deny($ruleErrorMessage);
    }

    public function updateCompanyContactInfo(User $user, Company $company)
    {
        if (AuthService::userIsSuperAdmin($user)) {
            return Response::allow();
        }
        $ruleName = 'CONTACT_INFO_EDITING';
        $ruleErrorMessage = config("profileRules.rule.{$ruleName}");
        $company->validateRuleBooleanValue($ruleName)
            ? Response::allow()
            : Response::deny($ruleErrorMessage);
    }

    public function addCompanyProduct(User $user, Company $company)
    {
        if (AuthService::userIsSuperAdmin($user)) {
            return Response::allow();
        }
        $ruleName = 'PRODUCT_LIMIT';
        $ruleErrorMessage = config("profileRules.rule.{$ruleName}");
        $company->loadMissing('products');
        $company->validateRuleMaxValue($ruleName, (float)$company->products->count())
            ? Response::allow()
            : Response::deny($ruleErrorMessage);
    }

    public function addCompanyProductCategory(User $user, Company $company, $categories)
    {
        if (AuthService::userIsSuperAdmin($user)) {
            return Response::allow();
        }
        $ruleName = 'PRODUCT_CATEGORIES_LIMIT';
        $ruleErrorMessage = config("profileRules.rule.{$ruleName}");
        $company->validateRuleMaxValue($ruleName, (float)count($categories))
            ? Response::allow()
            : Response::deny($ruleErrorMessage);
    }

    public function addCompanyProductSubCategory(User $user, Company $company, $subCategories)
    {
        if (AuthService::userIsSuperAdmin($user)) {
            return Response::allow();
        }
        $ruleName = 'PRODUCT_SUBCATEGORY_LIMIT';
        $ruleErrorMessage = config("profileRules.rule.{$ruleName}");
        $allowedProductSubCategoriesQuantity = $company->getProfileRuleValue($ruleName);
        $errorMessage = sprintf($ruleErrorMessage, $allowedProductSubCategoriesQuantity);

        return (count($subCategories) > (int)$allowedProductSubCategoriesQuantity) ?
            Response::deny($errorMessage) : Response::allow();
    }

    public function addCompanyVideoCategory(User $user, Company $company, $categories)
    {
        if (AuthService::userIsSuperAdmin($user)) {
            return Response::allow();
        }
        $ruleName = 'VIDEO_CATEGORIES_LIMIT';
        $ruleErrorMessage = config("profileRules.rule.{$ruleName}");
        $company->validateRuleMaxValue($ruleName, (float)count($categories))
            ? Response::allow()
            : Response::deny($ruleErrorMessage);
    }

    public function addCompanyVideoTag(User $user, Company $company, $tags)
    {
        if (AuthService::userIsSuperAdmin($user)) {
            return Response::allow();
        }
        $ruleName = 'VIDEO_TAGS_LIMIT';
        $ruleErrorMessage = config("profileRules.rule.{$ruleName}");
        $company->validateRuleMaxValue($ruleName, (float)count($tags))
            ? Response::allow()
            : Response::deny($ruleErrorMessage);
    }

    public function addCompanyProfileClaimer(User $user, Company $company)
    {
        if (AuthService::userIsSuperAdmin($user)) {
            return Response::allow();
        }
        $ruleName = 'CLAIMERS_LIMIT';
        $ruleErrorMessage = config("profileRules.rule.{$ruleName}");
        // needs to be a system admin or the current logged in user is a claimer on the current company
        $company->loadMissing(['claimers']);
        $company->validateRuleMaxValue($ruleName, (float)$company->claimers->count())
            ? Response::allow()
            : Response::deny($ruleErrorMessage);
    }

    public function addIndustryEvent(User $user, Model $model)
    {
        if (AuthService::userIsSuperAdmin($user)) {
            return Response::allow();
        }
        if (!($model instanceof Company)) {
            throw ValidationException::withMessages([
                'ERROR::BusinessRulesPolicy->addIndustryEvent::Logic not implemented for model type: ' . get_class($model),
            ]);
        }
        $ruleName = 'INDUSTRY_EVENT_LIMIT';
        $ruleErrorMessage = config("profileRules.rule.{$ruleName}");
        $model->loadMissing('industryEvents');
        $model->validateRuleMaxValue($ruleName, (float)$model->industryEvents->count())
            ? Response::allow()
            : Response::deny($ruleErrorMessage);
    }

    /**
     * Determine if the given post can be updated by the user.
     */
    public function postUserVideo(User $user, User $targetUser)
    {
        Log::debug('postUserVideo - Started check with BusinessRulesPolicy.php');
        Log::debug('postUserVideo - Checking the user is super admin');
        if (AuthService::userIsSuperAdmin($user)) {
            return Response::allow();
        }

        Log::debug('postUserVideo - Started checking VIDEO_POSTING_MONTHLY_LIMIT');
        $ruleName = 'VIDEO_POSTING_MONTHLY_LIMIT';
        $ruleErrorMessage = config("profileRules.rule.{$ruleName}");
        $targetUser->loadMissing('userProfileType');
        $userProfileTypeName = $targetUser->userProfileType->value;
        $ruleValue = RulesHelper::getProfileRuleValue($userProfileTypeName, $ruleName);
        if ($ruleValue === null) {
            return Response::allow();
        }

        Log::debug('postUserVideo - Started to find posted video count from db');
        $monthStartDate = Carbon::now()->startOfMonth();
        $monthEndDate = Carbon::now()->endOfMonth();

        $query = Media::query()
            ->where('model_id', $targetUser->id)
            ->where('model_type', User::class)
            ->where('collection_name', config('custom.media_collections.videos'))
            ->whereBetween('created_at', [$monthStartDate, $monthEndDate]);
        Log::debug('postUserVideo - Eneded to find posted video count from db');

        return ((float)$query->count() < $ruleValue) ?
            Response::allow() : Response::deny($ruleErrorMessage);
    }

    public function addPartnerPage(?User $user, Company $company)
    {
        $ruleErrorMessage = config('genericMessages.error.COMPANY_NOT_ALLOWED_TO_CREATE_PARTNER_PAGE');

        return $company->partner_flag ? Response::allow() : Response::deny($ruleErrorMessage);
    }

    public function addPartnerPageSection(User $user, Company $company)
    {
        return Response::allow();
    }

    public function updatePartnerPageSection(User $user, Company $company)
    {
        return Response::allow();
    }

    public function deletePartnerPageSection(User $user, Company $company)
    {
        return Response::allow();
    }

    public function addPartnerPageSectionContent(User $user, Company $company)
    {
        return Response::allow();
    }

    public function deletePartnerPageSectionContent(User $user, Company $company)
    {
        return Response::allow();
    }

    public function postPartnerPageVideo(User $user, Company $company)
    {
        return Response::allow();
    }

    public function postCompanyDocument(User $user, Company $company)
    {
        if (AuthService::userIsSuperAdmin($user)) {
            return Response::allow();
        }
        $ruleName = 'DOCUMENT_POSTING_MONTHLY_LIMIT';
        $ruleErrorMessage = config("profileRules.rule.{$ruleName}");
        $company->loadMissing('companyProfileType');
        $companyProfileTypeName = $company->companyProfileType->value;
        $ruleValue = RulesHelper::getProfileRuleValue($companyProfileTypeName, $ruleName);
        if ($ruleValue === null) {
            return Response::allow();
        }
        $monthStartDate = Carbon::now()->startOfMonth();
        $monthEndDate = Carbon::now()->endOfMonth();

        $query = Media::query()
            ->where('model_id', $company->id)
            ->where('model_type', Company::class)
            ->where('collection_name', config('custom.media_collections.documents'))
            ->where(function ($q) {
                $q->whereRaw('custom_properties->>\'is_partner_content\' = \'0\' OR custom_properties->>\'is_partner_content\' IS NULL');
            })
            ->where(function ($q) {
                $q->whereRaw('custom_properties->>\'is_active\' = \'true\' OR custom_properties->>\'is_active\' IS NULL');
            })
            ->whereRaw('custom_properties->>\'pitch_event_id\' IS NULL')
            ->whereBetween('created_at', [$monthStartDate, $monthEndDate]);

        return ((float)$query->count() < $ruleValue) ?
            Response::allow() : Response::deny($ruleErrorMessage);
    }

    public function postCompanyImage(User $user, Company $company)
    {
        if (AuthService::userIsSuperAdmin($user)) {
            return Response::allow();
        }
        $ruleName = 'IMAGE_POSTING_MONTHLY_LIMIT';
        $ruleErrorMessage = config("profileRules.rule.{$ruleName}");
        $company->loadMissing('companyProfileType');
        $companyProfileTypeName = $company->companyProfileType->value;
        $ruleValue = RulesHelper::getProfileRuleValue($companyProfileTypeName, $ruleName);
        if ($ruleValue === null) {
            return Response::allow();
        }
        $monthStartDate = Carbon::now()->startOfMonth();
        $monthEndDate = Carbon::now()->endOfMonth();

        $query = MediaGallery::query()
            ->where('model_id', $company->id)
            ->where('model_type', Company::class)
            ->where(function ($q) {
                $q->whereRaw('custom_properties->>\'is_partner_content\' = \'0\' OR custom_properties->>\'is_partner_content\' IS NULL');
            })
            ->where('is_active', true)
            ->whereBetween('created_at', [$monthStartDate, $monthEndDate]);

        return ((float)$query->count() < $ruleValue) ?
            Response::allow() : Response::deny($ruleErrorMessage);
    }

    public function postCompanyBlog(User $user, Company $company)
    {
        if (AuthService::userIsSuperAdmin($user)) {
            return Response::allow();
        }
        $ruleName = 'BLOG_POSTING_MONTHLY_LIMIT';
        $ruleErrorMessage = config("profileRules.rule.{$ruleName}");
        $company->loadMissing('companyProfileType');
        $companyProfileTypeName = $company->companyProfileType->value;
        $ruleValue = RulesHelper::getProfileRuleValue($companyProfileTypeName, $ruleName);
        if ($ruleValue === null) {
            return Response::allow();
        }
        $monthStartDate = Carbon::now()->startOfMonth();
        $monthEndDate = Carbon::now()->endOfMonth();

        $query = Blog::query()
            ->where('subject_id', $company->id)
            ->where('subject_type', Company::class)
            ->where(function ($q) {
                $q->where('is_partner_content', false)->orWhereNull('is_partner_content');
            })
            ->where('is_active', true)
            ->whereBetween('created_at', [$monthStartDate, $monthEndDate]);

        return ((float)$query->count() < $ruleValue) ?
            Response::allow() : Response::deny($ruleErrorMessage);
    }

    public function contracts(User $user, Company $company)
    {
        return $this->processContractRule($user, $company, 'CONTRACTS');
    }

    public function contractsReminders(User $user, Company $company)
    {
        return $this->processContractRule($user, $company, 'CONTRACTS_REMINDERS');
    }

    public function contractsCalendar(User $user, Company $company)
    {
        return $this->processContractRule($user, $company, 'CONTRACTS_CALENDAR');
    }

    public function contractsReporting(User $user, Company $company)
    {
        return $this->processContractRule($user, $company, 'CONTRACTS_REPORTING');
    }

    public function contractsExporting(User $user, Company $company)
    {
        return $this->processContractRule($user, $company, 'CONTRACTS_EXPORTING');
    }

    public function plaid(User $user, Company $company)
    {
        return $this->processContractRule($user, $company, 'PLAID');
    }

    private function processContractRule(User $user, Company $company, string $ruleName)
    {
        if (AuthService::userIsSuperAdmin($user)) {
            return Response::allow();
        }
        $ruleErrorMessage = config("profileRules.rule.{$ruleName}");
        $company->loadMissing('companyProfileType');
        $companyProfileTypeName = $company->companyProfileType->value;
        $ruleValue = RulesHelper::getProfileRuleValue($companyProfileTypeName, $ruleName);
        if ($ruleValue === null) {
            return Response::allow();
        }

        return RulesHelper::getProfileRuleValue($companyProfileTypeName, $ruleName) ?
            Response::allow() : Response::deny($ruleErrorMessage);
    }
}
