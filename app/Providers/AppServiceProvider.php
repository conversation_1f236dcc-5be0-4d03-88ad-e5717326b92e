<?php

namespace App\Providers;

use App\Database\PostgresCockroachConnection;
use App\Models\Category\Category;
use App\Models\Company\Company;
use App\Models\Company\CompanyConfigurations;
use App\Models\MSPFollowingPartner;
use App\Models\MyStack\MyStack;
use App\Models\Permission\Permission;
use App\Models\Permission\Role\Role;
use App\Observers\CategoryObserver;
use App\Observers\CompanyConfigurationsObserver;
use App\Observers\CompanyObserver;
use App\Observers\MediaObserver;
use App\Observers\MSPFollowingPartnerObserver;
use App\Observers\MyStackCompanyObserver;
use App\Observers\PermissionObserver;
use App\Observers\RoleObserver;
use App\Observers\ShortURLObserver;
use App\Services\Cyclr\CyclrService;
use AshAllenDesign\ShortURL\Models\ShortURL;
use Illuminate\Database\Connection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\ServiceProvider;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->setCustomResolverForPgsql();
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        JsonResource::withoutWrapping();
        ShortURL::observe(ShortURLObserver::class);
        Media::observe(MediaObserver::class);
        Category::observe(CategoryObserver::class);
        Company::observe(CompanyObserver::class);
        MSPFollowingPartner::observe(MSPFollowingPartnerObserver::class);
        MyStack::observe(MyStackCompanyObserver::class);
        CompanyConfigurations::observe(CompanyConfigurationsObserver::class);
        Role::observe(RoleObserver::class);
        Permission::observe(PermissionObserver::class);

        $this->registerHttpMacros();

        if (app()->environment('local')) {
            if (class_exists(\Laravel\Telescope\TelescopeServiceProvider::class)) {
                $this->app->register(\Laravel\Telescope\TelescopeServiceProvider::class);
                $this->app->register(TelescopeServiceProvider::class);
            }
        }
    }

    private function setCustomResolverForPgsql()
    {
        Connection::resolverFor('pgsql', function ($connection, $database, $prefix, $config) {
            return new PostgresCockroachConnection($connection, $database, $prefix, $config);
        });
    }

    /**
     * @link https://laravel.com/docs/11.x/http-client#macros
     */
    private function registerHttpMacros(): void
    {
        $cyclrBaseUrl = config('cyclr.API_BASE_URL');

        Http::macro('cyclrApi', function () use ($cyclrBaseUrl) {
            return Http::baseUrl($cyclrBaseUrl)
                ->connectTimeout(30);
        });

        Http::macro('cyclrApiWithToken', function () use ($cyclrBaseUrl) {
            return Http::withHeader('Authorization', 'Bearer ' . CyclrService::getToken())
                ->baseUrl($cyclrBaseUrl . '/' . config('cyclr.API_VERSION'))
                ->connectTimeout(30);
        });

        Http::macro('plaid', function () {
            return Http::baseUrl(config('plaid.base_url'))
                ->connectTimeout(30);
        });
    }
}
