<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidEmail implements ValidationRule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct() {}

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Check if the value is a valid email format
        if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
            $fail('The :attribute must be a valid email address.');

            return;
        }

        // Check if the email matches the specific pattern
        // This pattern ensures: <EMAIL> (with domain at least 2 chars)
        if (!preg_match('/^[^@]+@[^@]+\.[a-z]{2,}$/i', $value)) {
            $fail('The :attribute format is invalid. Please use a standard email format.');

            return;
        }
    }
}
