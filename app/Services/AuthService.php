<?php

namespace App\Services;

use App\Enums\Cache\CacheTTLEnum;
use App\Enums\Email\EmailType;
use App\Enums\RoleKeys;
use App\Helpers\PermissionsHelper;
use App\Models\Company\Company;
use App\Models\Company\CompanyClient;
use App\Models\Permission\Group\PermissionGroup;
use App\Models\Permission\Group\PermissionGroupPermission;
use App\Models\Permission\Permission;
use App\Models\User;
use App\Services\Permission\RoleService;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Auth\StatefulGuard;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Validation\ValidationException;

class AuthService
{
    public static function getAuthGuard(): Guard|StatefulGuard
    {
        return Auth::guard('api');
    }

    public static function getAuthUser(): null|Authenticatable|User
    {
        return self::getAuthGuard()->user();
    }

    public static function getLoggedInUserId(): mixed
    {
        return self::getAuthUser() ? self::getAuthUser()?->getAuthIdentifier() : null;
    }

    /**
     * This method receives the permissionId, userId and companyIds and validates if the user has a role for the
     * companies that can use the permission (API route), throws an exception if the user has no permission to it.
     *
     * @throws ValidationException
     */
    public static function validatePermission(
        string $permissionId,
        string $userId,
        array $companyIds
    ): void {
        $cacheKey = "user_permissions_count_{$userId}_{$permissionId}";
        $permissionsCount = Cache::remember($cacheKey, now()->addSeconds(CacheTTLEnum::CACHE_TTL_WEEK),
            function () use ($userId, $permissionId, $companyIds) {
                return PermissionGroupPermission::select('permission_group_permissions.id')
                    ->join('permissions_roles as pr', 'pr.permission_group_id', '=', 'permission_group_permissions.permission_group_id')
                    ->join('roles_users', 'roles_users.role_id', '=', 'pr.role_id')
                    ->join('roles', 'roles.id', '=', 'pr.role_id')
                    ->join('companies', 'companies.id', '=', 'roles.company_id')
                    ->where('permission_group_permissions.permission_id', $permissionId)
                    ->where('roles_users.user_id', $userId)
                    ->whereIn('roles.company_id', $companyIds)
                    ->count();
            });

        // Checking inherited permissions
        $hasAsInherited = self::hasInheritedPermissions($userId, [$permissionId]);

        if ($permissionsCount === 0 && !$hasAsInherited) {
            throw ValidationException::withMessages([
                'UNAUTHORIZED' => config('genericMessages.error.UNAUTHORIZED_PERMISSION'),
                'permission_id' => $permissionId,
                'user_id' => $userId,
                'company_id' => $companyIds[0],
            ]);
        }
    }

    /**
     * Checks if a user has inherited permissions for a different company type.
     *
     * This method determines whether a user has permissions inherited from a related
     * company type when accessing a resource. It verifies if the company in the route
     * parameters is different from the active company and checks for inherited permissions.
     *
     * @param  string  $userId  The ID of the user.
     * @param  array|null  $permissionsIDs  Optional list of permission IDs to check.
     * @return bool Returns true if the user has inherited permissions, otherwise false.
     *
     * @throws ValidationException
     */
    public static function hasInheritedPermissions(
        string $userId,
        ?array $permissionsIDs = []
    ): bool {
        $request = request();
        // Checking if the current company has a parent
        $asCompanyId = PermissionsHelper::getCompanyId($request);
        $companyParent = CompanyClient::firstWhere('client_id', $asCompanyId)?->company_id ?? null;
        $asCompanyId = $companyParent ?? $asCompanyId;
        $params = $request->route()->parameters();
        $hasPermission = false;
        // Searching for an instance of a Company as a request param
        foreach ($params as $key => $param) {
            if ($param instanceof Company) {
                // Checking if the routed company is the same as the active company
                if ($param->id == $asCompanyId) {
                    return false;
                }
                // Checking for inherited permissions for company type
                $cacheKey = "inherited_user_permissions_{$userId}_{$asCompanyId}";
                $hasPermission = Cache::remember($cacheKey, now()->addSeconds(CacheTTLEnum::CACHE_TTL_WEEK),
                    function () use ($asCompanyId, $userId, $permissionsIDs, $param) {
                        return Permission::query()
                            ->join('permission_group_permissions as pgp', 'pgp.permission_id', '=', 'permissions.id')
                            ->join('template_permission_roles as tpr', 'tpr.permission_group_id', '=', 'pgp.permission_group_id')
                            ->join('template_roles as tr', 'tr.id', '=', 'tpr.template_role_id')
                            ->join('template_roles_extensions as tre', function ($join) use ($param) {
                                $join->on('tre.extends_id', '=', 'tr.id')
                                    ->where('tre.company_type_id', '=', $param->type);
                            })
                            ->join('template_roles as tr2', 'tr2.id', '=', 'tre.template_role_id')
                            ->join('roles as r', function ($join) use ($asCompanyId) {
                                $join->on('r.template_role_id', '=', 'tr2.id')
                                    ->where('r.company_id', '=', $asCompanyId);
                            })
                            ->join('roles_users as ru', function ($join) use ($userId) {
                                $join->on('ru.role_id', '=', 'r.id')
                                    ->where('ru.user_id', '=', $userId);
                            })
                            ->when(!empty($permissionsIDs), function ($q) use ($permissionsIDs) {
                                $q->whereIn('permissions.id', $permissionsIDs);
                            })
                            ->exists();
                    });
            }

            return $hasPermission;
        }

        return false;
    }

    /**
     * Retrieves the inherited permission groups for a user based on their company type and associated company.
     *
     * This method checks for inherited permissions by querying the relevant template roles,
     * extensions, and user role associations to determine which permission groups the user
     * should have access to.
     *
     * @param  string  $userId  The ID of the user.
     * @param  string  $companyTypeId  The ID of the company type for inheritance check.
     * @param  string  $userCompanyId  The ID of the company the user belongs to.
     * @return Collection A collection of inherited permission groups.
     */
    public static function getInheritedPermissionGroups(
        string $userId,
        string $companyTypeId,
        string $userCompanyId
    ): Collection {
        // Checking for inherited permissions for company type
        $query = PermissionGroup::select('permission_groups.id', 'permission_groups.key')
            ->join('permission_group_permissions as pgp', 'pgp.permission_group_id', '=', 'permission_groups.id')
            ->join('template_permission_roles as tpr', 'tpr.permission_group_id', '=', 'pgp.permission_group_id')
            ->join('template_roles as tr', 'tr.id', '=', 'tpr.template_role_id')
            ->join('template_roles_extensions as tre', function ($join) use ($companyTypeId) {
                $join->on('tre.extends_id', '=', 'tr.id')
                    ->where('tre.company_type_id', '=', $companyTypeId);
            })
            ->join('template_roles as tr2', 'tr2.id', '=', 'tre.template_role_id')
            ->join('roles as r', function ($join) use ($userCompanyId) {
                $join->on('r.template_role_id', '=', 'tr2.id')
                    ->where('r.company_id', '=', $userCompanyId);
            })
            ->join('roles_users as ru', function ($join) use ($userId) {
                $join->on('ru.role_id', '=', 'r.id')
                    ->where('ru.user_id', '=', $userId);
            })->groupBy('permission_groups.id');

        return $query->get();
    }

    /**
     * This method will check if the user is a SUPER ADMIN, it will implement the singleton pattern. This way in the
     * same request if the method is called multiple times, it will go to the database to check the role just the first
     * time the method is used.
     *
     * @return true if it is super admin
     */
    public static function userIsSuperAdmin($user = null): bool
    {
        $isSuperAdmin = app('userIsSuperAdmin');

        if ($isSuperAdmin === null) {
            $superAdminRole = [RoleKeys::SUPERADMIN];
            $user = $user ?: self::getAuthUser();
            $isSuperAdmin = $user && $user->hasRole($superAdminRole);
            App::instance('userIsSuperAdmin', $isSuperAdmin);
        }

        return $isSuperAdmin;
    }

    /**
     * This method will check if any of the roles of the user for the company are ADMIN,
     * it will implement the singleton pattern. This way in the same request if the method is called multiple times,
     * it will go to the database to check the role just the first time the method is used.
     *
     * @return true if it is admin
     */
    public static function userIsAdmin(User $user): bool
    {
        $isAdmin = app('userIsAdmin');
        if ($isAdmin === null) {
            $isAdmin = RoleService::getRolesByUserId($user->id)
                ->contains(function ($item) {
                    return $item->is_admin;
                });
            App::instance('userIsAdmin', $isAdmin);
        }

        return $isAdmin;
    }

    /**
     * @throws ValidationException
     */
    public static function emailDetails(string $emailType, string $domain = ''): array
    {
        $fromEmail = config('custom.mail_from');
        $fromDisplayName = config('custom.mail_display_name');
        $mailer = 'smtp';
        if ($domain !== '') {
            $fromEmail = config('mail.mailers.smtp_bettertracker.from_address');
            $fromDisplayName = config('mail.mailers.smtp_bettertracker.from_name');
            $mailer = 'smtp_bettertracker';
            $domain .= '.';
        }

        $view = match ($emailType) {
            EmailType::AbandonedReviewsReminder => 'mail.abandonedReviewsReminder',
            EmailType::AffiliateLocationInvitation => 'mail.affiliateLocationInvitation',
            EmailType::MSPLocationInvitation => 'mail.mspLocationInvite',
            EmailType::AdminMSPNewClaimersResponsesNotification => 'mail.adminMSPNewClaimersResponsesNotification',
            EmailType::AttendantWelcomeNotification => 'mail.attendantWelcome',
            EmailType::ChangeEmail => 'mail.emailReset',
            EmailType::ChatNotificationMessages => 'mail.chatNotificationMessages',
            EmailType::ConfirmEmail => 'mail.confirmRegister',
            EmailType::MspClientInviteNotification => 'mail.mspClientInvite',
            EmailType::ConfirmedEmail,
            EmailType::ConfirmedEmailMSP,
            EmailType::MyStackVendorNoPrmClaimerNotification,
            EmailType::MyStackVendorPrmClaimerNotification => 'blade file selected in the notification file',
            EmailType::MinutesBeforePitchReminder => 'mail.minutesBeforePitchReminder',
            EmailType::PitchEventVendorWelcome => 'mail.pitchEventVendorWelcome',
            EmailType::ResetPassword => 'mail.forgotPassword',
            EmailType::ResetPasswordClient => 'mail.forgotPasswordClient',
            EmailType::RegisteredEmailMSP => 'mail.registeredMSP',
            EmailType::RegisteredEmailDirect => 'mail.bettertracker.registeredDirect',
            EmailType::ReviewApproved => 'mail.reviewApproved',
            EmailType::ReviewApprovedForClaimer => 'mail.reviewApprovedForClaimer',
            EmailType::VerificationCode => 'mail.verificationCode',
            EmailType::WelcomeClaimer => 'mail.welcomeClaimer',
            EmailType::WelcomeMSPClaimer => 'mail.welcomeMSPClaimer',
            EmailType::PartnerInvite => 'mail.partnerInvite',
            EmailType::PartnerRequested => 'mail.prmRequestsReminder',
            EmailType::VendorWeeklySummary => 'mail.vendorWeeklySummary',
            EmailType::VendorProductWeeklySummary => 'mail.vendorProductWeeklySummary',
            EmailType::EventApprovedNotification => 'mail.eventApprovedNotification',
            EmailType::UpcomingEventNotification => 'mail.upcomingEventNotification',
            EmailType::PendingInviteRequestNotification => 'mail.pendingInviteRequestNotification',
            EmailType::MyStackFreeVendorClaimerNotification => 'mail.myStackFreeVendorClaimerNotification',
            EmailType::MissingVendorDistributorNotification => 'mail.missingVendorDistributorNotification',
            EmailType::MissingProductOrVendorNotification => 'mail.missingProductOrVendorNotification',
            EmailType::MspUserInviteNotification => 'mail.mspUserInvite',
            EmailType::MspCustomerUserInviteNotification => 'mail.mspCustomerUserInvite',
            EmailType::NoActivityVendorNotification => 'mail.noActivityVendorNotification',
            EmailType::VendorPendingRequestNotification => 'mail.vendorPendingRequestNotification',
            EmailType::FreeVendorTrendingNotification => 'mail.freeVendorTrendingNotification',
            EmailType::MyStackBuildingReminder => 'mail.myStackBuildingReminder',
            EmailType::InviteToReviewEmail => 'mail.inviteToReviewEmail',
            EmailType::RecommendNaviStackEmail => 'mail.recommendNaviStackEmail',
            EmailType::FirstReviewApproved => 'mail.firstReviewApproved',
            EmailType::VendorContractRenewalReminder => 'mail.vendorContractRenewalReminder',
            EmailType::VendorContractRenewalReminderCustomer => 'mail.bettertracker.vendorContractRenewalReminderCustomer',
            EmailType::VendorUserInviteNotification => 'mail.vendorClientInvite',
            EmailType::VendorNoticeDeadlineApproaching => 'mail.vendorNoticeDeadlineApproaching',
            EmailType::VendorNoticeDeadlineApproachingCustomer => 'mail.bettertracker.vendorNoticeDeadlineApproachingCustomer',
            EmailType::VendorWeeklySummaryForVendor => 'mail.vendorWeeklySummaryForVendor',
            EmailType::NewMultiStackInvitation => 'mail.newMultiStackInvitation',
            EmailType::FreeVendorTopTrendingProduct => 'mail.freeVendorTopTrendingProduct',
            EmailType::DealSubmittedToVendor => 'mail.dealSubmittedToVendor',
            EmailType::DealSubmittedToFreeVendor => 'mail.dealSubmittedToFreeVendor',
            EmailType::DealSubmittedToVendorNoChannelCommand => 'mail.dealSubmittedToVendorNoChannelCommand',
            EmailType::DealAdittionalInfoRequested => 'mail.dealAdittionalInfoRequested',
            EmailType::DealAdittionalInfoProvided => 'mail.dealAdittionalInfoProvided',
            EmailType::DealApprovedByVendor => 'mail.dealApprovedByVendor',
            EmailType::DealRejectedByVendor => 'mail.dealRejectedByVendor',
            EmailType::DealValidityDateApproachingMSP => 'mail.dealValidityDateApproachingMSP',
            EmailType::DealValidityDateApproachingVendor => 'mail.dealValidityDateApproachingVendor',
            EmailType::ApprovedDealChanged => 'mail.approvedDealChanged',
            EmailType::DeclinedDealChanged => 'mail.declinedDealChanged',
            EmailType::CHANNEL_DEAL_APPROVED => 'mail.channelDealApproved',
            EmailType::CHANNEL_DEAL_SEND_LEADS => 'mail.channelDealSendLeads',
            EmailType::CHANNEL_DEAL_USER_INVITATION => 'mail.channelDealUserInvitation',
            EmailType::COMPANY_EXPENSES_UPCOMING_RECURRING_CHARGES => 'mail.bettertracker.companyExpensesUpcomingRecurringCharges',
            EmailType::COMPANY_EXPENSES_INCREASE_OVER_TIMEFRAME => 'mail.bettertracker.companyExpensesIncreaseOverTimeframe',
            EmailType::COMPANY_EXPENSES_DECREASE_OVER_TIMEFRAME => 'mail.bettertracker.companyExpensesDecreaseOverTimeframe',
            EmailType::COMPANY_EXPENSES_INDIVIDUAL_RECURRING_EXPENSE_INCREASE => 'mail.bettertracker.companyExpensesIndividualRecurringExpenseIncrease',
            EmailType::COMPANY_EXPENSES_INDIVIDUAL_RECURRING_EXPENSE_DECREASE => 'mail.bettertracker.companyExpensesIndividualRecurringExpenseDecrease',
            EmailType::COMPANY_EXPENSES_WEEKLY_SUMMARY => 'mail.bettertracker.companyExpensesWeeklySummary',
            EmailType::COMPANY_EXPENSES_MONTHLY_SUMMARY => 'mail.bettertracker.companyExpensesMonthlySummary',
            EmailType::COMPANY_EXPENSES_TIME_TO_REVIEW_YOUR_EXPENSES => 'mail.bettertracker.companyExpensesTimeToReviewYourExpenses',
            EmailType::COMPANY_EXPENSES_UPCOMING_RECURRING_CHARGES_MSP => 'mail.companyExpensesUpcomingRecurringChargesMSP',
            EmailType::COMPANY_EXPENSES_INCREASE_OVER_TIMEFRAME_MSP => 'mail.companyExpensesIncreaseOverTimeframeMSP',
            EmailType::COMPANY_EXPENSES_DECREASE_OVER_TIMEFRAME_MSP => 'mail.companyExpensesDecreaseOverTimeframeMSP',
            EmailType::COMPANY_EXPENSES_INDIVIDUAL_RECURRING_EXPENSE_INCREASE_MSP => 'mail.companyExpensesIndividualRecurringExpenseIncreaseMSP',
            EmailType::COMPANY_EXPENSES_INDIVIDUAL_RECURRING_EXPENSE_DECREASE_MSP => 'mail.companyExpensesIndividualRecurringExpenseDecreaseMSP',
            EmailType::COMPANY_EXPENSES_WEEKLY_SUMMARY_MSP => 'mail.companyExpensesWeeklySummaryMSP',
            EmailType::COMPANY_EXPENSES_MONTHLY_SUMMARY_MSP => 'mail.companyExpensesMonthlySummaryMSP',
            EmailType::COMPANY_EXPENSES_TIME_TO_REVIEW_YOUR_EXPENSES_MSP => 'mail.companyExpensesTimeToReviewYourExpensesMSP',
            EmailType::HAPPY_ANNIVERSARY_ONE_MONTH => 'mail.bettertracker.happyAnniversaryOneMonth',
            EmailType::HAPPY_ANNIVERSARY_SIX_MONTHS => 'mail.bettertracker.happyAnniversarySixMonths',
            EmailType::HAPPY_ANNIVERSARY_ONE_YEAR => 'mail.bettertracker.happyAnniversaryOneYear',
            default => throw ValidationException::withMessages([
                'ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::Logic not implemented for email type: ' . $emailType,
            ]),
        };

        return [
            'domain' => $domain,
            'mailer' => $mailer,
            'view' => $view,
            'fromEmail' => $fromEmail,
            'fromDisplayName' => $fromDisplayName,
        ];
    }

    /**
     * @return true if user has a permission group
     */
    public static function hasCpPermissionGroup(string $permissionGroup): bool
    {
        $loggedInUser = self::getAuthUser();

        return $loggedInUser?->permissionGroups(config('custom.channel_program_company.id'))->pluck('key')->contains($permissionGroup);
    }
}
