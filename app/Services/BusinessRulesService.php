<?php

namespace App\Services;

use App\Enums\ProfileRuleValueType;
use App\Helpers\RulesHelper;
use App\Models\Company\Company;
use App\Models\Profile\ProfileRule;
use App\Models\Profile\ProfileRuleValue;
use Carbon\Exceptions\InvalidFormatException;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\QueryException;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BusinessRulesService
{
    public static function validateProfileRuleValueDoesNotExists(
        string $userProfileTypeId,
        string $companyProfileTypeId,
        string $ruleId
    ): void {
        $profileRuleValueQuery = ProfileRuleValue::query();
        if ($userProfileTypeId) {
            $profileRuleValueQuery->where('user_profile_types_id', $userProfileTypeId);
        } else {
            $profileRuleValueQuery->where('company_profile_types_id', $companyProfileTypeId);
        }
        $profileRuleValueQuery->where('profile_rules_id', $ruleId);
        if ($profileRuleValueQuery->first()) {
            abort(422, config('genericMessages.error.PROFILE_RULE_ALREADY_EXISTS'));
        }
    }

    public static function getConfigRulesAndValues(): array
    {
        try {
            $companyRulesQuery = DB::table('profile_rules', 'pr')
                ->select(['cpt.label', 'cpt.value', 'pr.rule', 'pr.value_type', 'prv.rule_value', 'pr.description'])
                ->crossJoin('company_profile_types as cpt')
                ->leftJoin('profile_rule_values as prv', function ($q) {
                    $q->on('prv.profile_rules_id', '=', 'pr.id')
                        ->on('prv.company_profile_types_id', '=', 'cpt.id');
                });
            $userRulesQuery = DB::table('profile_rules', 'pr')
                ->select(['upt.label', 'upt.value', 'pr.rule', 'pr.value_type', 'prv.rule_value', 'pr.description'])
                ->crossJoin('user_profile_types as upt')
                ->leftJoin('profile_rule_values as prv', function ($q) {
                    $q->on('prv.profile_rules_id', '=', 'pr.id')
                        ->on('prv.user_profile_types_id', '=', 'upt.id');
                });
            $companyRulesQuery->union($userRulesQuery);
            $groups = $companyRulesQuery->get()->groupBy('value');
            $rules = [];
            foreach ($groups as $name => $group) {
                $mapped = $group->mapWithKeys(function ($config) {
                    if ($config && $config->rule_value !== null) {
                        return [
                            $config->rule => [
                                'value_type' => $config->value_type,
                                'rule_value' => $config->rule_value,
                                'rule_description' => $config->description,
                            ],
                        ];
                    }

                    return [];
                })->filter()->toArray();
                $rules[$name] = $mapped;
            }

            return $rules;
        } catch (QueryException $e) {
            Log::warning(config('genericMessages.error.PROFILE_RULES_TABLE_NOT_FOUND'));

            return [];
        }
    }

    public static function isValidRuleValueType(
        ProfileRule $profileRule,
        mixed $value
    ): void {
        $result = false;
        $type = ProfileRuleValueType::coerce($profileRule->value_type);

        switch ($type) {
            case ProfileRuleValueType::NumericType:
                $result = is_numeric($value);

                break;
            case ProfileRuleValueType::BooleanType:
                $result = self::isValidBoolean($value);

                break;
            case ProfileRuleValueType::StringType:
                $result = is_string($value);

                break;
            case ProfileRuleValueType::DatetimeType:
                $result = self::isValidDate($value);

                break;
            default:
                abort(428, 'Type does not exist in ProfileRuleValueType, received value::' . $type);
        }

        if (!$result) {
            abort(422, 'Rule value must be of type ' . $profileRule->value_type);
        }
    }

    public static function isValidBoolean($value): bool
    {
        if (is_bool($value)) {
            return true;
        }
        if ($value === 1 || $value === 0) {
            return true;
        }
        if (strtolower($value) === 'true' || strtolower($value) === 'false') {
            return true;
        }

        return false;
    }

    public static function isValidDate($value): bool
    {
        try {
            Carbon::parse($value);
        } catch (InvalidFormatException $e) {
            return false;
        }

        return true;
    }

    public static function addCompanyStackChartVisibility(
        Collection $companies
    ): void {
        $ruleName = 'STACKCHART_VISIBILITY';
        $companies->transform(function ($company) use ($ruleName) {
            if ($company instanceof Company) {
                $company->loadMissing('companyProfileType');
                $companyProfileTypeName = $company->companyProfileType->value;
                $ruleValue = RulesHelper::getProfileRuleValue($companyProfileTypeName, $ruleName);
                $company->stack_chart_visibility = $ruleValue;
            } else {
                $ruleValue = RulesHelper::getProfileRuleValue($company->company_profile_type, $ruleName);
                $company->stack_chart_visibility = $ruleValue;
            }
        });
    }
}
