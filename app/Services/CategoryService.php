<?php

namespace App\Services;

use App\Enums\AppConfigEnum;
use App\Helpers\UtilityHelper;
use App\Http\Requests\Category\CategoryShowAllRequest;
use App\Models\Category\Category;
use App\Models\Company\Company;
use App\Models\Company\CompanyCategory;
use App\Models\Company\CompanyType;
use App\Models\ProductCategory;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class CategoryService
{
    /**
     * Returns a Builder for global categories (categories without company type).
     */
    public static function getGlobalCategoriesQuery(Builder $query): Builder
    {
        return $query->whereDoesntHave('companyTypes');
    }

    /**
     * Build a query to retrieve main categories with product count.
     *
     * Applies an optional filter to exclude hidden categories
     * and includes the number of products associated with each category.
     * Results are ordered alphabetically by name (case insensitive).
     *
     * @param  bool  $is_hidden  Whether to include hidden categories (default is false).
     */
    public static function getMainCategoryQuery(
        bool $is_hidden = false
    ): Builder {
        return Category::withCount('productCategories as number_of_products')
            ->when(!$is_hidden, function ($query) {
                $query->where('is_hidden', false);
            })
            ->orderByRaw('UPPER(name)');
    }

    /**
     * Retrieve all company types that have associated categories.
     *
     * @param  array  $fields  Columns to select from the company_types table.
     */
    public static function getCategoriesCompanyTypes(
        array $fields = ['id', 'label', 'value']
    ): Collection {
        if (!in_array('id', $fields)) {
            $fields[] = 'id';
        }

        return CompanyType::whereHas('categories')
            ->select($fields)
            ->orderBy('label')
            ->get();
    }
    /**
     * Returns a Builder for categories of a specific company type.
     */
    public static function getParticularCompanyTypeQuery(Builder $query, string $companyTypeId): Builder
    {
        return $query->whereHas('companyTypes', function (Builder $query) use ($companyTypeId) {
            $query->where('company_type_id', $companyTypeId);
        });
    }

    /**
     * Applies a filter to the query for categories based on company type and behavior.
     */
    public static function applyCompanyTypeCategoriesFilter(
        Builder $query,
        ?Company $company = null,
        ?string $behavior = 'include'
    ): void {
        if (!$company) {
            $query->whereDoesntHave('companyTypes');

            return;
        }
        match ($behavior) {
            'restrict' => $query->whereHas('companyTypes', function ($q) use ($company) {
                $q->where('value', $company->enumType->value);
            }),
            'exclude' => $query->whereDoesntHave('companyTypes', function ($q) use ($company) {
                $q->where('value', $company->enumType->value);
            }),
            default => $query->where(function ($q) use ($company) {
                $q->whereHas('companyTypes', function ($q) use ($company) {
                    $q->where('value', $company->enumType->value);
                })->orWhereDoesntHave('companyTypes');
            })
        };
    }

    /**
     * Apply filters to the category query based on the request parameters.
     *
     * Filters by default categories, parent ID, company type, and section if provided.
     */
    public static function applyCategoryFilters(
        Builder $query,
        CategoryShowAllRequest $request,
        bool $defaultCategories
    ): void {
        $query->when($request->has('default_categories'), function ($q) use ($defaultCategories) {
            $q->where('default_my_stack_category', $defaultCategories);
        });

        $query->when($request->has('parent_id'), function ($q) use ($request) {
            $q->where('parent_id', $request->parent_id);
        });

        $query->when($request->has('company_type'), function ($q) use ($request) {
            CategoryService::getParticularCompanyTypeQuery($q, $request->company_type);
        });

        $query->when($request->has('section'), function ($q) use ($request) {
            $q->join('category_section_categories', 'category_section_categories.category_id', '=', 'categories.id')
                ->join('category_sections', 'category_sections.id', '=', 'category_sections.id')
                ->where('category_sections.section', $request->section);
        });
    }

    /**
     * Applies filter for MyStack categories to the query.
     *
     * @param  Builder  $query
     */
    public static function applyMyStackCategoriesFilter($query, bool $defaultCategories): void
    {
        $query->whereNull('parent_id')
            ->where('is_hidden', false)
            ->with(['subCategories' => function ($subQuery) use ($defaultCategories) {
                $subQuery->where('default_my_stack_category', $defaultCategories)
                    ->where('is_hidden', false);
            }])
            ->whereHas('subCategories', function ($subQuery) use ($defaultCategories) {
                $subQuery->where('default_my_stack_category', $defaultCategories)
                    ->where('is_hidden', false);
            });
    }

    /**
     * Validates if a category can be visible based on its parent visibility.
     *
     * @param  int  $parentId
     * @param  bool  $isHidden
     * @return void
     *
     * @throws ValidationException
     */
    public static function validateParentVisibility($parentId, $isHidden)
    {
        $parentCategory = Category::find($parentId);
        if ($parentCategory->is_hidden && !$isHidden) {
            throw ValidationException::withMessages(
                ['is_hidden' => config('genericMessages.error.CAN_NOT_BE_VISIBLE_WHEN_PARENT_IS_NOT_VISIBLE')]);
        }
    }

    /**
     * Validates if a category name already exists under the same parent and company type.
     *
     * @param  string  $name
     * @param  int|null  $parentId
     * @param  int|null  $id
     * @param  string|null  $companyTypeId
     * @return void
     *
     * @throws ValidationException
     */
    public static function validateNameExistence($name, $parentId = null, $id = null, $companyTypeId = null)
    {
        $existenceQuery = Category::whereRaw("lower(name) = '" . strtolower($name) . "'");
        if ($parentId) {
            $existenceQuery->where('parent_id', $parentId);
        } else {
            $existenceQuery->whereNull('parent_id');
        }
        if ($id) {
            $existenceQuery->where('id', '!=', $id);
        }
        if ($companyTypeId) {
            $existenceQuery->whereHas('companyTypes', function ($query) use ($companyTypeId) {
                $query->where('company_type_id', $companyTypeId);
            });
        } else {
            $existenceQuery->whereDoesntHave('companyTypes');
        }
        if ($existenceQuery->exists()) {
            throw ValidationException::withMessages(
                ['name' => config('genericMessages.error.CATEGORY_NAME_ALREADY_EXISTS')]
            );
        }
    }

    /**
     * Appends usage counts (products, vendors, videos) to each category in the collection.
     *
     * @param  \Illuminate\Support\Collection  $collection
     * @return \Illuminate\Support\Collection
     */
    public static function appendUsage($collection)
    {
        $collection = $collection->filter();
        if ($collection->count() === 0) {
            return $collection;
        }
        $modelIds = $collection->pluck('id');
        $productUsage = ProductCategory::whereIn('category_id', $modelIds)
            ->get()
            ->groupBy('category_id');
        $vendorCategoriesUsage = CompanyCategory::whereIn('category_id', $modelIds)
            ->get()
            ->groupBy('category_id');
        $videoUsage = self::getUsedByVideoList($modelIds);
        $collection->transform(function ($m) use ($productUsage, $vendorCategoriesUsage, $videoUsage) {
            if (!empty($m) && !empty($m->id)) {
                $productsUsageCount = !empty($productUsage->get($m->id)) ? $productUsage->get($m->id)->count() : 0;
                $vendorsUsageCount = !empty($vendorCategoriesUsage->get($m->id)) ? $vendorCategoriesUsage->get($m->id)->count() : 0;
                $videoUsageCount = !empty($videoUsage->get($m->id)) ? $videoUsage->get($m->id)->count : 0;
                $totalUsage = $productsUsageCount + $vendorsUsageCount + $videoUsageCount;

                $m->product_usage_count = $productsUsageCount;
                $m->vendor_usage_count = $vendorsUsageCount;
                $m->video_usage_count = $videoUsageCount;
                $m->total_usage_count = $totalUsage;
            }

            return $m;
        });

        return $collection;
    }

    /**
     * Appends usage counts to a single category collection.
     *
     * @param  \Illuminate\Support\Collection  $collection
     * @return object
     */
    public static function appendCategoryCount($collection)
    {
        $modelIds = $collection;
        $category_id = $modelIds['id'];
        $productUsage = ProductCategory::where('category_id', $category_id)
            ->get();
        $vendorCategoriesUsage = CompanyCategory::where('category_id', $category_id)
            ->get();
        $videoUsage = self::getUsedByVideoList(collect($category_id));
        $productsUsageCount = !empty($productUsage) ? $productUsage->count() : 0;
        $vendorsUsageCount = !empty($vendorCategoriesUsage) ? $vendorCategoriesUsage->count() : 0;
        $videoUsageCount = !empty($videoUsage->get($category_id)) ? $videoUsage->get($category_id)->count : 0;
        $totalUsage = $productsUsageCount + $vendorsUsageCount + $videoUsageCount;

        $collection->put('product_usage_count', $productsUsageCount);
        $collection->put('vendor_usage_count', $vendorsUsageCount);
        $collection->put('video_usage_count', $videoUsageCount);
        $collection->put('total_usage_count', $totalUsage);
        $appendCategoryCount = (object)$collection->toArray();

        return $appendCategoryCount;
    }

    /**
     * Returns a collection with video usage counts for the given category IDs.
     *
     * @param  \Illuminate\Support\Collection  $ids
     * @return \Illuminate\Support\Collection
     */
    public static function getUsedByVideoList($ids)
    {
        $whereIn = $ids->implode(',');
        $query = "SELECT categories.id::text as category_id, count(media.id) FROM media
            CROSS JOIN LATERAL jsonb_array_elements(custom_properties->'categories') as tag
            left join categories on tag::jsonb::text = concat('\"',categories.id::text,'\"')
            AND categories.id in ({$whereIn})
            group by category_id";

        return collect(DB::select($query))->keyBy('category_id');
    }

    /**
     * Returns the SQL query string for product categories usage.
     */
    public static function getProductsCategoriesUsageQuery(): string
    {
        return 'SELECT count(c.id) AS total_usage_count,
                         c.id           AS id,
                         c.parent_id    AS parent_id,
                         c.name         AS name,
                         c.color        AS color,
                         c.friendly_url AS friendly_url
                  FROM categories c
                           JOIN product_categories pc
                                ON c.id = pc.category_id
                  WHERE c.is_hidden = false
                    AND c.id IS NOT NULL
                  GROUP BY c.id, c.parent_id, c.name, c.color, friendly_url';
    }

    /**
     * Returns the SQL query string for company categories usage.
     */
    public static function getCompaniesCategoriesUsageQuery(): string
    {
        return 'SELECT count(c.id) AS total_usage_count,
                         c.id        AS id,
                         c.parent_id AS parent_id,
                         c.name      AS name,
                         c.color     AS color,
                         c.friendly_url AS friendly_url
                  FROM categories c
                           JOIN company_categories cc
                                ON c.id = cc.category_id
                  WHERE c.is_hidden = false
                    AND c.id IS NOT NULL
                  GROUP BY c.id, c.parent_id, c.name, c.color, friendly_url';
    }

    /**
     * Returns the SQL query string for media categories usage.
     */
    public static function getMediasCategoriesUsageQuery(): string
    {
        return "SELECT count(media.id)   AS total_usage_count,
                         c.id            AS id,
                         c.parent_id     AS parent_id,
                         c.name          AS name,
                         c.color         AS color,
                         c.friendly_url  AS friendly_url
                  FROM media
                           CROSS JOIN LATERAL jsonb_array_elements(custom_properties -> 'categories') AS category
                           LEFT JOIN categories c ON category::jsonb::TEXT = concat('\"', c.id::TEXT, '\"')
                  WHERE category::TEXT IS NOT NULL
                    AND c.id IS NOT NULL
                  GROUP BY c.id, c.parent_id, c.name, c.color, category::TEXT, c.name, friendly_url";
    }

    /**
     * Returns the top used categories based on total usage count.
     *
     * @param  int  $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getTopUsedCategories($limit)
    {
        $productsCategoryUsageQuery = self::getProductsCategoriesUsageQuery();
        $companiesCategoryUsageQuery = self::getCompaniesCategoriesUsageQuery();
        $mediasCategoryUsageQuery = self::getMediasCategoriesUsageQuery();
        $topCategoriesQuery = <<<EOT
            SELECT sum(total_usage_count) AS total_usage_count, id, parent_id, name, color, friendly_url
            FROM ({$productsCategoryUsageQuery}
                  UNION
                  {$companiesCategoryUsageQuery}
                  UNION
                  {$mediasCategoryUsageQuery})
            GROUP BY id, parent_id, name, color, friendly_url
            ORDER BY total_usage_count DESC
                LIMIT {$limit}
            EOT;

        return Category::hydrate(DB::select($topCategoriesQuery));
    }

    /**
     * Returns the top video used categories based on total usage count.
     *
     * @param  int  $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getTopVideoUsedCategories($limit)
    {
        $mediasCategoryUsageQuery = self::getMediasCategoriesUsageQuery();
        $topCategoriesQuery = <<<EOT
            SELECT sum(total_usage_count) AS total_usage_count, id, parent_id, name, color, friendly_url
            FROM ({$mediasCategoryUsageQuery})
            GROUP BY id, parent_id, name, color, friendly_url
            ORDER BY total_usage_count DESC
                LIMIT {$limit}
            EOT;

        return Category::hydrate(DB::select($topCategoriesQuery));
    }

    /**
     * Generates a unique friendly URL for a category.
     *
     * @throws Exception
     */
    public static function generateFriendlyUrl(Category $category): string
    {
        $friendlyUrl = Str::slug($category->name, '-');
        $exists = Category::where('friendly_url', $friendlyUrl)->exists();
        if ($exists) {
            if ($category->parent_id === null) {
                throw new Exception('Could not generate friendly url, category name is not unique and has no parent id');
            }
            $parentCategory = $category->parentCategory;
            $friendlyUrl = Str::slug(implode('-', [$parentCategory->name, $category->name]));
        }

        return $friendlyUrl;
    }

    /**
     * Returns a collection of popular categories within a date range.
     */
    public static function getPopularCategories(
        Collection $results,
        Carbon $startDate,
        Carbon $endDate,
        int $numOfCategories,
        array $ignoreIdsList = []
    ): Collection {
        if (UtilityHelper::getDaysBetweenDates(now(), $startDate) > (int)AppConfig::loadAppConfigByKey(
            AppConfigEnum::TOP_TRENDING_DAYS, config('common.topTrendingDays'))->value) {
            return $results;
        }
        $periodResult = self::preparePopulars($startDate, $endDate, $ignoreIdsList);
        $categoriesIds = [];
        if ($periodResult->count() > 0) {
            $parentCategoriesIds = $periodResult->pluck('parent_id')->unique()->toArray();
            $parents = Category::whereIn('id', $parentCategoriesIds)
                ->orderByRaw('LOWER(name)')->get();
            $categoriesIds = array_merge($periodResult->pluck('id')->toArray(), $parentCategoriesIds);
            $parents->transform(function ($item) use ($periodResult, $startDate, $endDate) {
                $children = $periodResult->where('parent_id', $item->id);
                $item->period = $startDate . ' - ' . $endDate;
                $item->number_of_products = $children->sum('number_of_products');
                $item->subCats = Category::where([
                    'parent_id' => $item->id,
                    'is_top_category' => true,
                ])
                    ->inRandomOrder()
                    ->take(config('common.popularSubCategories'))
                    ->get()?->sortBy('name');

                return $item;
            });
            $results = $results->merge($parents)
                ->filter(function ($item) {
                    return $item->subCats->count() > 0;
                })
                ->take($numOfCategories);
        }

        if ($results->count() < $numOfCategories) {
            $nextEndDate = new Carbon($startDate->subDay());
            $nextStartDate = new Carbon($startDate->subMonth());

            return self::getPopularCategories($results, $nextStartDate, $nextEndDate,
                $numOfCategories, array_merge($categoriesIds, $ignoreIdsList));
        }

        return $results;
    }

    /**
     * Prepares a collection of popular categories for a given period, excluding specified IDs.
     *
     * @param  string|Carbon  $startDate
     * @param  string|Carbon  $endDate
     * @param  array  $ignoreIdsList
     * @return \Illuminate\Support\Collection
     */
    private static function preparePopulars($startDate, $endDate, $ignoreIdsList)
    {
        return Category::select('categories.*', DB::raw('count(*) as number_of_products'))
            ->join('product_categories', 'product_categories.category_id', '=', 'categories.id')
            ->whereBetween('product_categories.created_at', [$startDate, $endDate])
            ->whereNotNull('categories.parent_id')
            ->whereNotIn('categories.id', $ignoreIdsList)
            ->whereNotIn('categories.parent_id', $ignoreIdsList)
            ->groupBy('categories.id')
            ->orderBy('number_of_products', 'DESC')
            ->get();
    }
}
