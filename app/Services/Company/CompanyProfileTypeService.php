<?php

namespace App\Services\Company;

use App\Enums\ActivityLogAction;
use App\Enums\Company\CompanyProfileTypes;
use App\Enums\IndustryEventStatus;
use App\Enums\ModelType;
use App\Helpers\RulesHelper;
use App\Models\Blog\Blog;
use App\Models\Company\Company;
use App\Models\IndustryEvent\IndustryEvent;
use App\Models\MediaGallery;
use App\Models\Profile\CompanyProfileType;
use App\Models\Subscription\CompaniesSubscriptionHistory;
use App\Services\ActivityLogs\ActivityLogsService;
use App\Services\AuthService;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class CompanyProfileTypeService
{
    /**
     * @return mixed
     */
    public static function getCompanyActiveSubscription(Company $company)
    {
        $selectFields = [
            'company_profile_types.label',
            'companies_subscription_history.started_at',
            'companies_subscription_history.ended_at',
            'companies_subscription_history.hubspot_deal_type',
        ];

        return DB::table('companies_subscription_history')
            ->join(
                'company_profile_types',
                'companies_subscription_history.company_profile_types_id',
                '=',
                'company_profile_types.id'
            )
            ->where('is_active', true)
            ->where('company_id', $company->id)
            ->orderBy('companies_subscription_history.started_at', 'desc')
            ->select($selectFields)
            ->first();
    }

    /**
     * @return Company $company
     *
     * @throws ValidationException
     */
    public static function changeSubscription(
        Company $company,
        CompanyProfileType $newCompanyProfileType,
        ?bool $updateClients = false
    ): Company {
        $company->loadMissing('companyProfileType');
        self::changeSubscriptionValidations($company, $newCompanyProfileType);
        $oldCompanyProfileType = $company->companyProfileType;
        self::logSubscriptionHistory($company, $newCompanyProfileType);
        $company->company_profile_types_id = $newCompanyProfileType->id;
        $company->save();
        // check the old subscription is VENDOR_FREE if yes then unhide company content
        if ($oldCompanyProfileType->value === CompanyProfileTypes::VendorFree) {
            self::showVendorFreeCompanyContent($company);
        }
        // check the new subscription is VENDOR_FREE if yes then hide company content
        if ($newCompanyProfileType->value === CompanyProfileTypes::VendorFree) {
            self::hideVendorFreeCompanyContent($company);
        }
        if ($updateClients && !$company->enumType->type_is_of_vendor) {
            $clientIds = $company->clients->pluck('id');
            Company::whereIn('id', $clientIds)->update([
                'company_profile_types_id' => $newCompanyProfileType->id,
            ]);
        }
        // commenting out for now. Krista and Erica don't know why we are doing this and it made a bug on prod. 9/20/23
        // self::updateCompanyIndustryEventsStatus($company, $newCompanyProfileType->id);

        return $company;
    }

    /**
     * @throws ValidationException
     */
    private static function logSubscriptionHistory(Company $company, CompanyProfileType $newCompanyProfileType): void
    {
        $now = Carbon::now();
        $actualSubscriptionHistory = CompaniesSubscriptionHistory::where(
            ['company_id' => $company->id, 'is_active' => true]
        )->first();
        $actualSubscriptionHistory?->update(['ended_at' => $now, 'is_active' => false]);

        $newSubscriptionHistory = CompaniesSubscriptionHistory::create([
            'company_id' => $company->id,
            'company_profile_types_id' => $newCompanyProfileType->id,
            'is_active' => true,
            'started_at' => $now,
        ]);

        ActivityLogsService::store(
            ActivityLogAction::editedPlan,
            ModelType::userType,
            AuthService::getLoggedInUserId(),
            ModelType::companies,
            $company->id,
            [
                'old_company_subscription_history_id' => '' . $actualSubscriptionHistory?->id,
                'new_company_subscription_history_id' => '' . $newSubscriptionHistory->id,
            ]
        );
    }

    /**
     * @throws ValidationException
     */
    private static function changeSubscriptionValidations(Company $company, CompanyProfileType $newCompanyProfileType): void
    {
        if ($newCompanyProfileType->id === $company->company_profile_types_id) {
            throw ValidationException::withMessages([
                'company_profile_type_id' => config('genericMessages.warning.CAN_NOT_SET_SAME_PROFILE_TYPE'),
            ]);
        }

        $company->loadMissing(['claimers', 'companyProfileType', 'products', 'products.categories']);
        $errors = [];
        $errors = self::claimersLimitValidation($errors, $company, $newCompanyProfileType);
        $errors = self::productsLimitValidation($errors, $company, $newCompanyProfileType);
        /**
         * Commenting out for now. Not needed as we are not limiting the number of categories/tags for videos and products
         *
         * $errors = self::productCategoriesLimitValidation($errors, $company, $newCompanyProfileType);
         * $errors = self::videoCategoriesLimitValidation($errors, $company, $newCompanyProfileType);
         * $errors = self::videoTagsLimitValidation($errors, $company, $newCompanyProfileType);
         */
        if (count($errors) > 0) {
            throw ValidationException::withMessages($errors);
        }
    }

    private static function claimersLimitValidation(array $errors, Company $company, CompanyProfileType $newCompanyProfileType)
    {
        $newCompanyProfileTypeName = $newCompanyProfileType->value;
        $allowedClaimersQuantity = RulesHelper::getProfileRuleValue(
            $newCompanyProfileTypeName,
            'CLAIMERS_LIMIT'
        );
        if ($allowedClaimersQuantity === null) {
            $errors[] = [
                'The CLAIMERS_LIMIT rule does not exists for the new plan (' . $newCompanyProfileType->label . '), please fix to continue.',
            ];
        }
        if ($company->claimers->count() > $allowedClaimersQuantity) {
            $errors[] = [
                'The business profile has '
                . $company->claimers->count() . ' users, must first remove '
                . ($company->claimers->count() - $allowedClaimersQuantity)
                . ' of them to be able to downgrade or remove the plan.',
            ];
        }

        return $errors;
    }

    private static function productsLimitValidation(array $errors, Company $company, CompanyProfileType $newCompanyProfileType)
    {
        $newCompanyProfileTypeName = $newCompanyProfileType->value;
        $allowedProductsQuantity = RulesHelper::getProfileRuleValue(
            $newCompanyProfileTypeName,
            'PRODUCT_LIMIT'
        );
        if ($allowedProductsQuantity === null) {
            $errors[] = [
                'The PRODUCT_LIMIT rule does not exists for the new plan (' . $newCompanyProfileType->label . '), please fix to continue.',
            ];
        }
        if ($company->products->count() > $allowedProductsQuantity) {
            $errors[] = [
                'The business profile has '
                . $company->products->count() . ' products, must first remove '
                . ($company->products->count() - $allowedProductsQuantity)
                . ' of them to be able to downgrade or remove the plan.',
            ];
        }

        return $errors;
    }

    private static function productCategoriesLimitValidation(array $errors, Company $company, CompanyProfileType $newCompanyProfileType)
    {
        $newCompanyProfileTypeName = $newCompanyProfileType->value;
        $allowedProductsCategoriesQuantity = RulesHelper::getProfileRuleValue(
            $newCompanyProfileTypeName,
            'PRODUCT_CATEGORIES_LIMIT'
        );
        if ($allowedProductsCategoriesQuantity === null) {
            $errors[] = [
                'The PRODUCT_CATEGORIES_LIMIT rule does not exists for the new plan (' . $newCompanyProfileType->label . '), please fix to continue.',
            ];
        }
        foreach ($company->products as $product) {
            if ($product->categories->count() > $allowedProductsCategoriesQuantity) {
                $errors[] = [
                    'The business profile product '
                    . '(' . $product->name . ' - ' . $product->id . ') has '
                    . $product->categories->count() . ' categories, must first remove '
                    . ($product->categories->count() - $allowedProductsCategoriesQuantity)
                    . ' of it to be able to downgrade or remove the plan.',
                ];
            }
        }

        return $errors;
    }

    private static function videoCategoriesLimitValidation(array $errors, Company $company, CompanyProfileType $newCompanyProfileType)
    {
        $newCompanyProfileTypeName = $newCompanyProfileType->value;
        $allowedVideosCategoriesQuantity = RulesHelper::getProfileRuleValue(
            $newCompanyProfileTypeName,
            'VIDEO_CATEGORIES_LIMIT'
        );
        if ($allowedVideosCategoriesQuantity === null) {
            $errors[] = [
                'The VIDEO_CATEGORIES_LIMIT rule does not exists for the new plan (' . $newCompanyProfileType->label . '), please fix to continue.',
            ];
        }
        foreach ($company->profileVideos as $video) {
            if (Arr::has($video->custom_properties, 'categories')) {
                try {
                    $categoriesCount = count(Arr::get($video->custom_properties, 'categories'));
                    $videoTitle = Arr::get($video->custom_properties, 'title', $video->name);
                    if ($categoriesCount > $allowedVideosCategoriesQuantity) {
                        $errors[] = [
                            'The business profile video '
                            . '(' . $videoTitle . ' - ' . $video->id . ') has '
                            . $categoriesCount . ' categories, must first remove '
                            . ($categoriesCount - $allowedVideosCategoriesQuantity)
                            . ' of it to be able to downgrade or remove the plan.',
                        ];
                    }
                } catch (\Exception $e) {
                    Log::error('Categories param of video corrupt : ' . $video->id);
                }
            }
        }

        return $errors;
    }

    private static function videoTagsLimitValidation(array $errors, Company $company, CompanyProfileType $newCompanyProfileType)
    {
        $newCompanyProfileTypeName = $newCompanyProfileType->value;
        $allowedVideosTagsQuantity = RulesHelper::getProfileRuleValue(
            $newCompanyProfileTypeName,
            'VIDEO_TAGS_LIMIT'
        );
        if ($allowedVideosTagsQuantity === null) {
            $errors[] = [
                'The VIDEO_TAGS_LIMIT rule does not exists for the new plan (' . $newCompanyProfileType->label . '), please fix to continue.',
            ];
        }
        foreach ($company->profileVideos as $video) {
            if (Arr::has($video->custom_properties, 'tags')) {
                try {
                    $tagsCount = count(Arr::get($video->custom_properties, 'tags'));
                    $videoTitle = Arr::get($video->custom_properties, 'title', $video->name);
                    if ($tagsCount > $allowedVideosTagsQuantity) {
                        $errors[] = [
                            'The business profile video '
                            . '(' . $videoTitle . ' - ' . $video->id . ') has '
                            . $tagsCount . ' tags, must first remove '
                            . ($tagsCount - $allowedVideosTagsQuantity)
                            . ' of it to be able to downgrade or remove the plan.',
                        ];
                    }
                } catch (\Exception $e) {
                    Log::error('Tags param of video corrupt : ' . $video->id);
                }
            }
        }

        return $errors;
    }

    private static function updateCompanyIndustryEventsStatus($company, $newCompanyProfileTypeId): void
    {
        $newCompanyProfileType = CompanyProfileType::findOrFail($newCompanyProfileTypeId);
        if ($newCompanyProfileType->value === CompanyProfileTypes::VendorFree) {
            $newStatus = IndustryEventStatus::free;
        } else {
            $newStatus = IndustryEventStatus::awaiting_approval;
        }
        IndustryEvent::where('subject_id', $company->id)
            ->whereNotIn('status', [IndustryEventStatus::rejected, IndustryEventStatus::archived])
            ->update(['status' => $newStatus]);
    }

    private static function hideVendorFreeCompanyContent(Company $company)
    {
        $companyVideosAndDocuments = $company->media()->whereIn(
            'collection_name',
            [
                config('custom.media_collections.videos'),
                config('custom.media_collections.documents'),
                config('custom.media_collections.brandable'),
            ]
        )
            ->where(function ($q) {
                $q->whereRaw('custom_properties->>\'is_partner_content\' = \'0\' OR custom_properties->>\'is_partner_content\' IS NULL');
            })
            ->get();

        // update custom_properties of videos and documents
        foreach ($companyVideosAndDocuments as $media) {
            $customProperties = $media->custom_properties;
            $customProperties['is_active'] = false;
            $media->custom_properties = $customProperties;
            $media->save();
        }
        $company->mediaGalleries()->update(['is_active' => false]);
        $company->blogs()->update(['is_active' => false]);
    }

    // show vendor free company content
    private static function showVendorFreeCompanyContent(Company $company)
    {
        $companyVideosAndDocuments = $company->media()->whereIn(
            'collection_name',
            [
                config('custom.media_collections.videos'),
                config('custom.media_collections.documents'),
                config('custom.media_collections.brandable'),
            ]
        )->get();
        // update custom_properties of videos and documents
        foreach ($companyVideosAndDocuments as $media) {
            $customProperties = $media->custom_properties;
            $customProperties['is_active'] = true;
            $media->custom_properties = $customProperties;
            $media->save();
        }
        MediaGallery::where([
            'model_type' => Company::class,
            'model_id' => $company->id,
        ])->update(['is_active' => true]);

        Blog::where([
            'subject_type' => Company::class,
            'subject_id' => $company->id,
        ])->update(['is_active' => true]);
    }
}
