<?php

namespace App\Services\Company;

use App\Enums\AnalyticAction;
use App\Enums\AppConfigEnum;
use App\Enums\BroadcastMessageType;
use App\Enums\Company\CompanyProfileTypes;
use App\Enums\Company\CompanyType as CompanyTypeEnum;
use App\Enums\PermissionGroups;
use App\Enums\PrmMediaVisibility;
use App\Enums\Review\ReviewStatus;
use App\Enums\StatusScope\StatusScopeEnum;
use App\Enums\UserStatus;
use App\Helpers\AppConfigurationHelper;
use App\Helpers\UtilityHelper;
use App\Http\Resources\Register\RegisterCompanyExistenceValidationResource;
use App\Jobs\SyncHubspotCompanyUrl;
use App\Models\Analytics\Analytics;
use App\Models\Category\Category;
use App\Models\Company\Company;
use App\Models\Company\CompanyClient;
use App\Models\Company\CompanyContact;
use App\Models\Company\CompanyInvite;
use App\Models\Company\CompanySocialMedia;
use App\Models\Company\CompanyType;
use App\Models\Focuses\Focus;
use App\Models\Focuses\FocusCompanyValue;
use App\Models\MyStack\MyStack;
use App\Models\Permission\Role\PermissionRole;
use App\Models\Permission\Role\Role;
use App\Models\Profile\CompanyProfileType;
use App\Models\ProfileEnrichment\ProfileEnrichmentQuestion;
use App\Models\User;
use App\Services\Analytics\AnalyticService;
use App\Services\AppConfig;
use App\Services\AuthService;
use App\Services\ImageService;
use App\Services\Permission\RoleUserService;
use App\Services\ProductService;
use App\Services\UserService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class CompanyService
{
    /**
     * Checks if provided company is Channel Program
     */
    public static function companyIsCP(Company $company): bool
    {
        return (string)$company->id === (string)config('custom.channel_program_company.id');
    }

    /**
     * @throws ValidationException
     */
    public static function companyIsOwner(Company $company, Media $media): void
    {
        $loggedUser = AuthService::getAuthUser();
        if (!$loggedUser) {
            throw ValidationException::withMessages(['message' => config('genericMessages.error.UNAUTHORIZED')]);
        }
        if (!AuthService::userIsSuperAdmin($loggedUser)) {
            self::validateUserIsFromCompany($loggedUser, $company);
        }

        if ((string)$company->id !== (string)$media->model_id) {
            throw ValidationException::withMessages(
                ['message' => config('genericMessages.error.MEDIA_DOES_NOT_BELONG_TO_REQUESTED_COMPANY')]
            );
        }
    }

    /**
     * Checks if provided company can manage clients
     *
     * /**
     * @throws ValidationException
     */
    public static function validateCompanyCanManageClients(?Company $company): void
    {
        if (!$company || !$company->manage_clients) {
            throw ValidationException::withMessages([
                'manage_clients' => config('genericMessages.error.COMPANY_NOT_ALLOWED_TO_HAVE_CLIENTS'),
            ]);
        }
    }

    /**
     * This function receives a company and a userId, if the user Id
     * is not in the company claimers throws an INVALID_CLAIMER_USER exception.
     *
     * @throws ValidationException
     */
    public static function validateUserIsCompanyClaimer(string $userId, Company $company): void
    {
        $companyClaimers = $company->companyClaimers;
        if (empty($companyClaimers) || !$companyClaimers->firstWhere('user_id', $userId)) {
            throw ValidationException::withMessages(
                ['message' => config('genericMessages.error.INVALID_CLAIMER_USER')]
            );
        }
    }

    /**
     * This function receives a user and a company and checks if the user is from that company
     * if not throws a USER_NOT_FROM_COMPANY exception.
     *
     * @throws ValidationException
     */
    public static function validateUserIsFromCompany(User $user, Company $company): void
    {
        if (AuthService::hasCpPermissionGroup(PermissionGroups::ADMIN_COMPANY_MNGMT_READ)) {
            return;
        }

        if (!$user->allCompanies()->where('companies.id', $company->id)->exists()) {
            throw ValidationException::withMessages(
                ['message' => config('genericMessages.error.USER_NOT_FROM_COMPANY')]
            );
        }
    }

    /**
     * This function receives a userId and a company, if the user Id
     * is not in the companyPartners array of the company throws an INVALID_PARTNER_USER
     * exception.
     *
     * @throws ValidationException
     */
    public static function validateUserIsCompanyPartner($userId, $company): void
    {
        if (AuthService::hasCpPermissionGroup(PermissionGroups::CHANNEL_COMMAND_READ)) {
            return;
        }

        $companyPartners = $company->usersFollowingMyPortal;
        if (!$userId || empty($companyPartners) || !$companyPartners->firstWhere('id', $userId)) {
            throw ValidationException::withMessages(
                ['message' => config('genericMessages.error.INVALID_PARTNER_USER')]
            );
        }
    }

    /**
     * Returns a query to retrieve companies by name and company type,
     * with an optional condition.
     *
     * @param  string  $companyName  The name of the company.
     * @param  string  $companyType  The type of the company.
     * @param  array|null  $optionalCondition  Additional conditions to apply to the query.
     * @return Builder The query builder instance.
     */
    public static function getCompanyQueryByNameAndCompanyType(
        string $companyName,
        string $companyType,
        ?array $optionalCondition = null
    ): Builder {
        $query = Company::with('enumType')
            ->whereHas('enumType', function ($query) use ($companyType) {
                $query->where('value', $companyType);
            })->where(DB::raw('lower(name)'), strtolower(trim($companyName)));
        if (!is_null($optionalCondition)) {
            $query->where($optionalCondition);
        }

        return $query;
    }

    /**
     * Retrieves a company by its name and type. Optionally creates the company if it does not exist.
     *
     * @param  string  $companyName  The name of the company.
     * @param  string  $companyType  The type of the company.
     * @param  bool|null  $createIfNotExist  Whether to create the company if it does not exist. Default is true.
     * @param  array|null  $optionalCondition  Additional optional conditions to filter the query. Default is null.
     * @param  string  $parentId  The ID of the parent company if needed.
     * @param  string  $affiliateBrandId  The ID of the affiliate brand if needed.
     * @return Company The retrieved company object or null if not found and $createIfNotExist is false.
     *
     * @throws ValidationException
     */
    public static function getCompany(
        string $companyName,
        string $companyType,
        ?bool $createIfNotExist = true,
        ?array $optionalCondition = null,
        ?string $parentId = null,
        ?string $affiliateBrandId = null
    ): Company {
        $query = self::getCompanyQueryByNameAndCompanyType(
            $companyName, $companyType, $optionalCondition
        );
        $company = $query->first();
        if (is_null($company) && $createIfNotExist) {
            $company = self::createCompany(
                $companyName,
                $companyType,
                $parentId,
                $affiliateBrandId
            );
        }

        return $company;
    }

    /**
     * Creates a new company based on the provided data
     *
     * @param  string  $name  The name of the company.
     * @param  string  $companyType  The type of the company.
     * @param  string  $parentId  The ID of the parent company if needed.
     * @param  string  $affiliateBrandId  The ID of the affiliate brand if needed.
     * @return Company The retrieved company object or null if not found and $createIfNotExist is false.
     *
     * @throws ValidationException
     */
    public static function createCompany(
        string $name,
        string $companyType,
        ?string $parentId = null,
        ?string $affiliateBrandId = null
    ): Company {
        $companyType = CompanyType::firstWhere('value', $companyType);
        // if parent id is set we need to heritage the company`s the parent configurations
        if ($parentId !== null) {
            $parentCompany = Company::select('manage_clients', 'company_profile_types_id')->find($parentId);
            $manageClients = $parentCompany->manage_clients;
            $defaultCompanyProfileTypeId = $parentCompany->company_profile_types_id;
        } else {
            $manageClients = false;
            $defaultCompanyProfileTypeId = $companyType->defaultCompanyProfileType->id;
        }

        return Company::create([
            'name' => $name,
            'company_type' => $companyType,
            'type' => $companyType->id,
            'parent_id' => $parentId,
            'affiliate_brand_id' => $affiliateBrandId,
            'company_profile_types_id' => $defaultCompanyProfileTypeId,
            'profile_vendor_handle' => UtilityHelper::generateUniqueWord(
                'companies',
                'profile_vendor_handle',
                UtilityHelper::generateUniqueWord(
                    'users',
                    'handle',
                    $name
                )
            ),
            'friendly_url' => UtilityHelper::generateUniqueWord(
                'companies',
                'friendly_url',
                UtilityHelper::generateUniqueWord(
                    'users',
                    'friendly_url',
                    $name
                )
            ),
            'subdomain' => UtilityHelper::generateUniqueWord(
                'companies',
                'subdomain',
                $name,
                ''
            ),
            'manage_clients' => $manageClients,
            'manage_affiliates' => false,
        ]);
    }

    /**
     * Creates or retrieves a company and creates products to it.
     *
     * If a company ID is provided, it retrieves the existing company; otherwise,
     * it creates a new one. The method then creates and associates products
     * with the company, setting their status to "pending".
     *
     * @param  Company  $owner  The company that owns the vendor.
     * @param  User  $loggedUser  The user performing the action.
     * @param  array  $data  The data containing company and product details.
     * @param  string|null  $companyType  The type of company to create (default: vendor).
     * @return array An array containing the vendor and associated products.
     *
     * @throws ValidationException If an error occurs during validation.
     */
    public static function createCompanyOrProduct(
        Company $owner,
        User $loggedUser,
        array $data,
        ?string $companyType = CompanyTypeEnum::VENDOR_ALL
    ): Company {
        $source = __CLASS__ . '::' . __FUNCTION__ . '::';
        DB::beginTransaction();

        try {
            // Adding or retrieving company
            $vendor = !empty($data['company_id'])
                ? Company::find($data['company_id'])
                : null;
            if (!$vendor) {
                $vendor = self::createCompany($data['company_name'], $companyType);
                $vendor->profile_company_website_url = $data['company_url'] ?? '';
                $vendor->save();
                // Setting company status as pending
                $vendor->setStatus(StatusScopeEnum::pending, $owner, $loggedUser);
            }
            // Adding products
            $productsIDs = collect([]);
            foreach ($data['products'] as $productData) {
                $productData['description'] = $productData['description'] ?? '';
                if (!empty($productData['category_id'])) {
                    $productData['product_categories_ids'] = [$productData['category_id']];
                } else {
                    $productData['product_categories_ids'] = [$productData['parent_category_id']];
                }
                unset($productData['category_id']);
                $product = ProductService::storeProduct($productData, $vendor, $loggedUser);
                $product->setStatus(StatusScopeEnum::pending, $owner, $loggedUser);
                $productsIDs->push($product->id);
            }
            DB::commit();

            $vendor->load([
                'enumType',
                'products' => function ($q) use ($productsIDs) {
                    $q->whereIn('products.id', $productsIDs);
                }]);

            return $vendor;
        } catch (ValidationException $e) {
            DB::rollBack();
            Log::error($source . $e->getMessage());

            throw $e;
        }
    }

    /**
     * This function receives a collection of companies and appends the vendor_contract_status depending on
     * if the company profile type is an MSP BUSINESS PREMIUM,
     */
    public static function appendVendorContractsStatus(Collection $companies): Collection
    {
        $companies = $companies->filter();
        if ($companies->count() === 0) {
            return $companies;
        }
        $company = $companies->first();
        if (!$company->relationLoaded('companyProfileType')) {
            // in case the relation has not been loaded to the items we do this logic to do a better performance
            $companyProfileIds = $companies->pluck('company_profile_types_id')->unique()->toArray();
            $companyProfileTypes = CompanyProfileType::whereIn('id', $companyProfileIds)->get();
            $companies->transform(function ($item) use ($companyProfileTypes) {
                $item->vendor_contract_status =
                    $companyProfileTypes->where('id', $item->company_profile_types_id)
                        ->first()?->value === CompanyProfileTypes::MSPBusinessPremium
                        ? 'Active'
                        : 'Inactive';

                return $item;
            });
        } else {
            $companies->transform(function ($item) {
                $item->vendor_contract_status =
                    $item->companyProfileType->value === CompanyProfileTypes::MSPBusinessPremium
                        ? 'Active'
                        : 'Inactive';

                return $item;
            });
        }

        return $companies;
    }

    /**
     * This function receives a company and appends the vendor_contract_status depending on
     * if the company profile type is an MSP BUSINESS PREMIUM,
     */
    public static function appendVendorContractStatus(Company $company): Company
    {
        $company->vendor_contract_status =
            $company->companyProfileType->value === CompanyProfileTypes::MSPBusinessPremium
                ? 'Active'
                : 'Inactive';

        return $company;
    }

    /**
     * This function receives a company ID and validates that the company is of vendor type
     * they are diferent.
     *
     * @param  $company  | can be a company object or a company ID
     *
     * @throws ValidationException
     */
    public static function validateCompanyIsVendor($company): Company
    {
        if (!$company instanceof Company) {
            $company = Company::findOrFail($company);
        }
        $company->load('enumType');
        if (!$company->enumType->type_is_of_vendor) {
            throw ValidationException::withMessages(
                ['message' => config('genericMessages.error.COMPANY_IS_NOT_OF_TYPE_VENDOR') . '::COMPANY_ID::' . $company->id]
            );
        }

        return $company;
    }

    /**
     * This function receives a company and validates that the company partner_flag is active
     */
    public static function validateCompanyPartnerPageIsActive(Company $company): void
    {
        if ($company->partner_flag === false) {
            abort(422, config('genericMessages.error.PARTNER_PAGE_NOT_ACTIVE') .
                '::COMPANY_ID::' . $company->id);
        }
    }

    /**
     * This function receives a company ID and validates that the company is of MSP type
     *
     * @param  $company  - can be a company object or a company ID
     *
     * @throws ValidationException
     */
    public static function validateCompanyIsMSP($company): Company
    {
        if (!$company instanceof Company) {
            $company = Company::findOrFail($company);
        }
        $company->load('enumType');
        if ($company->vendor) {
            throw ValidationException::withMessages(
                ['message' => config('genericMessages.error.COMPANY_IS_NOT_OF_TYPE_MSP') . '::COMPANY_ID::' . $company->id]
            );
        }

        return $company;
    }

    /**
     * This function receives a company ID and validates that the company is a valid AFFILIATE
     * must be MSP and have a parent
     *
     * @throws ValidationException
     */
    public static function validateCompanyIsAffiliate(Company $company): Company
    {
        self::validateCompanyIsMSP($company);
        if (is_null($company->parent_id)) {
            throw ValidationException::withMessages(
                ['message' => config('genericMessages.error.COMPANY_IS_NOT_VALID_AFFILIATE') . '::COMPANY_ID::' . $company->id]
            );
        }

        return $company;
    }

    /**
     * This function receives a company and validates that the company is a valid MSP_CLIENT
     *
     * @throws ValidationException
     */
    public static function validateCompanyIsClientMSP(Company $company): Company
    {
        $company->load('enumType:id,value,type_is_of_vendor');
        if ($company->enumType->value !== CompanyTypeEnum::MSP_CLIENT) {
            throw ValidationException::withMessages(
                ['message' => config('genericMessages.error.COMPANY_IS_NOT_OF_TYPE_MSP_CLIENT')]
            );
        }

        return $company;
    }

    /**
     * This function receives a company and validates that the company is not a location and is an MSP
     *
     * @param  $company  - can be a company object or a company ID
     *
     * @throws ValidationException
     */
    public static function validateCompanyIsNotLocation(Company $company): Company
    {
        self::validateCompanyIsMSP($company);
        if ($company->parent_id !== null) {
            throw ValidationException::withMessages(
                ['message' => config('genericMessages.error.COMPANY_CAN_NOT_BE_LOCATION') . '::COMPANY_ID::' . $company->id]
            );
        }

        return $company;
    }

    /**
     * This function calculates the brand stack adoption for a list of affiliates using the main company stack
     */
    public static function calculateBrandStackAdoption(Collection $recommendedMyStack, Collection $affiliates): Collection
    {
        if ($recommendedMyStack->count() > 0 && $affiliates->count() > 0) {
            $pivots = $recommendedMyStack->pluck('pivot');
            $mainStackCount = $recommendedMyStack->count();
            $affiliates->transform(function ($affiliate) use ($pivots, $mainStackCount) {
                $recommendedMyStackFindings = CompanyService::findBrandStackAdoptions($affiliate, $pivots);
                $stackCount = $recommendedMyStackFindings['found_products']->count()
                    + $recommendedMyStackFindings['found_categories']->count();
                $affiliate->navistack_progress = round(($stackCount) / $mainStackCount * 100, 2);

                return $affiliate;
            });
        }

        return $affiliates;
    }

    /**
     * This function calculates the brand stack adoption for a list of affiliates using the main company stack
     */
    public static function calculateBrandStackAdoptionWithProgress(
        Collection $recommendedMyStack, Collection $affiliates): Collection
    {
        $maxMyStackCategoryCount = AppConfigurationHelper::getValue('MAX_MY_STACK_CATEGORY_COUNT')
            ?? config('common.maxMyStackCategoryCount');
        if ($recommendedMyStack->count() > 0 && $affiliates->count() > 0) {
            $pivots = $recommendedMyStack->pluck('pivot');
            $affiliates->transform(function ($affiliate) use ($recommendedMyStack, $pivots, $maxMyStackCategoryCount) {
                $recommendedMyStackFindings = self::findBrandStackAdoptions($affiliate, $pivots);
                $stackCount = $recommendedMyStackFindings['found_products']->count() + $recommendedMyStackFindings['found_categories']->count();
                $affiliateBrandStackCalculation = $recommendedMyStack->count() > 0
                    ? round(($stackCount / $recommendedMyStack->count()) * 100) : 0;
                $affiliate->brand_stack = $affiliateBrandStackCalculation .
                    '% (' . $stackCount . '/' . $recommendedMyStack->count() . ')';
                $stackCategoryCount = $affiliate->myStack->pluck('pivot.category_id')->unique()->count();
                $affiliate->navistack_progress =
                    round($stackCategoryCount / $maxMyStackCategoryCount * 100, 2);

                return $affiliate;
            });
        }

        return $affiliates;
    }

    /**
     * Categorize found and missing recommended stack products and categories
     * for the given affiliate based on their stack and the provided pivots.
     *
     * @param  Company  $affiliate  The affiliate company.
     * @return array Returns an array with the found and missing products and categories:
     *               [
     *               'found_products' => Collection of found product pivots,
     *               'found_categories' => Collection of found category pivots,
     *               'missing_products' => Collection of missing product pivots,
     *               'missing_categories' => Collection of missing category pivots
     *               ]
     */
    public static function findBrandStackAdoptions(Company $affiliate, Collection $recommendedStacks): array
    {
        $foundProducts = collect();
        $missingProducts = collect();
        $foundCategories = collect();
        $missingCategories = collect();
        if ($affiliate->myStack->count() > 0) {
            $recommendedStacks->map(function ($recommendedStack) use (
                $affiliate, &$foundProducts, &$missingProducts, &$foundCategories, &$missingCategories
            ) {
                $affiliateStacks = $affiliate->myStack->pluck('pivot');
                if (is_null($recommendedStack->product_id)) {
                    $foundCategory = $affiliateStacks->where('category_id', $recommendedStack->category_id)->first();
                    if ($foundCategory) {
                        if (!$foundCategories->where('category_id', $recommendedStack->category_id)->first()) {
                            $foundCategories->push($recommendedStack);
                        }
                    } else if (!$missingCategories->where('category_id', $recommendedStack->category_id)->first()) {
                        $missingCategories->push($recommendedStack);
                    }
                } else {
                    $foundProduct = $affiliateStacks->where('category_id', $recommendedStack->category_id)
                        ->where('product_id', $recommendedStack->product_id)
                        ->first();
                    if ($foundProduct) {
                        if (!$foundProducts->contains($recommendedStack)) {
                            $foundProducts->push($recommendedStack);
                        }
                        if (!$foundCategories->where('category_id', $recommendedStack->category_id)->first()) {
                            $foundCategories->push($recommendedStack);
                        }
                    } else {
                        if (!$missingProducts->contains($recommendedStack)) {
                            $missingProducts->push($recommendedStack);
                        }
                        $foundCategory = $affiliateStacks->where('category_id', $recommendedStack->category_id)->first();
                        if ($foundCategory) {
                            if (!$foundCategories->where('category_id', $recommendedStack->category_id)->first()) {
                                $foundCategories->push($recommendedStack);
                            }
                        } else if (!$missingCategories->where('category_id', $recommendedStack->category_id)->first()) {
                            $missingCategories->push($recommendedStack);
                        }
                    }
                }
            });
        } else {
            $missingCategories = $recommendedStacks->filter(function ($item) {
                return $item['product_id'] === null;
            });
            $missingProducts = $recommendedStacks->filter(function ($item) {
                return $item['product_id'] !== null;
            });
        }

        return ['found_products' => $foundProducts, 'found_categories' => $foundCategories,
            'missing_products' => $missingProducts, 'missing_categories' => $missingCategories];
    }

    /**
     * @throws ValidationException
     */
    public static function validateUserIsAdminOrClaimer(Company|string $company, ?User $loggedUser = null): User
    {
        $tempUser = is_null($loggedUser) ? AuthService::getAuthUser() : $loggedUser;
        if (!$company instanceof Company) {
            $company = Company::findOrFail($company);
        }
        if (!UserService::validateUserIsAdmin($tempUser)) {
            self::validateUserIsCompanyClaimer($tempUser?->id, $company);
        }

        return $tempUser;
    }

    public static function findCompaniesByName($name)
    {
        $companies = collect();
        $compareName = strtolower(str_replace(' ', '', $name));
        $allCompanies = collect(
            DB::select("SELECT id, type, subdomain, LOWER(REPLACE(name, ' ', '')) as name FROM companies"));
        $tempCompanies = $allCompanies->filter(function ($tempCompany) use ($compareName) {
            return $tempCompany->name === $compareName;
        });
        if ($tempCompanies->count() > 0) {
            $companies = Company::with('enumType')
                ->whereIn('id', $tempCompanies->pluck('id'))->get();
        }

        return $companies;
    }

    public static function companyExistenceValidationByName($companyName)
    {
        $companies = Company::with('enumType')
            ->whereRaw('lower(companies.name) like lower(?)', ['%' . strtolower($companyName) . '%'])
            ->get();
        $companies = ImageService::appendCompanyAvatars($companies);

        return RegisterCompanyExistenceValidationResource::collection($companies);
    }

    public static function getSomeVendorsWithAvatar($quantity)
    {
        $loggedInUserId = AuthService::getLoggedInUserId() ?? 0;
        $rawUserIsFollowing = "exists(
             Select 1 from users_following_companies
             where follower_user_id={$loggedInUserId}
             and followed_company_id = companies.id
            ) as current_user_is_following";
        $fields = [
            'companies.*',
            DB::raw("'vendor' as profile_type"),
            'company_types.type_is_of_vendor',
            'profile_vendor_handle as handle',
            DB::raw($rawUserIsFollowing),
        ];
        $query = Company::select($fields)
            ->join('company_types', 'companies.type', '=', 'company_types.id')
            ->join('company_profile_types', 'companies.company_profile_types_id', '=', 'company_profile_types.id')
            ->where('company_types.type_is_of_vendor', true)
            ->whereHas('claimers')
            ->orderBy('company_profile_types.order', 'asc')
            ->orderBy('name', 'asc')
            ->withCount('usersFollowingMe')
            ->limit($quantity);
        $profiles = ImageService::appendCompanyAvatars($query->get());

        return AnalyticService::appendLikesCounts($profiles, UserService::getLoggedUserLikes());
    }

    public static function getPaginatedVendorsWithAvatar($request, $resultsPerPage)
    {
        $loggedInUserId = AuthService::getLoggedInUserId() ?? 0;
        $rawUserIsFollowing = "exists(
             Select 1 from users_following_companies
             where follower_user_id = {$loggedInUserId}
             and followed_company_id = companies.id
            ) as current_user_is_following";

        $fields = [
            'companies.*',
            DB::raw("'vendor' as profile_type"),
            'company_types.type_is_of_vendor',
            'profile_vendor_handle as handle',
            DB::raw($rawUserIsFollowing),
        ];
        $query = Company::select($fields)
            ->join('company_types', 'companies.type', '=', 'company_types.id')
            ->join('company_profile_types', 'companies.company_profile_types_id', '=', 'company_profile_types.id')
            ->withCount('usersFollowingMe')
            ->whereHas('claimers')
            ->where('company_types.type_is_of_vendor', true)
            ->orderBy('company_profile_types.order')
            ->orderBy('name');

        $searchWord = strtolower($request->search_word);
        if (!empty($searchWord)) {
            $query->where(function ($q) use ($searchWord) {
                $q->orWhereRaw("lower(companies.name) LIKE '%" . $searchWord . "%'");
                $q->orWhereRaw("lower(companies.profile_vendor_handle) LIKE '%" . $searchWord . "%'");
            });
        }

        $result = UtilityHelper::getSearchRequestQueryResults($request, $query, $resultsPerPage);

        if ($result instanceof LengthAwarePaginator) {
            $pageResults = $result->getCollection();
        } else {
            $pageResults = $result;
        }

        $pageResults = ImageService::appendCompanyAvatars($pageResults);

        if ($result instanceof LengthAwarePaginator) {
            $result->setCollection($pageResults);
        } else {
            $result = $pageResults;
        }

        return $result;
    }

    public static function appendProductsCategoriesToCollection($collection, $limit = '', $sortBy = '', $order = 'asc')
    {
        $CompanyIds = $collection->pluck('id');
        $categories = Category::select([DB::raw('distinct companies.id as company_id'), 'categories.*'])
            ->join('product_categories', 'categories.id', '=', 'product_categories.category_id')
            ->join('products', 'product_categories.product_id', '=', 'products.id')
            ->join('companies', 'products.company_id', '=', 'companies.id')
            ->whereIn('companies.id', $CompanyIds)
            ->when($sortBy, function ($query) use ($sortBy, $order) {
                $query->orderByRaw("categories.{$sortBy} {$order}");
            })
            ->get()
            ->groupBy('company_id');

        return $collection->transform(function ($company) use ($categories, $limit) {
            $company->subcategories = $limit ? $categories->get($company->id)?->take($limit) : $categories->get($company->id);

            return $company;
        });
    }

    /**
     * This service loads a company with the corresponding requested optional fields
     *
     * @throws ValidationException
     */
    public static function loadCompanyByFriendlyUrl(
        string $friendlyUrl, string $searchBy = 'friendly_url', ?array $requestedFields = null): mixed
    {
        $columns = [
            'companies.id', 'companies.parent_id', 'companies.name', 'companies.description', 'companies.friendly_url',
            'companies.subdomain', 'companies.is_distributor', 'companies.manage_clients',
            'companies.partner_flag', 'companies.type', 'companies.profile_company_friendly_name',
            'companies.show_distributor_banner', 'companies.show_manage_clients_banner',
            'companies.show_manage_affiliates_banner', 'companies.company_profile_types_id',
            'companies.created_at', 'companies.updated_at', 'companies.profile_vendor_handle',
            'companies.phone', 'companies.city', 'companies.address', 'companies.address2', 'companies.state_id',
            'companies.country_id', 'companies.zip', 'companies.affiliate_id', 'companies.hide_expenses',
        ];

        $companyQuery = Company::select($columns)
            ->with('enumType:id,value,label,type_is_of_vendor')
            ->where($searchBy, $friendlyUrl);

        if ($requestedFields && count($requestedFields) > 0) {
            $requestedFields = array_unique($requestedFields);

            foreach ($requestedFields as $field) {
                match ($field) {
                    'users' => $companyQuery->with('users:id,company_id,first_name,last_name,handle,friendly_url'),
                    'likes_count' => $companyQuery->with('likes:id,action,subject_id'),
                    'categories' => $companyQuery->with('categories:id,name'),
                    'revenue' => $columns[] = 'revenue',
                    'address' => $columns[] = 'address',
                    'address2' => $columns[] = 'address2',
                    'city' => $columns[] = 'city',
                    'state' => $companyQuery->with('state'),
                    'claimers' => $companyQuery->with('claimers'),
                    'zip' => $columns[] = 'zip',
                    'phone' => $columns[] = 'phone',
                    'industry' => $columns[] = 'industry',
                    'employee_range' => $columns[] = 'employee_range',
                    'founded' => $columns[] = 'founded',
                    'profile_claimer_user_id' => $companyQuery->with('companyClaimers:id,user_id,company_id,created_at,updated_at'),
                    'profile_company_website_url' => $columns[] = 'profile_company_website_url',
                    'features' => $columns[] = 'features',
                    'products' => $companyQuery->with('products:id,company_id,name,description,url,friendly_url'),
                    'blogs' => $companyQuery->with('blogs'),
                    'profile_images' => $companyQuery->with('media'),
                    'company_profile_type' => $companyQuery->with('companyProfileType:id,label,value,order'),
                    'bulletins' => $companyQuery->with('activeBulletins:id,title,description,color,author_id,company_id,start_date,end_date,duration,media_visibility,status,time_zone'),
                    'parent' => $companyQuery->with('parentCompany:id,name,description,friendly_url'),
                    'affiliate_brand' => $companyQuery->with('affiliateBrand:id,name,is_active,main_company_id,author_id'),
                    'client_parent' => $companyQuery->with('clientParent'),
                    'parent_id', 'current_user_is_following', 'rating', 'is_affiliate_brand_main_company',
                    'focuses', 'affiliate_id', 'is_mdf', 'affiliate_brand_id', 'followers_count', 'id' => '',
                    default => throw ValidationException::withMessages([
                        'ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::Logic not implemented for: ' . $field,
                    ]),
                };
            }
            $companyQuery->select($columns);
        } else {
            $companyQuery->with([
                'users' => function ($query) {
                    $query->select([
                        'id', 'company_id', 'first_name', 'last_name',
                        'handle', 'friendly_url', 'is_profile_complete', 'show_on_vendor_related_section',
                    ])
                        ->public()
                        ->IsVisibleOnVendorProfile();
                },
                'media',
                'enumType',
                'companyClaimers',
                'users',
                'claimers',
                'categories',
                'companyProfileType',
                'likes',
                'products',
                'products.categories',
                'affiliateBrand',
                'activeBulletins',
            ]);
        }
        $company = $companyQuery->first();
        if (!$company) {
            abort(404, config('genericMessages.success.NO_RESULTS'));
        }

        return $company;
    }

    public static function getFocuses(Company $company)
    {
        $focusValues = FocusCompanyValue::select('id', 'focus_option_id', 'company_id')
            ->with('options:id,focus_id,name')
            ->where('company_id', $company->id)
            ->get();
        $focuses = Focus::select('focuses.id', 'focuses.name', 'focuses.type')
            ->with('options:id,focus_id,name')
            ->join('focus_company_types', 'focus_company_types.focus_id', '=', 'focuses.id')
            ->where('focus_company_types.company_type_id', $company->type)
            ->orderBy('name')
            ->get();
        $focuses->transform(function ($item) use ($focusValues) {
            $item->options = $focusValues->pluck('options')->where('focus_id', $item->id);

            return $item;
        });

        return $focuses;
    }

    public static function getProfileImages(Company $company)
    {
        return $company->media()
            ->whereIn('collection_name', [
                config('custom.media_collections.avatar.company'),
                config('custom.media_collections.top_banner'),
            ])
            ->get();
    }

    public static function getAdminIds($adminType): array
    {
        $companies = Company::with('companyProfileType', 'enumType', 'companyClaimers')
            ->when($adminType === BroadcastMessageType::VendorAdmins()->key, function ($q) {
                return $q->whereHas(
                    'enumType',
                    function ($query) {
                        $query->where('type_is_of_vendor', true);
                    }
                );
            })->when($adminType === BroadcastMessageType::MspAdmins()->key, function ($q) {
                $q->whereHas(
                    'enumType',
                    function ($query) {
                        $query->where('type_is_of_vendor', false);
                    }
                );
            })->when($adminType === BroadcastMessageType::AllAdmins()->key, function ($q) {
                $q->whereHas('enumType');
            })->whereHas('companyClaimers')->get();

        $adminIds = [];
        foreach ($companies as $company) {
            foreach ($company->companyClaimers as $claimer) {
                $adminIds[] = $claimer->user_id;
            }
        }

        return array_unique($adminIds);
    }

    public static function getByIdList(Collection $idList, bool $loadAvatars = true)
    {
        $fields = [
            'companies.*',
            DB::raw("'vendor' as profile_type"),
            'company_types.type_is_of_vendor',
            'profile_vendor_handle as handle',
        ];
        $query = Company::select($fields)
            ->join('company_types', 'companies.type', '=', 'company_types.id')
            ->join('company_profile_types', 'companies.company_profile_types_id', '=', 'company_profile_types.id')
            ->whereIn('companies.id', $idList);

        if ($loadAvatars) {
            return ImageService::appendCompanyAvatars($query->get());
        }

        return $query->get();
    }

    public static function getCompanySubdomain(Company $company): string
    {
        if (empty($company->subdomain)) {
            return UtilityHelper::generateUniqueWord(
                'companies',
                'subdomain',
                $company->name,
                ''
            );
        }

        return $company->subdomain;
    }

    public static function getCompanyUsersAndClaimers(array $companyIds = []): Collection
    {
        return User::where('users.status', UserStatus::Active)
            ->whereIn('company_id', $companyIds)
            ->orWhereRaw('users.id IN (SELECT user_id FROM company_claimers where company_id IN (' . implode(',', $companyIds) . '))')
            ->get();
    }

    public static function loadCompaniesStacks(Collection $companies, $startDate = null, $endDate = null): Collection
    {
        $companies = $companies->filter();
        if ($companies->count() === 0) {
            return $companies;
        }

        $myStack = MyStack::select('id', 'stack_company_id', 'category_id')
            ->whereIn('stack_company_id', $companies->pluck('id')->toArray())
            ->whereHas('category', function ($query) {
                $query->select('id')->where('is_hidden', false);
            })
            ->when($startDate && $endDate, function ($query) use ($startDate, $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate]);
            })
            ->whereNull('deleted_at')
            ->get();

        return $myStack;
    }

    public static function appendStackToCompanies(Collection $companies, $startDate = null, $endDate = null): Collection
    {
        $companies = $companies->filter();
        if ($companies->count() === 0) {
            return $companies;
        }
        $companyStacks = self::loadCompaniesStacks($companies, $startDate, $endDate);
        $companies->transform(function ($company) use ($companyStacks) {
            if (!empty($company) && !empty($company->id)) {
                $company->appendedStack = $companyStacks->where('stack_company_id', $company->id);
            }

            return $company;
        });

        return $companies;
    }

    public static function calculateRatings(Collection $companies, $startDate = null, $endDate = null)
    {
        $companies = $companies->filter();
        if ($companies->count() === 0) {
            return $companies;
        }
        $products = $companies->flatten()->pluck('products')->flatten();
        if (count($products) === 0) {
            return $companies;
        }
        ProductService::calculateProductReviewsStatistics($products, $startDate, $endDate);
        $companies->transform(function ($item) use ($products) {
            $item->product_count = 0;
            $item->total_reviews = 0;
            $item->rating = null;
            foreach ($products as $product) {
                if ($product->rating !== null && $product->company_id === $item->id) {
                    $item->rating += floatval($product->rating);
                    $item->product_count++;
                    $item->total_reviews = $item->total_reviews + $product->total_reviews;
                }
            }
            if ($item->product_count > 0) {
                $item->rating = $item->rating / $item->product_count;
            }

            return $item;
        });

        return $companies;
    }

    public static function calculateRatingsForTopTrending(Collection $companies, $startDate = null, $endDate = null)
    {
        $companies = $companies->filter();
        if ($companies->count() === 0) {
            return $companies;
        }
        $products = $companies->flatten()->pluck('products')->flatten();
        if (count($products) === 0) {
            return $companies;
        }
        ProductService::calculateProductReviewsStatistics($products, $startDate, $endDate);
        $companies = self::appendStackToCompanies($companies, $startDate, $endDate);
        $companies->transform(function ($item) use ($products) {
            $item->product_count = 0;
            $item->total_reviews = 0;
            $item->final_rating = null;
            foreach ($products as $product) {
                if ($product->rating !== null && $product->company_id === $item->id) {
                    $item->final_rating += (float)$product->rating;
                    $item->product_count++;
                    $item->total_reviews += $product->total_reviews;
                }
            }
            if ($item->total_reviews > 0) {
                $item->final_rating = $item->total_reviews * (float)AppConfig::loadAppConfigByKey(
                    AppConfigEnum::REVIEW_COUNT_PERCENTAGE,
                    config('common.trending.companies.review_count_percentage'))->value;
                if ($item->appendedStack && $item->appendedStack->count() > 0) {
                    $item->final_rating = $item->final_rating + $item->appendedStack->count() * (float)AppConfig::loadAppConfigByKey(
                        AppConfigEnum::STACK_COUNT_PERCENTAGE,
                        config('common.trending.companies.stack_count_percentage'))->value;
                }
            }

            return $item;
        });

        return $companies;
    }

    public static function getContentLikesByDateRange($startDate, $endDate, $ignoreIdsList = [])
    {
        $blogsLikes = Analytics::query()
            ->select(['analytics.id', DB::raw('companies.id as company_id')])
            ->join('blogs', 'blogs.id', '=', 'analytics.subject_id')
            ->join('companies', 'companies.id', '=', 'blogs.subject_id')
            ->where('analytics.action', AnalyticAction::like)
            ->whereNotIn('companies.id', array_merge($ignoreIdsList, [config('custom.channel_program_company.id')]))
            ->whereBetween('analytics.created_at', [$startDate, $endDate]);

        $productsLikes = Analytics::query()
            ->select(['analytics.id', DB::raw('companies.id as company_id')])
            ->join('products', 'products.id', '=', 'analytics.subject_id')
            ->join('companies', 'companies.id', '=', 'products.company_id')
            ->where('analytics.action', AnalyticAction::like)
            ->whereNotIn('companies.id', array_merge($ignoreIdsList, [config('custom.channel_program_company.id')]))
            ->whereBetween('analytics.created_at', [$startDate, $endDate]);

        $productFeaturesLikes = Analytics::query()
            ->select(['analytics.id', DB::raw('companies.id as company_id')])
            ->join('product_features', 'product_features.id', '=', 'analytics.subject_id')
            ->join('products', 'products.id', '=', 'product_features.product_id')
            ->join('companies', 'companies.id', '=', 'products.company_id')
            ->where('analytics.action', AnalyticAction::like)
            ->whereNotIn('companies.id', array_merge($ignoreIdsList, [config('custom.channel_program_company.id')]))
            ->whereBetween('analytics.created_at', [$startDate, $endDate]);

        $productReviewsLikes = Analytics::query()
            ->select(['analytics.id', DB::raw('companies.id as company_id')])
            ->join('reviews', 'reviews.id', '=', 'analytics.subject_id')
            ->join('products', 'products.id', '=', 'reviews.model_id')
            ->join('companies', 'companies.id', '=', 'products.company_id')
            ->where('analytics.action', AnalyticAction::like)
            ->whereNotIn('companies.id', array_merge($ignoreIdsList, [config('custom.channel_program_company.id')]))
            ->whereBetween('analytics.created_at', [$startDate, $endDate]);

        $shoutOutsLikes = Analytics::query()
            ->select(['analytics.id', DB::raw('companies.id as company_id')])
            ->join('shout_outs', 'shout_outs.id', '=', 'analytics.subject_id')
            ->join('companies', 'companies.id', '=', 'shout_outs.subject_id')
            ->where('analytics.action', AnalyticAction::like)
            ->whereNotIn('companies.id', array_merge($ignoreIdsList, [config('custom.channel_program_company.id')]))
            ->whereBetween('analytics.created_at', [$startDate, $endDate]);

        $mediaGalleriesLikes = Analytics::query()
            ->select(['analytics.id', DB::raw('companies.id as company_id')])
            ->join('media_galleries', 'media_galleries.id', '=', 'analytics.subject_id')
            ->join('companies', 'companies.id', '=', 'media_galleries.model_id')
            ->where('analytics.action', AnalyticAction::like)
            ->whereNotIn('companies.id', array_merge($ignoreIdsList, [config('custom.channel_program_company.id')]))
            ->whereBetween('analytics.created_at', [$startDate, $endDate]);

        $mediasLikes = Analytics::query()
            ->select(['analytics.id', DB::raw('companies.id as company_id')])
            ->join('media', 'media.id', '=', 'analytics.subject_id')
            ->join('companies', 'companies.id', '=', 'media.model_id')
            ->where('analytics.action', AnalyticAction::like)
            ->whereNotIn('companies.id', array_merge($ignoreIdsList, [config('custom.channel_program_company.id')]))
            ->whereBetween('analytics.created_at', [$startDate, $endDate]);

        $subResult = $blogsLikes->unionAll($productsLikes)->unionAll($productFeaturesLikes)
            ->unionAll($productReviewsLikes)->unionAll($shoutOutsLikes)->unionAll($mediaGalleriesLikes)
            ->unionAll($mediasLikes);

        return $subResult->get();
    }

    public static function getTopTrendingResults(
        Collection $results,
        Carbon $startDate,
        Carbon $endDate,
        int $numOfCompanies,
        array $ignoreIdsList = []
    ): Collection {
        if (UtilityHelper::getDaysBetweenDates(now()->endOfDay(), $startDate) > (int)AppConfig::loadAppConfigByKey(
            AppConfigEnum::TOP_TRENDING_DAYS, config('common.topTrendingDays'))->value) {
            return $results;
        }
        $periodResult = self::prepareTopTrendingResults(collect(), $startDate, $endDate, $ignoreIdsList);
        if ($periodResult->count() > 0) {
            $periodResult->transform(function ($company) use ($startDate, $endDate) {
                $company->period = $startDate . ' - ' . $endDate;

                return $company;
            });
            $results = $results->merge($periodResult->sortByDesc('final_result')
                ->flatten()->take($numOfCompanies));
        }
        if ($results->count() < $numOfCompanies) {
            $nextEndDate = new Carbon($startDate->subDay()->endOfDay());
            $nextStartDate = new Carbon($startDate->clone()->startOfDay()->subDays(2));

            return self::getTopTrendingResults(
                $results,
                $nextStartDate,
                $nextEndDate,
                $numOfCompanies,
                array_merge($results->pluck('id')->toArray(), $ignoreIdsList)
            );
        }

        return $results;
    }

    private static function prepareTopTrendingResults($periodResult, $startDate, $endDate, $ignoreIdsList): Collection
    {
        $reviewedCompaniesQuery = Company::select('companies.id')
            ->with('products')
            ->join('products', 'products.company_id', '=', 'companies.id')
            ->join('reviews', 'reviews.model_id', '=', 'products.id')
            ->whereNotIn('companies.id', array_merge($ignoreIdsList, [config('custom.channel_program_company.id')]))
            ->where('reviews.status', ReviewStatus::approved)
            ->whereBetween('reviews.approved_date', [$startDate, $endDate]);

        $query = Company::select('companies.id')
            ->with('products')
            ->join('products', 'products.company_id', '=', 'companies.id')
            ->join('my_stack', 'my_stack.product_id', '=', 'products.id')
            ->whereNotIn('companies.id', array_merge($ignoreIdsList, [config('custom.channel_program_company.id')]))
            ->whereNull('deleted_at')
            ->whereBetween('my_stack.created_at', [$startDate, $endDate])
            ->union($reviewedCompaniesQuery)
            ->groupBy('companies.id');

        $result = $query->get();

        if ($result->count() > 0) {
            $result = self::calculateRatingsForTopTrending($result, $startDate, $endDate);
            $result->map(function ($company) use ($periodResult) {
                $element = new \stdClass();
                $element->id = $company->id;
                $element->rating = $company->final_rating;
                $periodResult->push($element);
            });
        }

        return $periodResult->sortByDesc('rating');
    }

    public static function updateCompanyFriendlyUrl($company, $request): void
    {
        $company->friendly_url = UtilityHelper::generateuniqueword('companies', 'friendly_url',
            UtilityHelper::generateUniqueWord('users', 'friendly_url', $request->get('name')),
            '', $company->id);
        $company->save();
        SyncHubspotCompanyUrl::dispatch($company);
    }

    public static function updateCompanySubdomain($company, $request): void
    {
        $company->subdomain = UtilityHelper::generateUniqueWord(
            'companies',
            'subdomain',
            $request->get('name')
        );
        $company->save();
    }

    /**
     * Modifies the query to include companies of a specific type for the channel program.
     */
    public static function withCompaniesType($request, $query): void
    {
        $query->with('enumType')
            ->when($request->has('company_type'), function ($q) use ($request) {
                $q->whereHas('enumType', function ($q) use ($request) {
                    match ($request->company_type) {
                        PrmMediaVisibility::VENDOR => $q->where('type_is_of_vendor', true),
                        PrmMediaVisibility::MSP => $q->where('type_is_of_vendor', false)
                            ->where('value', '<>', CompanyTypeEnum::DIRECT),
                        PrmMediaVisibility::DIRECT => $q->where('type_is_of_vendor', false)
                            ->where('value', CompanyTypeEnum::DIRECT),
                        default => throw ValidationException::withMessages([
                            'ERROR::' . __CLASS__ . '::' . __FUNCTION__ .
                            '::Logic not implemented for Company Type: ' . $request->company_type,
                        ]),
                    };
                });
            });
    }

    /**
     * Activates company invites for a user
     *
     * @throws ValidationException
     */
    public static function ActivateCompanyInvites(
        User $user, string $companyTypeValue, ?string $parentId = null, ?string $affiliateBrandId = null): void
    {
        $companyInvites = CompanyInvite::select('id', 'parent_company_id', 'child_company_id', 'email',
            'activated', 'activated_at', 'type', 'author_id', 'role_id')
            ->with(['parentCompany' => function ($query) {
                $query->select('id');
            }, 'parentCompany.affiliateBrandAsMainCompany' => function ($query) {
                $query->select('id', 'main_company_id')->where('is_active', true);
            }])
            ->where('email', $user->email)
            ->where('activated', false)
            ->where('type', $companyTypeValue)
            ->when($parentId !== null, function ($query) use ($parentId) {
                $query->where('parent_company_id', $parentId);
            })
            ->orderBy('created_at', 'desc')
            ->get();

        if ($companyInvites->count() > 0) {
            $companyType = CompanyType::select('id')
                ->where('value', CompanyTypeEnum::FranchiseMsp)
                ->first();
            $companyInvites->map(function ($invite) use ($user, $affiliateBrandId, $companyType, $parentId, $companyTypeValue) {
                if (!empty($invite->role_id)) {
                    RoleUserService::updateUserRoleByCompany(
                        Company::findOrFail($invite->parent_company_id), $invite->role_id, $user);
                }
                $invite->child_company_id = $user->company_id;
                $invite->activated = true;
                $invite->activated_at = now();
                $invite->save();
                if ($companyTypeValue === CompanyTypeEnum::FranchiseMsp) {
                    $user->company->parent_id = $parentId ??
                        $invite->parentCompany?->affiliateBrandAsMainCompany?->main_company_id;
                    $user->company->affiliate_brand_id = $affiliateBrandId ?? $invite->parentCompany?->affiliateBrandAsMainCompany?->id;
                    $user->company->type = $companyType->id;
                    $user->company->save();
                }
            });
        }
    }

    public static function checkMDFIsAvailable($companyId)
    {
        $query = ProfileEnrichmentQuestion::select([
            'id', 'key', 'type_is_of_vendor', 'is_archived',
        ])->where([
            'key' => 'PROVIDESMARKETINGDEVELOPMENTFUNDSMDF_SA',
            'type_is_of_vendor' => true,
            'is_archived' => false,
        ])->whereHas('options', function ($q) use ($companyId) {
            $q->select(['id', 'profile_enrichment_question_id', 'option'])
                ->whereRaw('lower(option) = \'yes\'')
                ->whereHas('answers', function ($q) use ($companyId) {
                    $q->select(['id', 'profile_enrichment_option_id', 'company_id'])
                        ->where('company_id', $companyId);
                });
        });

        return $query->exists();
    }

    public static function updateOrCreateCustomer($sourceId, $companyData, $companyType, $applicationSource, $cPCompanyID)
    {
        $location = __CLASS__ . '::' . __FUNCTION__;
        $createCompany = true;
        $companyName = $companyData['name'];
        $checkCompany = Company::select('companies.id', 'companies.name')
            ->leftjoin('company_clients', 'company_clients.client_id', 'companies.id')
            ->leftjoin('company_types', 'company_types.id', 'companies.type')
            ->where('company_clients.company_id', $cPCompanyID)
            ->where('company_types.value', CompanyTypeEnum::MSP_CLIENT)
            ->whereNull('companies.application_source_id')->whereNull('companies.application_source')
            ->where(function ($q) use ($companyName) {
                $q->whereRaw('lower(companies.name) like lower(?)', ['%' . $companyName . '%'])
                    ->orWhere('companies.name', UtilityHelper::removeSpecialCharacters($companyName));
            })->first();
        if (!empty($checkCompany)) {
            $createCompany = false;
            $companyWhere = [
                'id' => $checkCompany->id,
            ];
            $companyData['application_source_id'] = $sourceId;
            $companyData['application_source'] = $applicationSource;
        } else {
            $checkCompanyWithApplicationSource = Company::select('companies.id', 'companies.name')
                ->leftjoin('company_clients', 'company_clients.client_id', 'companies.id')
                ->leftjoin('company_types', 'company_types.id', 'companies.type')
                ->where('company_clients.company_id', $cPCompanyID)
                ->where('company_types.value', CompanyTypeEnum::MSP_CLIENT)
                ->where('companies.application_source_id', $sourceId)
                ->where('companies.application_source', $applicationSource)
                ->first();
            if (!empty($checkCompanyWithApplicationSource)) {
                $createCompany = false;
                $companyWhere = [
                    'application_source_id' => $sourceId,
                    'application_source' => $applicationSource,
                    'id' => $checkCompanyWithApplicationSource->id,
                ];
            } else {
                $companyData['application_source_id'] = $sourceId;
                $companyData['application_source'] = $applicationSource;
            }
        }
        $companyData['type'] = $companyType->id;
        if ($createCompany) {
            $company = Company::create($companyData);
        } else {
            $company = Company::updateOrCreate($companyWhere, $companyData);
        }
        $company->enumType()->associate($companyType);
        $company->subdomain = self::getCompanySubdomain($company);
        if (empty($company->friendly_url)) {
            $company->friendly_url = UtilityHelper::generateUniqueWord('companies', 'friendly_url',
                UtilityHelper::generateUniqueWord('users', 'friendly_url', $company->name)
            );
        }
        $company->profile_vendor_handle =
            UtilityHelper::generateUniqueWord('companies', 'profile_vendor_handle',
                UtilityHelper::generateUniqueWord('users', 'handle', $company->name));
        $company->save();

        self::appendVendorContractStatus($company);
        $phone = '';
        if (!empty($companyData['phone'])) {
            $phone = str_replace('-', '', $companyData['phone']);
            $companyData['phone'] = $phone;
        }
        $fax = '';
        if (!empty($companyData['fax'])) {
            $fax = str_replace('-', '', $companyData['fax']);
        }
        $alternatePhone = '';
        if (!empty($companyData['alternatePhone'])) {
            $alternatePhone = str_replace('-', '', $companyData['alternatePhone']);
        }
        $companyContactArray = [
            [
                'phone' => $phone,
                'title' => $companyData['name'],
                'email' => $companyData['email'],
            ],
            [
                'phone' => $fax,
                'title' => 'Fax',
            ],
            [
                'phone' => $alternatePhone,
                'title' => 'Alternate Phone',
            ],
        ];
        if (count($companyContactArray) > 0) {
            foreach ($companyContactArray as $companyContact) {
                if (!empty($companyContact['phone'])) {
                    CompanyContact::firstOrCreate([
                        'company_id' => $company->id,
                        'phone' => $companyContact['phone'],
                    ], [
                        'email' => $companyContact['email'] ?? null,
                        'title' => $companyContact['title'] ?? null,
                    ]);
                }
                if (!empty($companyContact['email'])) {
                    CompanyContact::firstOrCreate([
                        'company_id' => $company->id,
                        'email' => $companyContact['email'],
                    ], [
                        'title' => $companyContact['title'] ?? null,
                    ]);
                }
            }
        }

        if (!empty($companyData['profile_company_website_url'])) {
            $existsWebSite = CompanySocialMedia::where('url', $companyData['profile_company_website_url'])
                ->where('company_id', $company->id)->exists();
            if (!$existsWebSite) {
                CompanySocialMedia::create([
                    'company_id' => $company->id,
                    'url' => $companyData['profile_company_website_url'],
                    'title' => $companyData['name'],
                ]);
            }
        }

        return $company;
    }

    public static function getCompanyWithClaimer($companyID)
    {
        $companyClaimers = Company::select('id', 'name', 'company_profile_types_id', 'type')
            ->where('id', $companyID)->first();
        if (!$companyClaimers) {
            return null;
        }
        $companyClaimers->load('claimers', 'enumType');

        return $companyClaimers;
    }

    public static function checkCompanyIsExist($companyDetails, $companyProfileType, $applicationSource, $loggedUserId, $cPCompanyID)
    {
        $companyType = CompanyTypeService::getCompanyTypeByValue(CompanyTypeEnum::MSP_CLIENT);

        $companyData = [
            'name' => $companyDetails['name'] ?? null,
            'address' => $companyDetails['contact_site'] ?? null,
            'phone' => $companyDetails['phoneNumber'] ?? null,
            'email' => $companyDetails['email'] ?? null,
            'fax' => $companyDetails['fax'] ?? null,
            'alternatePhone' => $companyDetails['alternatePhone'] ?? null,
            'profile_company_website_url' => $companyDetails['website'] ?? null,
            'company_profile_types_id' => $companyProfileType,
            'created_by' => $loggedUserId,
            'updated_by' => $loggedUserId,
        ];

        $company = self::updateOrCreateCustomer(
            $companyDetails['id'], $companyData, $companyType, $applicationSource, $cPCompanyID
        );

        return $company;
    }

    /**
     * @throws ValidationException
     */
    public static function findRoleByKey(string $key): Role
    {
        $role = Role::select('id', 'company_id', 'title', 'description', 'key', 'template_role_id', 'is_admin')
            ->where('key', $key)
            ->first();
        if ($role === null) {
            Log::debug(__CLASS__ . '::' . __FUNCTION__ . '::' . $key
                . config('genericMessages.error.NOT_FOUND'));

            throw ValidationException::withMessages([config('genericMessages.error.PLEASE_TRY_AGAIN')]);
        }

        return $role;
    }

    /**
     * Extracts numeric elements from a comma-separated list of IDs.
     *
     * @param  string  $listOfDontShowCompanies  The string containing comma-separated IDs.
     * @return array An array of numeric IDs.
     */
    public static function getCompaniesIdsFromStringList(string $listOfDontShowCompanies): array
    {
        // Convert the comma-separated string into an array
        $items = explode(',', $listOfDontShowCompanies);

        // Filter the array to keep only the numeric elements
        return array_filter($items, static function ($item) {
            return is_numeric(trim($item));
        });
    }

    public static function companyIsAccessible(User $user, string $parentCompanyId, string $clientCompanyId): bool
    {
        if (AuthService::userIsSuperAdmin($user)) {
            return true;
        }

        $isAccessible = PermissionRole::join('roles_users', 'permissions_roles.role_id', '=', 'roles_users.role_id')
            ->join('permission_groups', 'permission_groups.id', '=', 'permissions_roles.permission_group_id')
            ->join('roles', 'roles.id', '=', 'roles_users.role_id')
            ->where('roles_users.user_id', $user->id)
            ->where('roles.company_id', $parentCompanyId)
            ->where('permission_groups.key', 'MSP_CLIENTS_MGNMT_READ')
            ->exists();

        $companyClientExist = CompanyClient::where('company_id', $parentCompanyId)
            ->where('client_id', $clientCompanyId)
            ->exists();

        return $isAccessible && $companyClientExist;
    }

    public static function loggedUserBelongToCompany(Company $company): bool
    {
        if (AuthService::userIsSuperAdmin()) {
            return true;
        }

        return $company->allUsers()
            ->where('users.id', AuthService::getLoggedInUserId())
            ->exists();
    }

    public static function initialLetters(Company $company): string
    {
        return Str::of(Str::replace(' ', '', $company->name))->substr(0, 2);
    }
}
