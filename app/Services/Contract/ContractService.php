<?php

namespace App\Services\Contract;

use App\Enums\ActivityLogAction;
use App\Enums\Company\CompanyType;
use App\Enums\Contract\BillingFrequencyEnum;
use App\Enums\Contract\ContractAgreementEnum;
use App\Enums\Contract\ContractTypes;
use App\Enums\CSV\CSVColumnsEnum;
use App\Enums\FileUploadSummaryStatus;
use App\Enums\ModelType;
use App\Enums\MyStackPartnerStatus;
use App\Helpers\CSVHelper;
use App\Helpers\UtilityHelper;
use App\Jobs\ProcessImportContractsFromCSVJob;
use App\Models\Category\Category;
use App\Models\Company\Company;
use App\Models\Contract\ClientProduct;
use App\Models\Contract\Contract;
use App\Models\Contract\ContractAgreement;
use App\Models\Contract\ContractBillingType;
use App\Models\Contract\ContractNextPaymentDate;
use App\Models\Contract\ContractNotification;
use App\Models\Contract\ContractNotificationType;
use App\Models\Contract\ContractType;
use App\Models\MyStack\CustomerStack;
use App\Models\MyStack\MyStack;
use App\Models\Product;
use App\Models\User;
use App\Services\ActivityLogs\ActivityLogsService;
use App\Services\Currency\CurrencyService;
use App\Services\ImageService;
use Exception;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use stdClass;

class ContractService
{
    /**
     * Load a contract based on its ID and type, with optional parent ID and relations.
     *
     * @param  int  $id  The ID of the contract.
     * @param  string  $contractType  The type of the contract.
     * @param  int|null  $parentId  The optional parent ID of the contract.
     * @param  array|null  $relations  The optional relations to load with the contract.
     * @return Contract The loaded contract.
     *
     * @throws Exception If the contract is not found.
     */
    public static function loadContractFromIdAndType(
        int $id,
        string $contractType,
        ?int $parentId = null,
        ?array $relations = null
    ): Contract {
        $contract = Contract::where(['id' => $id])
            ->when(!is_null($parentId), function ($q) use ($parentId) {
                $q->where('parent_id', $parentId);
            })->whereHas('contractType', function ($q) use ($contractType) {
                $q->where(['key' => $contractType]);
            })->when(!is_null($relations), function ($q) use ($relations) {
                $q->with($relations);
            })->first();
        if (!$contract) {
            throw ValidationException::withMessages(['Contract not found']);
        }

        return $contract;
    }

    /**
     * Stores a new vendor contract with associated data, including setting contract details,
     * calculating costs, saving the contract, and logging the activity.
     *
     * @param  array  $contractData  The data for the contract.
     * @param  Company  $company  The company associated with the contract.
     * @param  User  $loggedUser  The user creating the contract.
     * @return Contract The created contract.
     *
     * @throws ValidationException
     */
    public static function storeVendorContract(
        array $contractData,
        Company $company,
        User $loggedUser
    ): Contract {
        self::setContractTypeIdByKey($contractData, ContractTypes::CONTRACT);
        self::setNameAndCompanyIDValue($contractData);
        self::applyCurrencyRules($contractData, $company);

        $contractData['owner_id'] = $company->id;
        ContractClientService::setNameAndVendorIDValues($contractData);

        $productContracts = $contractData['product_contracts'] ?? [];
        unset($contractData['product_contracts']);
        $contractData['author_id'] = $loggedUser->id;
        $contractData['owner_id'] = $company->id;
        $contract = Contract::make($contractData);
        $cost = $contractData['cost'] ?? 0;
        if ($cost) {
            $contract->cost = round((float)$cost, 2);
            $contract->cost_after_discount =
                round(($contract->cost - $contract->cost * $contract->discount / 100), 2);
        }
        $contract->save();

        if (count($productContracts) > 0) {
            self::storeChildren($loggedUser, $company, $productContracts, $contract);
        }
        $contract->load([
            'company', 'clientVendor', 'clientProduct', 'currency',
            'productContracts',
            'productContracts.nextPaymentDates',
            'productContracts.notifications',
            'productContracts.agreement',
            'productContracts.currency',
        ]);
        ContractLogService::addActivityLog(ActivityLogAction::storeContract, $contract, $loggedUser);

        return $contract;
    }

    /**
     * Stores child product contracts for a parent contract, setting necessary details and logging the creation.
     * Handles the recurrence and notifications, as well as updating the company"s stack if applicable.
     *
     * @param  User  $loggedUser  The user creating the contracts.
     * @param  Company  $company  The company associated with the contracts.
     * @param  array  $productContracts  The data for the product contracts.
     * @param  Contract  $contract  The parent contract.
     *
     * @throws ValidationException
     */
    public static function storeChildren(
        User $loggedUser,
        Company $company,
        array $productContracts,
        Contract $contract
    ): void {
        $productContractType = ContractType::firstWhere('key', ContractTypes::PRODUCT_CONTRACT);
        if (!$productContractType) {
            throw ValidationException::withMessages(['The contract type ' . ContractTypes::CONTRACT . ' was not found.']);
        }
        foreach ($productContracts as $productContractData) {
            $productContractData['owner_id'] = $company->id;
            $product = array_key_exists('product_id', $productContractData)
                ? Product::select('id', 'name')
                    ->where('id', $productContractData['product_id'])
                    ->first()
                : $productContractData['client_product_name'];

            self::setNameAndCompanyIDValue($productContractData);
            ContractClientService::setNameAndVendorIDValues($productContractData, $contract->clientVendor);
            ContractClientService::setProductID($productContractData);
            self::applyCurrencyRules($productContractData, $company);

            $billingFrequency = $productContractData['billing_frequency'] ?? null;
            $initialPaymentDate = $productContractData['initial_payment_date'] ?? null;
            $cost = array_key_exists('cost', $productContractData) ?
                round(($productContractData['cost']), 2) : null;
            $discount = $productContractData['discount'] ?? null;
            $costAfterDiscount = null;
            if ($cost) {
                $costAfterDiscount = round(($cost - $cost * ($discount ?? 0) / 100), 2);
            }
            $contractData = [
                'name' => $product?->name ?? $productContractData['name'] ?? $contract->name ?? null,
                'parent_id' => $contract->id,
                'category_id' => !empty($productContractData['parent_category_id']) && empty($productContractData['category_id']) ?
                    $productContractData['parent_category_id'] : ($productContractData['category_id'] ?? null),
                'product_id' => $productContractData['product_id'] ?? null,
                'client_product_id' => $productContractData['client_product_id'] ?? null,
                'client_vendor_id' => $productContractData['client_vendor_id'] ?? null,
                'contract_agreement_id' => $productContractData['contract_agreement_id'] ?? null,
                'contract_billing_type_id' => $productContractData['contract_billing_type_id'] ?? null,
                'contract_type_id' => $productContractType->id,
                'currency_id' => $productContractData['currency_id'] ?? $contract->currency_id,
                'has_fixed_rate' => (bool)$productContractData['has_fixed_rate'] ?? 0,
                'exchange_rate' => $productContractData['exchange_rate'] ?? null,
                'cost' => $cost,
                'discount' => $productContractData['discount'] ?? null,
                'cost_after_discount' => $costAfterDiscount,
                'start_date' => $productContractData['start_date'] ?? null,
                'end_date' => $productContractData['end_date'] ?? null,
                'recurrence' => $billingFrequency,
                'initial_payment_date' => $initialPaymentDate,
                'expiry_date' => $productContractData['expiry_date'] ?? null,
                'notice_period' => $productContractData['notice_period'] ?? $contract->notice_period,
                'notice_date' => $productContractData['notice_date'] ?? $contract->notice_date,
                'auto_renew' => $productContractData['auto_renew'] ?? $contract->auto_renew,
                'custom_properties' => $productContractData['custom_properties'] ?? null,
                'author_id' => $loggedUser->id,
                'owner_id' => $productContractData['owner_id'],
                'company_id' => $contract->company_id,
            ];
            $productContract = Contract::create($contractData);
            $productContract->refresh();
            self::updateNextPaymentDates($productContract);
            self::forgetContractsCache($productContract->owner_id);
            if (isset($productContractData['notifications'])) {
                self::storeNotifications($productContractData['notifications'], $productContract, $loggedUser);
            }
            if (!is_null($productContractData['category_id'])) {
                // only do this for companies that are not of type MSP_CLIENT
                self::updateCompanyStack($loggedUser, $company, $productContract);
            }
            ContractLogService::addActivityLog(ActivityLogAction::storeContract, $productContract, $loggedUser);
        }
    }

    /**
     * Update a contract with validated request data and log the changes.
     *
     * @param  User  $loggedUser  The user performing the update.
     * @param  Contract  $contract  The contract to be updated.
     *
     * @throws ValidationException If validation fails during the update process.
     */
    public static function updateContract(
        User $loggedUser,
        Contract $contract,
        array $contractData
    ): void {
        DB::beginTransaction();

        try {
            $billingFrequency = $contractData['billing_frequency'] ?? $contract->recurrence ?? null;
            $contractData['recurrence'] = $billingFrequency;

            $contractData['currency_id'] = $contractData['currency_id'] ?? $contract->currency_id;
            self::applyCurrencyRules($contractData, $contract->owner);
            // Removing outdated exchange rate flag
            $customProperties = $contractData['custom_properties'] ?? $contract->custom_properties;
            unset($customProperties['outdated_exchange_rate']);
            $contractData['custom_properties'] = $customProperties;

            $discount = $contractData['discount'] ?? null;
            if (array_key_exists('cost', $contractData)) {
                $contractData['cost_after_discount'] = $contractData['cost'];
                if (!is_null($contractData['cost'])) {
                    $cost = $contractData['cost'];
                    $contractData['cost'] = round((float)($cost), 2);
                    $contractData['cost_after_discount'] = round(($cost - $cost * ($discount ?? 0) / 100), 2);
                }
            }
            if (!empty($contractData['client_product_name']) && !empty($contractData['owner_id'])) {
                $clientProduct = ClientProduct::where('name', $contractData['client_product_name'])
                    ->where('owner_id', $contractData['owner_id'])
                    ->first();
                if (!$clientProduct) {
                    $clientProduct = ClientProduct::create([
                        'name' => $contractData['client_product_name'],
                        'owner_id' => $contractData['owner_id'],
                    ]);
                }
                $contractData['client_product_id'] = $clientProduct->id ?? null;
                $contractData['name'] = $contractData['client_product_name'];
            }
            if (!empty($contractData['parent_category_id']) && empty($contractData['category_id'])) {
                $contractData['category_id'] = $contractData['parent_category_id'];
            }
            // Updating contract
            $contract->fill($contractData);
            if ($contract->isDirty()) {
                ContractLogService::addUpdateActivityLog($contract, $loggedUser);
            }
            if (!empty($contractData['cost']) && !empty($contractData['discount'])) {
                $contractData['cost'] = round((float)$contractData['cost'], 2);
                $contractData['discount'] = (float)$contractData['discount'];
                $contract->cost_after_discount = round($contractData['cost'] - $contractData['cost'] * $contractData['discount'] / 100, 2);
            }
            if (!$contract->isClean('product_id') && !empty($contractData['product_id'])) {
                $contract->product_id = $contractData['product_id'];
                $contract->name = Product::where('id', $contractData['product_id'])->first()->name;
            }
            $contract->save();
            self::updateNextPaymentDates($contract);
            self::forgetContractsCache($contract->owner_id);
            if (isset($contractData['notifications'])) {
                self::storeNotifications($contractData['notifications'], $contract, $loggedUser);
            }
            DB::commit();
        } catch (ValidationException $error) {
            DB::rollBack();
            Log::error(__CLASS__ . '::' . __FUNCTION__ . '::' . $error->getMessage());

            throw ValidationException::withMessages([$error->getMessage()]);
        }
    }

    /**
     * Deletes a contract and its associated data (notifications, documents).
     * If the contract is part of a parent contract, and the parent has no other related contracts, the parent contract is also deleted.
     *
     * @param  User  $loggedUser  The user performing the deletion.
     * @param  Contract  $contract  The contract to be deleted.
     *
     * @throws ValidationException
     */
    public static function deleteContract(
        User $loggedUser,
        Contract $contract
    ): void {
        Log::debug(__CLASS__ . '::' . __FUNCTION__ . '::Deleting contract: ID ' . $contract->id);
        ContractLogService::addActivityLog(ActivityLogAction::deleteContract, $contract, $loggedUser);
        $contract->load(['parent:id,parent_id,owner_id,name', 'product:id,name']);
        $parent = $contract->parent;
        // Getting contract owner ID
        $contractOwnerID = $contract->owner_id;
        // Deleting product contract
        $contract->notifications()->delete();
        $contract->contractDocuments->map(function ($contractDocument) {
            $contractDocument->delete();
        });
        $contract->delete();
        self::forgetContractsCache($contractOwnerID);

        // Deleting parent
        if ($parent && $parent->productContracts()->count() === 0) {
            Log::debug(__CLASS__ . '::' . __FUNCTION__ . '::Deleting aggregator contract. ID ' . $parent?->id);
            self::deleteContract($loggedUser, $parent);
        }
    }

    /**
     * Add the product to the company's stack
     */
    private static function updateCompanyStack(User $user, Company $company, Contract $contract): void
    {
        if ($company->enumType->value !== CompanyType::MSP_CLIENT) {
            $alreadyAddedToStack = MyStack::where([
                'company_id' => $company->id,
                'product_id' => $contract->product_id,
                'stack_company_id' => $contract->company_id,
                'category_id' => $contract->category_id,
            ])->first();

            if (!$alreadyAddedToStack) {
                $company->myStack()->syncWithoutDetaching([[
                    'stack_company_id' => $contract->company_id,
                    'product_id' => $contract->product_id,
                    'partner_status' => MyStackPartnerStatus::currentPartner,
                    'category_id' => $contract->category_id,
                    'user_id' => $user->id,
                ]]);
            }
        } else {
            $alreadyAddedToStack = CustomerStack::where([
                'company_id' => $company->id,
                'product_id' => $contract->client_product_id,
                'stack_company_id' => $contract->client_vendor_id,
                'category_id' => $contract->category_id,
            ])->first();

            if (!$alreadyAddedToStack) {
                $company->customerStack()->syncWithoutDetaching([[
                    'stack_company_id' => $contract->client_vendor_id,
                    'product_id' => $contract->client_product_id,
                    'partner_status' => MyStackPartnerStatus::currentPartner,
                    'category_id' => $contract->category_id,
                    'user_id' => $user->id,
                ]]);
            }
        }
    }

    /**
     * This service stores contract notifications
     */
    public static function storeNotifications(
        array $notifications,
        Contract $contract,
        User $loggedUser
    ): void {
        $dataToLog = [];
        foreach ($notifications as $notificationData) {
            // Searching for an existing notification
            $notification = $contract->notifications()
                ->with(['contractNotificationType'])
                ->firstWhere('contract_notification_type_id', $notificationData['contract_notification_type_id']);

            // Notification is disabled
            if (!$notificationData['enabled']) {
                if ($notification && $notification->enabled) {
                    $dataToLog[] = [
                        'event' => 'Edited "' . $notification->contractNotificationType->name . '" notification',
                        'description' => 'From "Active" -> "Inactive"',
                    ];
                    $notification->update(['enabled' => false]);
                }

                continue;
            }
            $newNotificationType = $notification?->contractNotificationType
                ?? ContractNotificationType::find($notificationData['contract_notification_type_id']);

            if (!$notification || !$notification->enabled) {
                $dataToLog[] = [
                    'event' => 'Edited "' . $newNotificationType->name . '" notification',
                    'description' => 'From "Inactive" -> "Active"',
                ];
            }
            if ($notificationData['days_before'] !== $notification?->days_before) {
                $dataToLog[] = [
                    'event' => 'Edited "' . $newNotificationType->name . ' - Amount of days" notification',
                    'description' => 'From "' . ($notification?->days_before ??
                            'empty') . '" -> "' . $notificationData['days_before'] . '"',
                ];
            }
            if ($notificationData['recipients'] !== $notification?->recipients) {
                $oldRecipients = $notification?->recipients === null ?
                    'empty' : implode(', ', $notification?->recipients);
                $newRecipients = empty($notificationData['recipients'])
                    ? 'empty' : implode(', ', $notificationData['recipients']);
                $dataToLog[] = [
                    'event' => 'Edited "' . $newNotificationType->name . ' - Recipients" notification',
                    'description' => 'From "' . $oldRecipients . '" -> "' . $newRecipients . '"',
                ];
            }
            ContractNotification::updateOrCreate([
                'contract_id' => $contract->id,
                'contract_notification_type_id' => $notificationData['contract_notification_type_id'],
            ], [
                'enabled' => $notificationData['enabled'],
                'days_before' => $notificationData['days_before'],
                'recipients' => $notificationData['recipients'],
            ]);
        }
        // Storing logs
        foreach ($dataToLog as $data) {
            ActivityLogsService::store(
                ActivityLogAction::updateContract,
                ModelType::userType,
                $loggedUser->id,
                ModelType::companyContract,
                $contract->id,
                $data
            );
        }
    }

    /**
     * This service updates the next payment dates for a contract between the start date and end date
     * according to the recurrence data
     *
     * @throws ValidationException
     */
    public static function updateNextPaymentDates(Contract $contract, bool $allowNullContractBillingType = false): void
    {
        $billingFrequency = $contract->recurrence ?? null;
        $contractAgreementKey = $contract->agreement?->key ?? null;
        $initialPaymentDateStr = $contract->initial_payment_date ?? null;
        if (
            is_null($billingFrequency) || is_null($contractAgreementKey)
            || is_null($initialPaymentDateStr) || is_null($contract->cost_after_discount)
            || (is_null($contract->contract_billing_type_id) && !$allowNullContractBillingType)
        ) {
            return;
        }
        Log::debug(__CLASS__ . '::' . __FUNCTION__ .
            '::Updating Next Payment Dates for Contract (ID = ' . $contract->id . ')');
        ContractNextPaymentDate::where('contract_id', $contract->id)->delete();
        $initialPaymentDate = Carbon::parse($initialPaymentDateStr);
        $contractAgreementFactor = self::calculateContractAgreementFactor($contractAgreementKey);
        $billingFrequencyFactor = self::calculateBillingFrequencyFactor(
            $contractAgreementKey, $contractAgreementFactor, $billingFrequency);
        $contract->load('owner', 'owner.configurations.currency', 'currency');
        $nextPaymentDates = self::calculateNextPaymentDates(
            $contract,
            $initialPaymentDate,
            $billingFrequency,
            $billingFrequencyFactor,
            $contractAgreementKey
        );
        ContractNextPaymentDate::insert($nextPaymentDates);
    }

    // Return exchange rate factor and last exchange rate update based on the contract configuration and date
    private static function getExchangeRateForPayment(
        Contract $contract,
        Carbon $date
    ): array {
        $exchange_rate = null;
        $exchange_rate_date = null;
        if ((bool)$contract->has_fixed_rate) {
            $exchange_rate = $contract->exchange_rate;
            $exchange_rate_date = now();
        } else if ($date->lte(Carbon::now())) {
            $companyCurrency = $contract->owner->configurations->currency;
            $contractCurrency = $contract->currency;
            if ($companyCurrency->id === $contractCurrency->id) {
                $exchange_rate = 1;
                $exchange_rate_date = null;
            } else {
                $exchangeRate = CurrencyService::getClosestRateToDate(
                    $companyCurrency->key,
                    $contractCurrency->key,
                    $date->format('Y-m-d')
                );
                $exchange_rate = $exchangeRate->rates[$contractCurrency->key] ?? 1;
                $exchange_rate_date = $exchangeRate->last_rate_update ?? null;
            }
        }

        return [$exchange_rate, $exchange_rate_date];
    }
    /**
     * @throws ValidationException
     */
    private static function calculateNextPaymentDates(
        Contract $contract,
        Carbon $initialPaymentDate,
        $billingFrequency,
        $billingFrequencyFactor,
        $contractAgreementKey
    ): array {
        $now = now();
        $calculatedPaymentCost = self::calculatePaymentCost(
            $contract->agreement->id,
            $contract->contract_billing_type_id, $billingFrequency,
            $contract->cost_after_discount,
            Arr::get($contract->custom_properties, 'unit_cost', 0) ?? 0,
            Arr::get($contract->custom_properties, 'amount_purchased', 0) ?? 0);
        $newDate = $initialPaymentDate->clone()->endOfDay();
        [$exchange_rate, $exchange_rate_date] = self::getExchangeRateForPayment($contract, $newDate);
        $nextPaymentDates[] = [
            'contract_id' => $contract->id,
            'date' => $newDate->toDate(),
            'exchange_rate' => $exchange_rate,
            'exchange_rate_date' => $exchange_rate_date,
            'created_at' => $now,
            'updated_at' => $now,
            'cost' => $calculatedPaymentCost['total_per_payment'],
        ];
        if ($billingFrequency !== BillingFrequencyEnum::onetimepayment) {
            if ($contractAgreementKey === ContractAgreementEnum::NOTERM) {
                $billingFrequencyFactor = 12;
            }
            for ($i = 0; $i < $billingFrequencyFactor - 1; $i++) {
                $newDate = self::addBillingFrequencyToDate($billingFrequency, $newDate);
                [$exchange_rate, $exchange_rate_date] = self::getExchangeRateForPayment($contract, $newDate);
                $nextPaymentDates[] = [
                    'contract_id' => $contract->id,
                    'date' => $newDate->toDate(),
                    'exchange_rate' => $exchange_rate,
                    'exchange_rate_date' => $exchange_rate_date,
                    'created_at' => $now,
                    'updated_at' => $now,
                    'cost' => $calculatedPaymentCost['total_per_payment'],
                ];
            }
        }

        return $nextPaymentDates;
    }

    /**
     * @throws ValidationException
     */
    private static function calculateContractAgreementFactor(string $contractAgreement): int
    {
        return match ($contractAgreement) {
            ContractAgreementEnum::MONTHLY, ContractAgreementEnum::QUARTERLY,
            ContractAgreementEnum::ONEYEAR, ContractAgreementEnum::NOTERM => 1,
            ContractAgreementEnum::TWOYEAR => 2,
            ContractAgreementEnum::THREEYEAR => 3,
            ContractAgreementEnum::FOUREYEAR => 4,
            ContractAgreementEnum::FIVEYEAR => 5,
            default => throw ValidationException::withMessages([
                'ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::Logic not implemented for: ' . $contractAgreement,
            ]),
        };
    }

    /**
     * This service calculates the cost of a payment
     *
     * @throws ValidationException
     */
    public static function calculatePaymentCost(
        ?string $contractAgreementId,
        ?string $contractBillingTypeId,
        ?string $billingFrequency,
        float $totalContractCost = 0,
        float $unitCost = 0,
        int $amountPurchased = 0,
    ): array {
        // CALCULATIONS TAKEN FROM https://channelprogram.atlassian.net/browse/CPK-3220

        $contractAgreementKey = 'NOTERM';
        $billingFrequencyType = BillingFrequencyEnum::onetimepayment;

        if ($contractAgreementId) {
            $contractAgreement = ContractAgreement::select('id', 'key')->find($contractAgreementId);
            $contractAgreementKey = $contractAgreement->key;
        }
        if ($billingFrequency) {
            $billingFrequencyType = $billingFrequency;
        }

        $agreementFactor = $billingFrequencyType === BillingFrequencyEnum::onetimepayment
            ? 1
            : self::calculateContractAgreementFactor($contractAgreementKey);
        $billingFrequencyFactor = round(self::calculateBillingFrequencyFactor($contractAgreementKey,
            $agreementFactor, $billingFrequencyType));
        if ((int)$billingFrequencyFactor === 0) {
            $billingFrequencyFactor = 1;
        }
        $contractBillingType = ContractBillingType::find($contractBillingTypeId);
        if ($contractBillingType?->key === 'PER') {
            $totalPerPayment = $unitCost * $amountPurchased;
            $contractTotal = $totalPerPayment * $billingFrequencyFactor;
            $totalPerMonth = $totalPerPayment;
            $result = [
                'total_per_payment' => $billingFrequencyType === BillingFrequencyEnum::onetimepayment
                    ? $contractTotal
                    : $totalPerPayment,
                'total_monthly_cost' => $totalPerMonth,
                'total_contract_cost' => $contractTotal,
            ];
        } else {
            $totalPerPayment = $totalContractCost;
            $totalContractCost *= $billingFrequencyFactor;
            $totalPerMonth = $totalPerPayment;
            $result = [
                'total_per_payment' => $billingFrequencyType === BillingFrequencyEnum::onetimepayment
                    ? $totalContractCost
                    : $totalPerPayment,
                'total_monthly_cost' => $totalPerMonth,
                'total_contract_cost' => $totalContractCost,
            ];
        }

        return $result;
    }

    /**
     * Appends calculated payment costs to a contract and its associated product contracts.
     * The method calculates and updates the contract"s total cost, per payment cost, and monthly cost
     * based on its agreement, billing type, recurrence, and other related properties.
     *
     * @param  Contract  $contract  The contract for which payment costs are being calculated and appended.
     *
     * @throws ValidationException
     */
    public static function appendPaymentCost(Contract $contract): void
    {
        $shouldCalculate = $contract->contractType->key === ContractTypes::PRODUCT_CONTRACT
            && !empty($contract->contract_agreement_id)
            && !empty($contract->contract_billing_type_id)
            && !empty($contract->recurrence);
        if ($shouldCalculate) {
            $unitCost = (float)($contract->custom_properties['unit_cost'] ?? '0');
            $amountPurchased = (int)($contract->custom_properties['amount_purchased'] ?? '0');
            $payment_detail = self::calculatePaymentCost($contract->contract_agreement_id,
                $contract->contract_billing_type_id, $contract->recurrence, $contract->cost ?? 0,
                $unitCost, $amountPurchased
            );
            $contract->total_contract_cost = round($payment_detail['total_contract_cost'], 2);
            $contract->total_per_payment = round($payment_detail['total_per_payment'], 2);
            $contract->total_monthly_cost = round($payment_detail['total_monthly_cost'], 2);
        } else {
            $contract->total_contract_cost = $contract->cost ?? 0;
        }
        if ($contract->productContracts) {
            $contract->productContracts->transform(function ($productContract) {
                self::appendPaymentCost($productContract);

                return $productContract;
            });
        }
    }

    /**
     * This service loads the contracts by owner, dates range, search word, billing types,
     * types and contract ids
     */
    public static function loadContracts(string $companyType,
        string $ownerId, Carbon $startDate, Carbon $endDate, array $relations = [],
        string $searchWord = '', array $contractIds = [], array $billingTypes = [],
        array $types = [], array $vendors = [], array $expirationDates = [],
        array $categories = [],
        array $subCategories = []
    ): Collection {
        $query = Contract::select('contracts.id', 'contracts.name', 'contracts.parent_id', 'contracts.category_id',
            'contracts.product_id', 'contracts.contract_billing_type_id', 'contracts.contract_type_id',
            'contracts.currency_id', 'contracts.author_id', 'contracts.owner_id', 'contracts.cost',
            'contracts.has_fixed_rate', 'contracts.exchange_rate',
            'contracts.discount', 'contracts.cost_after_discount', 'contracts.start_date', 'contracts.end_date',
            'contracts.every', 'contracts.recurrence', 'contracts.notice_period', 'contracts.notice_date',
            'contracts.auto_renew', 'contracts.custom_properties', 'contracts.created_at', 'contracts.updated_at',
            'contracts.initial_payment_date', 'contracts.expiry_date', 'contracts.contract_agreement_id',
            'contracts.company_id', 'contracts.client_vendor_id', 'contracts.client_product_id',
            'contracts.unit_frequency', DB::raw('categories.parent_id AS category_parent_id'),
            DB::raw('(contracts.end_date::date - CURRENT_DATE) AS expire_in_days'))
            ->with($relations)
            ->leftJoin('categories', 'categories.id', '=', 'contracts.category_id')
            ->where('contracts.owner_id', $ownerId)
            ->where(function ($query) {
                $query->whereHas('contractType', function ($q) {
                    $q->where(['key' => ContractTypes::PRODUCT_CONTRACT]);
                })->orWhereDoesntHave('productContracts');
            })
            ->when(empty($expirationDates), function ($query) use ($startDate, $endDate) {
                $query->where(function ($query) use ($startDate, $endDate) {
                    $query->whereBetween('contracts.start_date', [$startDate, $endDate])
                        ->orWhereBetween('contracts.end_date', [$startDate, $endDate])
                        ->orWhereNull('contracts.start_date')
                        ->orWhereNull('contracts.end_date')
                        ->orWhereRaw('? BETWEEN contracts.start_date and contracts.end_date', [$startDate])
                        ->orWhereRaw('? BETWEEN contracts.start_date and contracts.end_date', [$endDate]);
                });
            })
            ->when(!empty($searchWord), function ($query) use ($searchWord, $companyType) {
                $query->where(function ($query) use ($searchWord, $companyType) {
                    $searchWords = explode(' ', strtolower($searchWord));
                    foreach ($searchWords as $searchWord) {
                        $searchLikeParameter = "%{$searchWord}%";
                        $query->whereRaw('lower(contracts.name) LIKE ?', [$searchLikeParameter]);
                        if ($companyType === CompanyType::MSP_CLIENT) {
                            $query->orWhereRaw('lower(client_vendors.name) LIKE ?', [$searchLikeParameter]);
                            $query->orWhereRaw('lower(client_products.name) LIKE ?', [$searchLikeParameter]);
                        } else {
                            $query->orWhereRaw('lower(products.name) LIKE ?', [$searchLikeParameter]);
                            $query->orWhereRaw('lower(companies.name) LIKE ?', [$searchLikeParameter]);
                        }
                    }
                });
            })
            ->when(!empty($contractIds), function ($query) use ($contractIds) {
                $query->whereIn('id', $contractIds);
            })
            ->when(!empty($categories), function ($query) use ($categories) {
                $query->where(function ($query) use ($categories) {
                    $query->whereIn('categories.parent_id', $categories);
                    if (in_array(null, $categories, true)) {
                        $query->orWhereNull('category_id');
                    }
                });
            })
            ->when(!empty($subCategories), function ($query) use ($subCategories) {
                $query->whereIn('contracts.category_id', $subCategories);
            })
            ->when(!empty($billingTypes), function ($query) use ($billingTypes) {
                $query->whereIn('contract_billing_type_id', $billingTypes);
            })
            ->when(!empty($types), function ($query) use ($types) {
                $query->whereIn('contract_type_id', $types);
            })
            ->when(!empty($vendors), function ($query) use ($vendors) {
                $query->whereIn('products.company_id', $vendors);
            })
            ->when(!empty($expirationDates), function ($query) use ($expirationDates) {
                $query->where(function ($query) use ($expirationDates) {
                    $query->whereIn('end_date', $expirationDates);
                    if (in_array(null, $expirationDates, true)) {
                        $query->orWhereNull('end_date');
                    }
                });
            });

        if ($companyType === CompanyType::MSP_CLIENT) {
            $query->leftJoin('client_vendors', 'client_vendors.id', '=', 'contracts.client_vendor_id')
                ->leftJoin('client_products', 'client_products.id', '=', 'contracts.client_product_id');
        } else {
            $query->leftJoin('products', 'products.id', '=', 'contracts.product_id')
                ->leftJoin('companies', 'companies.id', '=', 'products.company_id');
        }

        return $query->get();
    }

    /**
     * Removes the contracts KPIs cache for a specific company.
     *
     * This method clears the cache of key performance indicators (KPIs)
     * for contracts associated with a company, forcing recalculation on the next request.
     *
     * @param  string  $companyId  The ID of the company whose KPIs cache will be removed
     * @return void
     */
    public static function forgetContractsCache(string $companyId)
    {
        // Cleanning KPIs cache
        $cacheKey = "contract_kpis_{$companyId}_" . now()->format('Y-m-d');
        Cache::forget($cacheKey);
        // Cleanning Upcoming Renewals cache
        $cacheKey = "contract_upcoming_renewals_{$companyId}_" . now()->format('Y-m-d');
        Cache::forget($cacheKey);
    }

    /**
     * Retrieves and calculates key performance indicators (KPIs) for a company's contracts.
     *
     * This method loads contracts, calculates monthly and yearly costs by category,
     * identifies upcoming renewals, and caches the results for improved performance.
     *
     * @param  Company  $company  The company for which to retrieve contract KPIs
     * @param  bool  $audit  Whether to include detailed audit information in the results
     * @return Collection A collection containing categorized cost data and upcoming renewals
     */
    public static function getContractsKPIs(
        Company $company,
        bool $audit = false
    ): stdClass {
        $relations = [
            'contractType:id,key,name',
            'category:id,name,parent_id,color',
            'category.parentCategory:id,name,color',
            'currency:id,key,symbol,name',
            'nextPaymentDates:id,contract_id,date,cost,exchange_rate,exchange_rate_date',
            'productContracts.nextPaymentDates:id,contract_id,date,cost,exchange_rate,exchange_rate_date',
            'productContracts.category:id,name,parent_id,color',
            'productContracts.contractType:id,key,name',
            'clientVendor:id,name,is_distributor',
            'clientProduct:id,name,category_id,description',
        ];
        $contracts = ContractService::loadContracts(
            $company->enumType->value,
            $company->id,
            now()->subYears(10),
            now()->addYears(10),
            $relations
        );

        $cacheKey = "contract_kpis_{$company->id}_" . now()->format('Y-m-d');
        $cacheTTL = 60 * 60;

        return Cache::remember($cacheKey, $cacheTTL, function () use ($audit, $company, $contracts) {
            $result = new stdClass();
            // Getting monthly cost
            $lastMonthCost = ContractService::getContractsTotalsInPeriod(
                $company,
                $contracts,
                now()->subMonth(1)->startOfMonth(),
                now()->subMonth(1)->endOfMonth()
            );
            $result->monthlyCostElements = ContractService::getContractsTotalsInPeriod(
                $company,
                $contracts,
                now()->startOfMonth(),
                now()->endOfMonth(),
                (bool)$audit
            )->map(function ($item) use ($lastMonthCost) {
                $foundCategory = $lastMonthCost->firstWhere('category_id', $item->category_id);
                $item->previous_cost = $foundCategory?->total_cost ?? null;

                return $item;
            });
            // Getting yearly cost
            $lastYearCost = ContractService::getContractsTotalsInPeriod(
                $company,
                $contracts,
                now()->subYear(1)->startOfYear(),
                now()->subYear(1)->endOfYear()
            );
            $result->yearlyCostElements = ContractService::getContractsTotalsInPeriod(
                $company,
                $contracts,
                now()->startOfYear(),
                now()->endOfYear(),
                (bool)$audit
            )->map(function ($item) use ($lastYearCost) {
                $foundCategory = $lastYearCost->firstWhere('category_id', $item->category_id);
                $item->previous_cost = $foundCategory?->total_cost ?? null;

                return $item;
            });

            $startInterval = now();
            $endInterval = now()->addDays(180);
            $upcomingRenewals = UtilityHelper::cloneCollection($contracts);
            $result->upcomingRenewals = ContractService::prepareContractsAsIndependentElements($upcomingRenewals)
                ->filter(function ($contract) use ($startInterval, $endInterval) {
                    return $contract->end_date >= $startInterval && $contract->end_date <= $endInterval
                        ? $contract
                        : null;
                })->sortBy('end_date');

            return $result;
        });
    }

    /**
     * Returns a collection of contracts that are due to renew within a given date range,
     * including related entities and cached for one day.
     *
     * @param  Company  $company  The company for which to retrieve contract renewals.
     * @param  Carbon  $startDate  The start date of the renewal window.
     * @param  Carbon  $endDate  The end date of the renewal window.
     */
    public static function getUpcomingRenewals(
        Company $company,
        Carbon $startDate,
        Carbon $endDate
    ): Collection {
        $cacheKey = "contract_upcoming_renewals_{$company->id}_" . now()->format('Y-m-d');
        $cacheTTL = 60 * 60;

        return Cache::remember($cacheKey, $cacheTTL, function () use ($company, $startDate, $endDate) {
            $relations = [
                'contractBillingType:id,key,name',
                'contractBillingType.option:id,contract_billing_type_id,name',
                'contractType:id,key,name',
                'company:id,name,is_distributor,type',
                'company.avatar:id,model_type,model_id,collection_name,name,file_name,disk',
                'company.enumType:id,value',
                'currency:id,key,symbol,name',
                'agreement:id,key,name,description',
                'clientVendor:id,name,is_distributor',
                'clientProduct:id,name,category_id,description',
                'productContracts.company:id,name,is_distributor,type',
                'productContracts.company.avatar:id,model_type,model_id,collection_name,name,file_name,disk',
                'productContracts.company.enumType:id,value',
                'productContracts.contractType:id,key,name',
                'productContracts.contractBillingType:id,key,name',
                'productContracts.contractBillingType.option:id,contract_billing_type_id,name',
                'productContracts.currency:id,key,symbol,name',
            ];
            $contracts = ContractService::loadContracts($company->enumType->value, $company->id,
                $startDate, $endDate, $relations);
            // we need to append the images
            $vendors = $contracts->pluck('company')->flatten()->unique()->filter();
            $addons = $contracts->pluck('addOns')->flatten()->unique()->filter();
            $addonsVendors = $addons->pluck('company')->flatten()->unique()->filter();
            ImageService::appendAvatars($vendors->merge($addonsVendors));

            return ContractService::prepareContractsAsIndependentElements($contracts)->filter(
                function ($contract) use ($startDate, $endDate) {
                    return $contract->end_date >= $startDate && $contract->end_date <= $endDate
                        ? $contract
                        : null;
                })->sortBy('end_date');
        });
    }

    /**
     * This service get a custom StdClass collection with the contracts grouped by category.
     * Also adds the total cost for each one of the categories group
     */
    public static function getContractsThatMakePaymentInPeriod(
        Collection $contracts,
        Carbon $startDate,
        Carbon $endDate,
        ?string $orderBy = 'date',
        ?string $sort = 'DESC'
    ): Collection {
        $collection = UtilityHelper::cloneCollection($contracts);

        // Filtering contracts that make payments in period
        $contractsThatMakePaymentInPeriod = $collection->filter(function ($contract) use ($startDate, $endDate) {
            if ($contract->nextPaymentDates->isEmpty()) {
                return true;
            }
            $contract->nextPaymentDates = $contract->nextPaymentDates?->filter(function ($date) use ($startDate, $endDate) {
                return $date['date'] <= $endDate && $date['date'] >= $startDate;
            });

            return
                $contract->nextPaymentDates->isNotEmpty()
                || $contract->start_date === null
                || $contract->end_date === null;
        });

        $contractsThatMakePaymentInPeriod = $sort === 'DESC'
            ? $contractsThatMakePaymentInPeriod->sortByDesc($orderBy)
            : $contractsThatMakePaymentInPeriod->sortBy($orderBy);

        // Grouping contracts by categories and calculating costs
        return $contractsThatMakePaymentInPeriod->groupBy('category_parent_id')
            ->map(function ($contracts) {
                $totalCost = 0;
                $categoryContracts = $contracts->map(function ($contract) use (&$totalCost) {
                    $totalCost += $contract->nextPaymentDates->sum('cost');

                    return $contract;
                });

                $parentCategory = $contracts->first()->category?->parentCategory ?? null;

                return (object)[
                    'category_id' => $parentCategory?->id ?? null,
                    'category_name' => $parentCategory?->name ?? 'Uncategorized',
                    'category_color' => $parentCategory?->color ?? null,
                    'total_cost' => $totalCost,
                    'total_contracts' => $contracts->count(),
                    'contracts' => $categoryContracts,
                ];
            })->sortBy('category_name');
    }

    /**
     * Computes contract KPIs for a given company within a specified period.
     *
     * This method filters contracts that have payments within the given date range,
     * groups them by category and currency, and calculates total costs based on exchange rates.
     *
     * @param  Company  $company  The company for which the KPIs are calculated.
     * @param  Collection  $contracts  A collection of contracts to process.
     * @param  Carbon  $startDate  The start date of the period to analyze.
     * @param  Carbon  $endDate  The end date of the period to analyze.
     * @return Collection A collection of categorized contract KPIs, including total costs and currency breakdowns.
     */
    public static function getContractsTotalsInPeriod(
        Company $company,
        Collection $contracts,
        Carbon $startDate,
        Carbon $endDate,
        ?bool $audit = false
    ): Collection {
        $source = __CLASS__ . '::' . __FUNCTION__ . '::';
        $companyConfig = $company->getConfig();
        $companyCurrency = $companyConfig->currency;

        $collection = UtilityHelper::cloneCollection($contracts);

        // Filtering contracts that make payments in period
        $contractsThatMakePaymentInPeriod = $collection->filter(function ($contract) use ($startDate, $endDate) {
            if ($contract->nextPaymentDates->isEmpty()) {
                return true;
            }
            $contract->nextPaymentDates = $contract->nextPaymentDates?->filter(function ($date) use ($startDate, $endDate) {
                return $date['date'] <= $endDate && $date['date'] >= $startDate;
            });

            return
                $contract->nextPaymentDates->isNotEmpty()
                || $contract->start_date === null
                || $contract->end_date === null;
        });

        // Grouping contracts by categories and calculating costs
        $contractsByCategory = $contractsThatMakePaymentInPeriod->groupBy('category_parent_id');
        // Getting all contracts categories
        $categoryIDs = $contractsByCategory->keys()->filter()->unique()->all();
        $categoriesMap = Category::select('id', 'name', 'color')
            ->whereIn('id', $categoryIDs)->get()->keyBy('id');

        $categories = collect([]);
        foreach ($contractsByCategory as $categoryId => $contracts) {
            // Getting category from list
            $category = $categoryId ? $categoriesMap->get($categoryId) : null;

            $totalCategoryCost = 0;
            $totalCategoryContracts = $contracts->count();

            $contractsByCurrency = $contracts->groupBy('currency_id');
            $currencies = collect([]);
            foreach ($contractsByCurrency as $currencyId => $contracts) {
                // Getting currency data
                $contractCurrency = $contracts->first()->currency;
                if (!$contractCurrency) {
                    Log::warning($source . 'Currency not found for contract: ' . $contracts->first()->id);

                    continue;
                }
                if ($contractCurrency->id === $companyCurrency->id) {
                    $exchangeRate = 1;
                } else {
                    $rate = CurrencyService::getClosestRateToDate($companyCurrency->key, $contractCurrency->key);
                    $exchangeRate = $rate ? $rate->rates[$contractCurrency->key] : 1;
                }
                $currency = [
                    'currency_id' => $currencyId,
                    'currency_key' => $contractCurrency->key,
                    'currency_name' => $contractCurrency->name,
                    'currency_symbol' => $contractCurrency->symbol,
                    'exchange_rate' => $exchangeRate,
                    'total_cost' => 0,
                    'total_breakdown_cost' => 0,
                    'total_contracts' => $contracts->count(),
                ];
                if ($audit) {
                    $currency['breakdown_payments'] = [];
                }

                // Getting total of payments for the currency
                $totalBaseCost = 0;
                $totalBreakdownCost = 0;
                foreach ($contracts as $contract) {
                    if ($audit) {
                        $currency['breakdown_payments'][$contract->id] = [];
                    }
                    foreach ($contract->nextPaymentDates as $payment) {
                        $exchangeRateToUse = ($payment->exchange_rate ?? $exchangeRate) ?: 1;
                        $convertedValue = $payment->cost / $exchangeRateToUse;
                        $totalBaseCost += $convertedValue;
                        $totalBreakdownCost += $payment->cost;
                        // Flag to audit values used in the calculations
                        if ($audit) {
                            $currency['breakdown_payments'][$contract->id][] = [
                                'id' => $payment->id,
                                'date' => $payment->date->format('Y-m-d'),
                                $contractCurrency->key => $payment->cost,
                                $companyCurrency->key => $convertedValue,
                                'exchange_rate' => $exchangeRateToUse,
                                'exchange_rate_date' => $payment->exchange_rate_date ?? $rate?->last_rate_update ?? 'current',
                            ];
                        }
                    }
                }
                $currency['total_cost'] = $totalBaseCost;
                $currency['total_breakdown_cost'] = $totalBreakdownCost;
                // Sum the contract payment values to the category's total when contract has the same currency as company
                $totalCategoryCost += $totalBaseCost;

                $currencies->push((object)$currency);
            }
            $categories->push((object)[
                'category_id' => $categoryId ?? null,
                'category_name' => $category?->name ?? 'Uncategorized',
                'category_color' => $category?->color ?? null,
                'total_cost' => $totalCategoryCost,
                'total_contracts' => $totalCategoryContracts,
                'currencies' => $currencies,
            ]);
        }
        $response = $categories->sortBy('category_name');

        return $response;
    }

    /**
     * This service takes a collection of contracts and its productContracts and creates a single contracts collection
     */
    public static function prepareContractsAsIndependentElements(Collection $contracts): Collection
    {
        $allContracts = collect();
        $contracts->each(function ($contract) use (&$allContracts) {
            $allContracts = $allContracts->merge($contract->productContracts);
            $contract->unsetRelation('productContracts');
            $allContracts->push($contract);
        });

        return $allContracts->filter();
    }

    /**
     * Set the value of the company_id field based on contract data, vendor_id and distributor_id.
     *
     * @param  array  $contractData  The contract data.
     * @return array The modified contract data.
     */
    public static function setNameAndCompanyIDValue(array &$contractData, ?int $currentId = null): void
    {
        $vendor = Company::find($contractData['company_id'] ?? null);
        $contractData['name'] = $contractData['name'] ?? $vendor?->name ?? '';
        $contractData['company_id'] = $contractData['company_id'] ?? $currentId;
    }

    /**
     * Applies currency rules to contract data.
     *
     * Ensures the contract has a valid exchange rate and determines
     * whether it uses a fixed conversion rate based on the company's currency.
     *
     * @param  array  $contractData  Reference to the contract data array.
     * @param  Company  $company  The company associated with the contract.
     */
    public static function applyCurrencyRules(
        array &$contractData,
        Company $company
    ): void {
        $source = __CLASS__ . '::' . __FUNCTION__ . '::';
        Log::debug($source . 'Applying currency rules to contract');
        $companyCurrency = $company->configurations->currency_id;
        $contractCurrency = $contractData['currency_id'] ?? $companyCurrency;
        if (!isset($contractData['custom_properties'])) {
            $contractData['custom_properties'] = [];
        }
        $contractData['custom_properties']['company_currency'] = $company->configurations->currency->key;
        $hasFixedRate = $companyCurrency == $contractCurrency || (bool)($contractData['has_fixed_rate'] ?? 0);
        $exchangeRate = $companyCurrency == $contractCurrency
            ? 1
            : ($hasFixedRate ? $contractData['exchange_rate'] ?? null : null);

        $contractData['exchange_rate'] = $exchangeRate;
        $contractData['has_fixed_rate'] = $hasFixedRate;
    }

    /**
     * Set the value of the contract_type_id field based on $contractTypeKey value.
     *
     * @param  array  $contractData  The contract data.
     * @param  string  $contractTypeKey  The key used to find correct contract type.
     * @param  bool  $throwsErrorOnFail  Indicates if a Validation Exception should be thrown if the ContractType is not found.
     * @return array The modified contract data.
     *
     * @throws ValidationException
     */
    public static function setContractTypeIdByKey(
        array &$contractData,
        string $contractTypeKey,
        ?bool $throwsErrorOnFail = true
    ): void {
        $contractType = ContractType::firstWhere('key', $contractTypeKey);
        if (!$contractType) {
            if ($throwsErrorOnFail) {
                throw ValidationException::withMessages(['The contract type ' . $contractTypeKey . ' was not found.']);
            }
        }
        $contractData['contract_type_id'] = $contractType->id;
    }

    /**
     * @throws ValidationException
     */
    private static function addBillingFrequencyToDate($billingFrequency, $newDate)
    {
        $newDate = match ($billingFrequency) {
            BillingFrequencyEnum::weekly => $newDate->addWeek()->clone()->endOfDay(),
            BillingFrequencyEnum::biweekly => $newDate->addWeeks(2)->clone()->endOfDay(),
            BillingFrequencyEnum::monthly => $newDate->addMonth()->clone()->endOfDay(),
            BillingFrequencyEnum::bimonthly => $newDate->addMonths(2)->clone()->endOfDay(),
            BillingFrequencyEnum::quarterly => $newDate->addQuarter()->clone()->endOfDay(),
            BillingFrequencyEnum::biquarterly => $newDate->addQuarters(2)->clone()->endOfDay(),
            BillingFrequencyEnum::yearly => $newDate->addYear()->clone()->endOfDay(),
            BillingFrequencyEnum::onetimepayment => $newDate->clone()->endOfDay(),
            default => throw ValidationException::withMessages([
                'ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::Logic not implemented for: ' . $billingFrequency,
            ])
        };

        return $newDate;
    }

    /**
     * This function calculates the billing frequency factor
     *
     * @throws ValidationException
     */
    private static function calculateBillingFrequencyFactor(
        string $contractAgreement, int $contractAgreementFactor, string $billingFrequency): float
    {
        $billingFrequencyFactor = $contractAgreement === ContractAgreementEnum::NOTERM
            ? 1
            : self::calculateBillingFrequencyFactorWithTerm($billingFrequency, $contractAgreement, $contractAgreementFactor);

        return $billingFrequencyFactor;
    }

    /**
     * @throws ValidationException
     */
    private static function calculateBillingFrequencyFactorWithTerm(
        string $billingFrequency, string $contractAgreement, int $contractAgreementFactor): float
    {
        $billingFrequencyFactor = match ($contractAgreement) {
            ContractAgreementEnum::MONTHLY => match ($billingFrequency) {
                BillingFrequencyEnum::weekly => 4,
                BillingFrequencyEnum::biweekly => 2,
                BillingFrequencyEnum::monthly, BillingFrequencyEnum::onetimepayment => 1,
                default => throw ValidationException::withMessages([
                    'ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::Logic not implemented for: ' . $billingFrequency,
                ])
            },
            ContractAgreementEnum::QUARTERLY => match ($billingFrequency) {
                BillingFrequencyEnum::weekly => 13,
                BillingFrequencyEnum::biweekly => 7,
                BillingFrequencyEnum::monthly => 3,
                BillingFrequencyEnum::bimonthly => 2,
                BillingFrequencyEnum::quarterly, BillingFrequencyEnum::onetimepayment => 1,
                default => throw ValidationException::withMessages([
                    'ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::Logic not implemented for: ' . $billingFrequency,
                ])
            },
            default => match ($billingFrequency) {
                BillingFrequencyEnum::weekly => 52,
                BillingFrequencyEnum::biweekly => 26,
                BillingFrequencyEnum::monthly => 12,
                BillingFrequencyEnum::bimonthly => 6,
                BillingFrequencyEnum::quarterly => 4,
                BillingFrequencyEnum::biquarterly => 2,
                BillingFrequencyEnum::yearly => 1,
                BillingFrequencyEnum::onetimepayment => match ($contractAgreement) {
                    ContractAgreementEnum::TWOYEAR => 2,
                    ContractAgreementEnum::THREEYEAR => 3,
                    ContractAgreementEnum::FOUREYEAR => 4,
                    ContractAgreementEnum::FIVEYEAR => 5,
                    default => 1,
                },
                default => throw ValidationException::withMessages([
                    'ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::Logic not implemented for: ' . $billingFrequency,
                ])
            }
        };
        $billingFrequencyFactor *= $contractAgreementFactor;

        return $billingFrequencyFactor;
    }

    /**
     * Handles the upload of a contracts CSV file, validates the file"s structure,
     * and dispatches a job to process the file asynchronously.
     *
     * @param  Company  $company  The company to which the contracts belong.
     * @param  User  $user  The user performing the upload.
     * @param  FormRequest  $request  The request containing the uploaded file information.
     * @return array Returns an array with custom properties indicating the status of the file processing.
     *
     * @throws ValidationException If the file is missing or has an invalid structure.
     */
    public static function importContractsCSVUploadHandler(
        Company $company,
        User $user,
        FormRequest $request
    ): array {
        $source = __CLASS__ . '::' . __FUNCTION__ . '::';
        $sessionId = $request->session_id;
        $context = $request->context;
        $path = $request->received_file_info['path'] ?? null;
        $fileName = $request->received_file_info['name'] ?? null;
        $fileId = $request->dzuuid ?? null;
        if (!$path || !$fileName) {
            Log::error($source . 'Error receiving file. file path or name are missing. Path = ' . $path . ', Name = ' . $fileName);

            throw ValidationException::withMessages([
                config('genericMessages.error.INVALID_CSV_FILE'),
            ]);
        }
        // Checking file data structure
        Log::debug($source . 'Parsing and validating csv');
        // Validating columns names and sequence
        CSVHelper::validateColumns($path, $fileName, CSVColumnsEnum::importContracts);

        Log::debug($source . 'Dispatching Job: ProcessImportContractsFromCSVJob');
        ProcessImportContractsFromCSVJob::dispatch($sessionId, $context, $company, $user, $path, $fileName, $fileId);

        // Updating file status to "processing" in File Status Summary
        return [
            'status' => FileUploadSummaryStatus::processing,
            'message' => config('genericMessages.error.FILE_WILL_BE_PROCESSED'),
        ];
    }
}
