<?php

namespace App\Services\Currency;

use App\Models\Currency;
use App\Models\CurrencyExchangeRate;
use AshAllenDesign\LaravelExchangeRates\Classes\ExchangeRate;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Throwable;

class CurrencyService
{
    /**
     * Load currencies based on optional keys or search word criteria
     *
     * @param  array|null  $keys  Array of currency keys to filter.
     * @param  string|null  $searchWord  Keyword for searching currencies by key, name, or description.
     * @return Builder Query builder instance for the filtered currencies.
     */
    public static function loadAllCurrencies(
        ?array $keys = null,
        ?string $searchWord = null
    ): Builder {
        $response = Currency::when(!empty($keys) && count($keys) > 0, function ($q) use ($keys) {
            $q->whereIn('key', $keys);
        })->when(!empty($searchWord), function ($q) use ($searchWord) {
            $q->where(function ($query) use ($searchWord) {
                $searchLikeParameter = '%' . strtolower($searchWord) . '%';
                $query->whereRaw('lower(key) LIKE ?', [$searchLikeParameter]);
                $query->orWhereRaw('lower(name) LIKE ?', [$searchLikeParameter]);
                $query->orWhereRaw('lower(description) LIKE ?', [$searchLikeParameter]);
            });
        })->select(
            'id', 'key', 'symbol', 'name', 'description', 'order', 'created_at', 'updated_at'
        );

        return $response;
    }

    /**
     * Loads exchange rates for the given currency within a specified date range.
     *
     * If no start or end date is provided, it defaults to the current date.
     * Retrieves exchange rates ordered by the most recent update and applies
     * currency symbols to the rates.
     *
     * @param  Currency  $currency  The currency model to load exchange rates into.
     * @param  string|null  $ratesStartDate  Optional start date for filtering exchange rates.
     * @param  string|null  $ratesEndDate  Optional end date for filtering exchange rates.
     */
    public static function loadCurrencyExchangeRates(
        Currency &$currency,
        ?string $ratesStartDate = null,
        ?string $ratesEndDate = null,
    ): void {
        $startDate = $ratesStartDate
            ? Carbon::parse($ratesStartDate)
            : Carbon::now();
        $endDate = $ratesEndDate
            ? Carbon::parse($ratesEndDate)
            : Carbon::now();
        $limit = max(1, $startDate->diffInDays($endDate));
        $currency->load([
            'exchangeRates' => function ($q) use ($startDate, $endDate, $limit) {
                $q->select('id', 'base', 'rates', 'last_rate_update');
                if ($limit === 1) {
                    $q->orderByRaw("abs(date_part('day', last_rate_update - ?))", [$startDate->format('Y-m-d')]);
                } else {
                    $q->whereBetween('last_rate_update', [
                        $startDate->format('Y-m-d 00:00:00'),
                        $endDate->format('Y-m-d 23:59:59'),
                    ]);
                }
                $q->orderBy('last_rate_update', 'DESC')
                    ->limit($limit);
            },
        ]);
        self::applyCurrenciesSymbolsToRates($currency);
    }

    /**
     * Retrieve the exchange rate record with the closest date to the given one,
     * ensuring it contains the target currency in the rates field.
     *
     * @param  string  $base  The base currency.
     * @param  string  $target  The target currency.
     * @param  ?string  $date  The reference date (format: yyyy-mm-dd). If not set, assume the current date
     * @return CurrencyExchangeRate|null The closest exchange rate record or null if not found.
     */
    public static function getClosestRateToDate(
        string $baseCurrency,
        string $targetCurrency,
        ?string $date = null
    ): ?CurrencyExchangeRate {
        $targetDate = $date
            ? Carbon::parse($date)->format('Y-m-d')
            : Carbon::now()->format('Y-m-d');
        $cacheKey = "closest_rate_{$targetDate}_{$baseCurrency}_{$targetCurrency}";

        return Cache::remember($cacheKey, now()->addHours(1), function () use ($cacheKey, $baseCurrency, $targetCurrency, $targetDate) {
            $query = CurrencyExchangeRate::where('base', $baseCurrency)
                ->whereRaw('rates->? is not null', [$targetCurrency])
                ->orderByRaw(
                    'abs(EXTRACT(EPOCH FROM (last_rate_update - ?::timestamp)) / 86400.0) ASC, last_rate_update DESC',
                    [$targetDate]
                );

            $exchangeRate = $query->first();
            if (!$exchangeRate) {
                return null;
            }
            Log::debug(__CLASS__ . '::' . __FUNCTION__ . '::Caching exchange rates for ' . $cacheKey . ' = ' . $exchangeRate?->rates[$targetCurrency] ?? 1);

            return $exchangeRate;
        });
    }

    /**
     * Removes the contracts KPIs cache for a specific company.
     *
     * This method clears the cache of key performance indicators (KPIs)
     * for contracts associated with a company, forcing recalculation on the next request.
     *
     * @param  string  $companyId  The ID of the company whose KPIs cache will be removed
     * @return void
     */
    public static function forgetExchangeRatesCache(string $companyId)
    {
        // Cleanning KPIs cache
        $cacheKey = "contract_kpis_{$companyId}_" . now()->format('Y-m-d');
        Cache::forget($cacheKey);
        // Cleanning Upcoming Renewals cache
        $cacheKey = "contract_upcoming_renewals_{$companyId}_" . now()->format('Y-m-d');
        Cache::forget($cacheKey);
    }

    /**
     * Update currency data, including reordering based on the new order value.
     *
     * @param  Currency  $currency  Currency model to be updated.
     * @param  array  $data  Data to update the currency with.
     */
    public static function updateCurrency(
        Currency $currency,
        array $data
    ): void {
        $currentOrder = $currency->order;
        $currency->fill($data);
        // Reordering data
        if ($currency->isDirty('order')) {
            $minOrder = min($currentOrder, $currency->order);
            $maxOrder = max($currentOrder, $currency->order);
            Currency::where('order', '>=', $minOrder)
                ->where('order', '<=', $maxOrder)
                ->increment('order', $currentOrder > $currency->order ? 1 : -1);
        }
        $currency->save();
    }

    /**
     * Retrieve from database the exchange rate value between two currencies.
     *
     * @param  string  $base  The base currency key.
     * @param  string  $currency  The target currency key.
     * @return float The exchange rate from the base currency to the target currency, or 1 if not found.
     */
    public static function getExchangeRateValue(
        string $base,
        string $currency
    ): float {
        $response = CurrencyExchangeRate::where('base', $base)
            ->whereRaw('rates ? ?', [$currency])
            ->orderBy('last_rate_update', 'DESC')
            ->first();

        return $response->rates[$currency] ?? 1;
    }

    /**
     * Adds currency symbols to exchange rates in the given Currency model.
     *
     * This method transforms the exchange rates by replacing each rate value
     * with an associative array containing the currency symbol and its rate.
     *
     * @param  Currency  $currency  The currency model with exchange rates to be updated.
     */
    public static function applyCurrenciesSymbolsToRates(Currency $currency): void
    {
        $currencySymbols = Currency::pluck('symbol', 'key')->toArray();
        $currency->exchangeRates->transform(function ($item) use ($currencySymbols) {
            $rates = $item->rates;
            foreach ($rates as $key => $rate) {
                $rates[$key] = [
                    'symbol' => $currencySymbols[$key] ?? '$',
                    'rate' => $rate,
                ];
            }
            $item->rates = $rates;

            return $item;
        });
    }

    /**
     * Update exchange rates by fetching from the API based on the API account tier.
     */
    public static function updateExchangeRatesFromAPI(): void
    {
        $isFreeTier = !empty(env('EXCHANGE_RATES_BASE_CURRENCY'));
        if ($isFreeTier) {
            // Free Tier Test Environment
            self::updateExchangeRatesFromBase();
        } else {
            // Paid Prod Environment
            self::updateAllExchangeRates();
        }
    }

    /**
     * Update exchange rates for a PAID API account, iterating over all currency pairs.
     */
    private static function updateAllExchangeRates(): void
    {
        Log::debug(__CLASS__ . '::' . __FUNCTION__ . 'Updating currencies exchange rates for prod environment.');
        $currencies = Currency::select('id', 'key', 'name')->orderBy('key')->get();
        $currencyKeys = $currencies->pluck('key')->toArray();
        $date = Carbon::now()->format('Y-m-d H:i');
        foreach ($currencyKeys as $base) {
            self::updateExchangeRatesForACurrency($base, $date, $currencyKeys);
        }
    }

    /**
     * Update exchange rates for a FREE API account, using predefined settings and base currency.
     *
     * This methods gets all exchange rates from the base currency configured in .env and calculates
     * all other exchange rates based on these ones. Default base currency = EUR
     */
    private static function updateExchangeRatesFromBase(): void
    {
        $source = __CLASS__ . '::' . __FUNCTION__ . '::';
        $base = strtoupper(preg_replace('/[^a-zA-Z]/', '', env('EXCHANGE_RATES_BASE_CURRENCY', '')));
        if (empty($base) || strlen($base) !== 3) {
            Log::error($source . 'EXCHANGE_RATES_BASE_CURRENCY environment variable not set or is invalid');

            return;
        }
        Log::debug($source . 'Updating currencies exchange rates for local/test environment. Base Currency: ' . $base);
        $date = Carbon::now()->format('Y-m-d H:i');
        $currencyKeys = Currency::where('key', '<>', $base)->orderBy('key')->pluck('key')->toArray();
        $rates = self::updateExchangeRatesForACurrency($base, $date, $currencyKeys);
        foreach ($rates as $newBase => $newBaseRate) {
            $currencyRates = [
                $base => 1 / $newBaseRate,
            ];
            foreach ($rates as $currency => $rate) {
                if ($currency === $newBase) {
                    continue;
                }
                $currencyRates[$currency] = $rate / $newBaseRate;
            }
            self::storeExchangeRatesForCurrencies($newBase, $date, $currencyRates);
        }
    }

    /**
     * Fetch and store exchange rates for a given base currency and a list of target currencies.
     *
     * @param  string  $base  Base currency key.
     * @param  string  $date  Exchange rate date
     * @param  array  $currencies  Array of target currency keys.
     */
    private static function updateExchangeRatesForACurrency(
        string $base,
        string $date,
        array $currencies
    ): ?array {
        $source = __CLASS__ . '::' . __FUNCTION__ . '::';
        Log::debug($source . 'Getting exchange rates for Base: ' . $base . ' and currencies: ' . implode(',', $currencies));
        $exchangeRate = app(ExchangeRate::class);

        try {
            $rates = $exchangeRate->exchangeRate($base, $currencies);
            if (empty($rates)) {
                Log::error($source . 'Error getting rates for ' . $base);

                return null;
            }
            foreach ($rates as $currency => $rate) {
                $rates[$currency] = $rate === 0 ? 1 : $rate;
            }
            self::storeExchangeRatesForCurrencies($base, $date, $rates);

            return $rates;
        } catch (Throwable $error) {
            Log::error($source . 'Error updating rates for ' . $base);
            Log::error($error->getMessage());

            return null;
        }
    }

    /**
     * Store exchange rates for currency.
     *
     * @param  string  $base  Base currency key.
     * @param  string  $date  Exchange rate date.
     * @param  array  $rates  List of exchange rates from base to all currencies
     */
    private static function storeExchangeRatesForCurrencies(
        string $base,
        string $date,
        array $rates
    ): void {
        $startDate = Carbon::parse($date)->setTime(0, 0, 0)->format('Y-m-d H:i');
        $endDate = Carbon::parse($date)->setTime(23, 59, 59)->format('Y-m-d H:i');
        $rates[$base] = 1;

        $exchangeRates = CurrencyExchangeRate::where('base', $base)
            ->whereBetween('last_rate_update', [$startDate, $endDate])
            ->first();
        if ($exchangeRates) {
            $exchangeRates->update([
                'last_rate_update' => $date,
                'rates' => $rates,
            ]);
        } else {
            CurrencyExchangeRate::create([
                'base' => $base,
                'last_rate_update' => $date,
                'rates' => $rates,
            ]);
        }
    }
}
