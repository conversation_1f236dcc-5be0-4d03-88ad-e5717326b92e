<?php

namespace App\Services\Email;

use App\Enums\Company\CompanyType;
use App\Enums\Email\EmailBladeFiles;
use App\Models\Company\Company;
use App\Models\Email\Email;
use App\Models\Email\EmailAllowedParameter;
use App\Models\Email\EmailTemplateText;
use App\Services\Company\CompanyWhitelabelingService;
use App\Services\MediaService;
use BenSampo\Enum\Exceptions\InvalidEnumMemberException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use stdClass;

class EmailService
{
    public static function prepareEmailCustomData(
        $bladeFileName,
        $dataBaseIds = [],
        $emailParameters = [],
        $whitelabelingCompanyId = null,
        $requestDomain = '',
        $isDirect = false
    ): stdClass {
        // CREATE AN OBJECT TO RETURN TO BE USED IN THE EMAIL WITH DEFAULT VALUES
        $emailCustomData = new stdClass();
        $emailBladeKey = EmailBladeFiles::getKey($bladeFileName);
        $emailCustomData->subject = config("mail.default_content.{$emailBladeKey}.subject") ?? 'UNSET';
        $emailCustomData->header_text = config("mail.default_content.{$emailBladeKey}.header_text") ?? 'UNSET';
        $emailCustomData->intro_text = config("mail.default_content.{$emailBladeKey}.intro_text") ?? 'UNSET';
        $emailCustomData->footer_text = config("mail.default_content.{$emailBladeKey}.footer_text") ?? 'UNSET';
        $emailCustomData->header_image = $whitelabelingCompanyId || $requestDomain !== '' || $isDirect
            ? 'img/email/bettertracker/logo.png'
            : 'img/email/CP_header_logo.png';

        // FIRST WE WILL LOAD THE CORRESPONDING BLADE DATA
        $email = Email::with('templateTexts', 'allowedParameters')
            ->where('blade_file_name', $bladeFileName)
            ->first();

        // IF NO EMAIL DATA IS LOADED, WE RETURN THE UNSET DATA
        if (!$email) {
            Log::debug('ERROR::PEC001::NO EMAIL DATA SET TO BLADE FILE::' . $bladeFileName);

            return $emailCustomData;
        }

        // NOW WE SELECT A RANDOM TEMPLATE TEXTS, IF THE TEMPLATE TEXTS HAVE DEFAULT ONES, THEN WE SELECT
        // A RANDOM ONE FROM THEM, IF THERE AREN'T DEFAULT ONES, WE SELECT A RANDOM ONE FROM THE REST
        if (!$email->templateTexts) {
            Log::debug('ERROR::PEC002::NO EMAIL TEMPLATE TEXT SET TO BLADE FILE::' . $bladeFileName);

            return $emailCustomData;
        }

        $customizedTemplateText = null;
        if ($whitelabelingCompanyId) {
            $company = Company::findOrFail($whitelabelingCompanyId);
            // we do the logic of the whitelabeling only for MSP customers
            if ($whitelabelingCompanyId || $company->enumType->value === CompanyType::MSP_CLIENT) {
                try {
                    $whitelabelingCompany = CompanyWhitelabelingService::validateCompanyCanHaveWhitelabeling($company);
                    $emailCustomData->subdomain = $whitelabelingCompany->subdomain;
                    $emailCustomData->header_image = 'img/email/bettertracker/logo.png';
                    if ($whitelabelingCompany->whitelabeling) {
                        if ($whitelabelingCompany->whitelabeling->emailHeader) {
                            $emailCustomData->header_image = Storage::temporaryUrlForDisk(
                                $whitelabelingCompany->whitelabeling->emailHeader->getPath(),
                                MediaService::getExpirationTime(),
                                $whitelabelingCompany->whitelabeling->emailHeader->disk);
                        }
                    }
                    $customizedTemplateText = $email->customizedTemplateTexts()
                        ->where('company_id', $whitelabelingCompany->id)
                        ->first();
                } catch (\Exception $e) {
                    // this means that the company doesn't have whitelabeling, so we do nothing
                }
            }
        }
        $templateTexts = $email->templateTexts;
        if ($customizedTemplateText) {
            $templateTexts->subject = $customizedTemplateText->subject ?? $templateTexts->subject;
            $templateTexts->header_text = $customizedTemplateText->header_text ?? $templateTexts->header_text;
            $templateTexts->intro_text = $customizedTemplateText->intro_text ?? $templateTexts->intro_text;
            $templateTexts->footer_text = $customizedTemplateText->footer_text ?? $templateTexts->footer_text;
        }

        // PREPARE THE PARAMETERS BETWEEN GENERAL PARAMETERS AND ALLOWED PARAMETERS
        $allowedParameters = EmailAllowedParameter::whereNull('email_id')
            ->get()->merge($email->allowedParameters)->keyBy('parameter')->toArray();

        // NOW WE SET THE CORRESPONDING VALUES TO THE EMAIL CUSTOM DATA OBJECT
        self::prepareCustomData(
            $emailBladeKey,
            $emailCustomData,
            $templateTexts,
            $allowedParameters,
            $dataBaseIds,
            $emailParameters,
            $requestDomain,
        );

        return $emailCustomData;
    }

    private static function prepareCustomData(
        $emailBladeKey,
        $emailCustomData,
        $randomTemplateText,
        $allowedParameters,
        $dataBaseIds = [],
        $emailParameters = [],
        $requestDomain = '',
    ): void {
        // here we prepare each of the possible sections for the emails, if you need
        // more sections this is the code where you need to add them

        // THE SUBJECT
        $emailCustomData->subject = self::replaceAllowedParameters(
            $emailBladeKey,
            $randomTemplateText->subject,
            $allowedParameters,
            $dataBaseIds,
            $emailParameters,
            $requestDomain
        );

        if (!empty($randomTemplateText->header_text)) {
            // THE HEADER
            $emailCustomData->header_text = self::replaceAllowedParameters(
                $emailBladeKey,
                $randomTemplateText->header_text,
                $allowedParameters,
                $dataBaseIds,
                $emailParameters,
                $requestDomain
            );
        }

        if (!empty($randomTemplateText->intro_text)) {
            // THE INTRO
            $emailCustomData->intro_text = self::replaceAllowedParameters(
                $emailBladeKey,
                $randomTemplateText->intro_text,
                $allowedParameters,
                $dataBaseIds,
                $emailParameters,
                $requestDomain
            );
        }
        if (!empty($randomTemplateText->footer_text)) {
            // THE FOOTER
            $emailCustomData->footer_text = self::replaceAllowedParameters(
                $emailBladeKey,
                $randomTemplateText->footer_text,
                $allowedParameters,
                $dataBaseIds,
                $emailParameters,
                $requestDomain
            );
        }
    }

    private static function replaceAllowedParameters(
        $emailBladeKey,
        $text,
        $allowedParameters,
        $dataBaseIds = [],
        $emailParameters = [],
        $requestDomain = ''
    ): string {
        // if the request domain is set, we need to replace the channel program values for BetterTracker
        if ($requestDomain !== '') {
            $text = self::replaceCPValueWithBetterTracker($text);
        }
        // we need to get all the possible parameters inside $text
        $parameters = [];
        preg_match_all('/{{(.*?)}}/', $text, $parameters);
        // if there are no $parameters in the text we finish the lookup
        if (count($parameters) === 0) {
            return $text;
        }
        // now we need to remove the {{ }} characters from the $parameters
        $parameters = $parameters[1];
        // after the $parameters are ready, we can search in the $allowedParameters each one and
        // use the $parameter to replace the corresponding value in the $text
        foreach ($parameters as $parameter) {
            if (array_key_exists($parameter, $allowedParameters)) {
                $text = self::replaceTextValueWithParameterValue(
                    $emailBladeKey,
                    $text,
                    (object)$allowedParameters[$parameter],
                    $dataBaseIds,
                    $emailParameters,
                    $requestDomain
                );
            }
        }

        return $text;
    }

    private static function replaceTextValueWithParameterValue(
        $emailBladeKey,
        $text,
        $parameter,
        $dataBaseIds = [],
        $emailParameters = [],
        $requestDomain = ''
    ): string {
        // if parameter is passed in the $emailParameters, we use that value, if not, we use the
        $value = array_key_exists($parameter->parameter, $emailParameters)
            ? $emailParameters[$parameter->parameter] : '';
        if (empty($value)) {
            if (!empty($parameter->value)) {
                // if the $parameter->value has something, it will replace the corresponding value
                // in the $text and finish.
                $value = $parameter->value;
            } elseif (!empty($parameter->table)) {
                // we create the query.
                if ($parameter->table === 'app_configurations') {
                    $result = DB::table($parameter->table)->select(DB::raw($parameter->column . ' AS value'))
                        ->where('key', strtoupper($parameter->parameter))
                        ->first();
                    $value = empty($result) ?
                        '' : $result->value;
                } else {
                    $result = DB::table($parameter->table)->select(DB::raw($parameter->column . ' AS value'))
                        ->whereIn('id', $dataBaseIds)
                        ->first();
                    $value = empty($result) ?
                        '' : $result->value;
                }
            }
        }
        if (empty($value)) {
            Log::info('INFO::PEC003::' . $emailBladeKey . '::PARAMETER::' . $parameter->parameter . '::VALUE NOT FOUND');
        }

        $text = str_replace('{{' . $parameter->parameter . '}}', $value, $text);

        return $requestDomain !== '' ? self::replaceCPValueWithBetterTracker($text) : $text;
    }

    private static function replaceCPValueWithBetterTracker(string $text): string
    {
        $searchPatterns = [
            'Channel Program',
            'ChannelProgram',
            'channel program',
            'channelprogram',
        ];

        // if more domains are added we need to refactor this section of code to have the corresponding logic
        $replaceWith = array_fill(0, count($searchPatterns), 'BetterTracker');

        $text = str_replace($searchPatterns, $replaceWith, $text);

        return $text;
    }

    /**
     * @throws InvalidEnumMemberException
     */
    public static function createBaseEmail(string $emailBladeFile): Email
    {
        $emailBladeKey = EmailBladeFiles::getKey($emailBladeFile);
        $email = Email::updateOrCreate([
            'email_name' => $emailBladeKey,
            'blade_file_name' => $emailBladeFile,
            'description' => config("mail.default_content.{$emailBladeKey}.description") ?? null,
            'display_name' => config("mail.default_content.{$emailBladeKey}.display_name") ?? null,
        ]);
        EmailTemplateText::updateOrCreate([
            'email_id' => $email->id,
            'subject' => config("mail.default_content.{$emailBladeKey}.subject") ?? 'UNSET',
            'default' => true,
            'header_text' => config("mail.default_content.{$emailBladeKey}.header_text") ?? 'UNSET',
            'intro_text' => config("mail.default_content.{$emailBladeKey}.intro_text") ?? 'UNSET',
            'footer_text' => config("mail.default_content.{$emailBladeKey}.footer_text") ?? 'UNSET',
        ]);

        return $email;
    }
}
