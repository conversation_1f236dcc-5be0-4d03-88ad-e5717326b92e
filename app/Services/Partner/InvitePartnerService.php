<?php

namespace App\Services\Partner;

use App\Enums\ActivityLogAction;
use App\Enums\ModelType;
use App\Enums\Partner\PartnerApplicationSource;
use App\Enums\Partner\PartnerCustomerStatus;
use App\Enums\Partner\PartnerPortalInvitationInitiator;
use App\Enums\Partner\PartnerPortalInvitationStatus;
use App\Jobs\StoreUsersNotifications;
use App\Models\Company\Company;
use App\Models\Company\CompanyPartners;
use App\Models\Integrations\VendorCustomer;
use App\Models\MSPFollowingPartner;
use App\Models\User;
use App\Services\ActivityLogs\ActivityLogsService;
use App\Services\AppConfig;
use App\Services\ImageService;
use App\Services\LookupService\LookupService;
use App\Services\MediaService;
use App\Services\ValidationService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;

class InvitePartnerService
{
    /**
     * Accepts an invitation for an MSP to follow a Vendor's portal.
     *
     * This method validates the invitation, checks for existing partnerships,
     * and processes the acceptance.
     *
     * @param  MSPFollowingPartner  $invitation  The invitation being accepted.
     * @param  User  $loggedInUser  The user accepting the invitation.
     * @param  string  $accepted_reason  The reason for accepting the invitation.
     * @param  string|null  $accepted_reason_other  Additional details for the acceptance reason (optional).
     * @param  array|null  $follower_partner_ids  Additional company IDs managed by the user (optional).
     * @return MSPFollowingPartner The updated invitation with the "Accepted" status.
     *
     * @throws ValidationException If the invitation is invalid or a partnership already exists.
     */
    public static function acceptInvitedInvitation(
        MSPFollowingPartner $invitation,
        User $loggedInUser,
        string $accepted_reason,
        ?string $accepted_reason_other = null,
        ?array $follower_partner_ids = []
    ): MSPFollowingPartner {
        $isOpenInvitation = empty($invitation->email);
        $invitationToken = $invitation->token;
        $followerPartnerID = $isOpenInvitation
            ? $loggedInUser->company_id
            : $invitation->follower_partner_id ?? $invitation->invitedUser?->company_id ?? null;
        if (!$followerPartnerID) {
            throw ValidationException::withMessages([
                config('genericMessages.error.FOLLOWER_NOT_FOUND'),
            ]);
        }
        // Check for no-open invitations
        if (!$isOpenInvitation && $invitation->email !== $loggedInUser->email) {
            throw ValidationException::withMessages([
                config('genericMessages.error.PARTNER_INVITATION_INVALID'),
            ]);
        }
        // Validating if the relationship already exists
        self::validateExistingPartnership($invitation->followed_partner_id, $followerPartnerID);

        // Checking if invite is an open invitation
        if ($isOpenInvitation) {
            $invitation = $invitation->replicate();
            $invitation->refreshToken();
            $invitation->email = $loggedInUser->email;
        }
        // Accepting for user's main company
        $invitation->follower_partner_id = $followerPartnerID;
        $invitation->accepted_reason = $accepted_reason;
        $invitation->accepted_reason_other = $accepted_reason_other;
        $invitation->status = PartnerPortalInvitationStatus::Accepted;
        $invitation->accepted_by = $loggedInUser->id;
        $invitation->accepted_at = now();
        $invitation->save();

        $invitation->load([
            'partner:id,name,type,friendly_url,subdomain,is_distributor',
            'partner.avatar',
            'followerPartner:id,name,type,friendly_url,subdomain,is_distributor',
            'followerPartner.avatar',
        ]);

        if ($isOpenInvitation) {
            self::notifyPortalGotNewFollowers($invitation, $loggedInUser, $invitationToken);
        }

        // Requesting access for aditional companies managed by the user
        foreach (($follower_partner_ids ?? []) as $follower_id) {
            [$errorMessages, $companyPartner] = self::sendUsersInvite(
                $invitation->partner,
                $follower_id,
                PartnerPortalInvitationInitiator::User,
                $loggedInUser->id,
                collect([$loggedInUser])
            );
        }
        self::deleteDuplicateInvites($invitation);
        self::appendUserObjectsToCollection(collect([$invitation]));

        return $invitation;
    }

    public static function reSendUsersInvite(string $inviteId): JsonResponse
    {
        $existingInvite = MSPFollowingPartner::findOrFail($inviteId);

        try {
            ValidationService::blackListDomainValidation($existingInvite->email);

            $last_sent = $existingInvite->last_sent_invite_at ? $existingInvite->last_sent_invite_at : $existingInvite->invited_at;
            if ($last_sent->diffInMinutes(now()) < AppConfig::loadAppConfigByKey('INVITE_RESEND_MIN_INTERVAL_MINUTES', 120)->value) {
                return response()->json(['message' => 'This action was performed recently. Please try again later.'], 429);
            }

            $existingInvite->sendInviteNotification();

            $existingInvite->last_sent_invite_at = now();
            $existingInvite->save();

            return response()->json(['message' => 'Invitation resent successfully.']);
        } catch (\Exception $e) {
            $errorMessages[$existingInvite->email] = $e->getMessage();
            Log::debug('ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::' . json_encode($errorMessages));

            return response()->json(['message' => config('genericMessages.error.INVITE_NOT_SENT') . $errorMessages], 500);
        }
    }

    /**
     * Validates whether an MSP and Vendor partnership already exists
     *
     * @param  string  $vendorId  The ID of the Vendor.
     * @param  string  $mspId  The ID of the MSP.
     *
     * @throws ValidationException If the partnership invitation has been accepted or rejected.
     */
    public static function validateExistingPartnership(
        string $vendorId,
        string $mspId
    ): void {
        // Checking for existing invites
        $query = MSPFollowingPartner::select('id','follower_partner_id', 'followed_partner_id', 'status', 'deleted_at')
            ->where([
                'followed_partner_id' => $vendorId,
                'follower_partner_id' => $mspId,
            ])
            ->whereNull('deleted_at');
        $existingInvite = $query->first();
        if ($existingInvite) {
            if ($existingInvite->status === PartnerPortalInvitationStatus::Accepted) {
                throw ValidationException::withMessages([
                    config('genericMessages.error.PARTNER_INVITATION_IS_ALREADY_ACCEPTED'),
                ]);
            }

            if ($existingInvite->status === PartnerPortalInvitationStatus::Rejected) {
                throw ValidationException::withMessages([
                    config('genericMessages.error.PARTNER_INVITATION_IS_REJECTED'),
                ]);
            }
        }
    }

    /**
     * Notifies Vendor's Admins when an MSP accepts an invitation to follow a partner's portal.
     *
     * @param  MSPFollowingPartner  $invitation  The invitation details.
     * @param  User  $loggedInUser  The user who accepted the invitation.
     * @param  string  $invitationToken  The token associated with the invitation.
     */
    public static function notifyPortalGotNewFollowers(
        MSPFollowingPartner $invitation,
        User $loggedInUser,
        string $invitationToken,
    ): void {
        // Adding activity log
        ActivityLogsService::store(
            ActivityLogAction::portalGotNewFollowers,
            ModelType::users,
            $loggedInUser->id,
            ModelType::company_partners,
            $invitation->id,
            [
                'event' => 'Accepted "Invite"',
                'description' => 'MSP accepted the invite with token "' . $invitationToken . '"',
            ]
        );

        // Notifying Super Admins
        $claimers = $invitation->partner->claimers()->get();
        $claimersIDs = $claimers->pluck('id')->toArray();
        foreach ($claimersIDs as $claimerId) {
            Log::error(__CLASS__ . '::' . __FUNCTION__ . '::Notifying user ID: ' . $claimerId);
            $avatar = $invitation->followerPartner->avatar ?
                Storage::temporaryUrlForDisk($invitation->followerPartner->avatar->getPath(), MediaService::getExpirationTime(), $invitation->followerPartner->avatar->disk)
                : null;
            StoreUsersNotifications::dispatch(
                $loggedInUser->id,
                $claimerId,
                User::class,
                'portal-new-follower',
                [
                    'invitation_token' => $invitationToken,
                    'partner' => [
                        'id' => $invitation->partner->id,
                        'name' => $invitation->partner->name,
                        'friendly_url' => $invitation->partner->friendly_url,
                        'subdomain' => $invitation->partner->subdomain,
                    ],
                    'follower' => [
                        'id' => $invitation->followerPartner->id,
                        'name' => $invitation->followerPartner->name,
                        'type' => $invitation->followerPartner->type,
                        'friendly_url' => $invitation->followerPartner->friendly_url,
                        'avatar' => $avatar,
                    ],
                ]
            );
        }
    }

    /**
     * This method receives an invitation and makes the corresponding validations
     * to accept it when is sent from a MSP to a VENDOR
     *
     * @param  array  $inviteReason
     *
     * @throws ValidationException
     */
    public static function acceptRequestedInvitation(
        MSPFollowingPartner $invite,
        User $loggedInUser,
        string $accepted_reason,
        ?string $accepted_reason_other = null,
    ): MSPFollowingPartner {
        return self::updateValidatedInvite(
            $invite,
            $loggedInUser,
            [
                'accepted_reason' => $accepted_reason,
                'accepted_reason_other' => $accepted_reason_other,
            ]
        );
    }

    /**
     * This method receives a validated invitation and updates it in the DB with the
     * new status, the user that accepted it and the follower_partner_id, and returns
     * the model
     *
     *
     * @throws ValidationException
     */
    public static function updateValidatedInvite(
        MSPFollowingPartner $invite,
        User $loggedInUser,
        array $inviteReason = []
    ): MSPFollowingPartner {
        $followerID = $invite->follower_partner_id ?? $invite->invitedUser?->company_id ?? null;
        // Checking if an invite already exists
        $acceptedInvite = MSPFollowingPartner::select(
            'id','follower_partner_id', 'followed_partner_id', 'status', 'initiator',
            'invited_by', 'invited_at', 'accepted_by', 'accepted_at', 'rejected_by', 'rejected_at',
            'rejected_reason', 'email', 'accepted_reason', 'accepted_reason_other', 'rejected_reason_other',
            'deleted_at', 'source'
        )
            ->where('id', $invite->id)
            ->where('status', PartnerPortalInvitationStatus::Accepted)
            ->whereNull('deleted_at')
            ->first();
        if (!empty($acceptedInvite)) {
            self::deleteDuplicateInvites($acceptedInvite);

            throw ValidationException::withMessages([
                config('genericMessages.error.PARTNER_INVITATION_IS_ALREADY_ACCEPTED'),
            ]);
        }
        foreach ($inviteReason as $key => $value) {
            $invite->{$key} = $value;
        }
        $followerID = null;
        if ($invite->follower_partner_id !== null) {
            $followerID = $invite->follower_partner_id;
        } else if ($invite->invitedUser?->company_id !== null) {
            $followerID = $invite->invitedUser->company_id;
        }

        $invite->status = PartnerPortalInvitationStatus::Accepted;
        $invite->accepted_by = $loggedInUser->id;
        $invite->accepted_at = now();
        $invite->follower_partner_id = $followerID;
        $invite->save();
        $invite->load('partner', 'followerPartner');
        self::deleteDuplicateInvites($invite);
        self::appendUserObjectsToCollection(collect([$invite]));

        return $invite;
    }

    /**
     * append user objects to collection
     */
    public static function appendUserObjectsToCollection($collection)
    {
        $user_ids = $collection->map(function ($item) {
            return $item->invited_by;
        });
        $user_ids = $user_ids->merge($collection->map(function ($item) {
            return $item->accepted_by;
        }));
        $user_ids = $user_ids->merge($collection->map(function ($item) {
            return $item->rejected_by;
        }));
        $user_ids = $user_ids->filter()->unique();
        $users = User::whereIn('id', $user_ids)->get();
        ImageService::appendUsersAvatars($users);
        foreach ($collection as $item) {
            $item->follower_user = $users->where('id', $item->follower_partner_id)->first();
            $item->invitedBy = $users->where('id', $item->invited_by)->first();
            $item->acceptedBy = $users->where('id', $item->accepted_by)->first();
            $item->rejectedBy = $users->where('id', $item->rejected_by)->first();
        }

        return $collection;
    }

    /**
     * append avatars to company collection
     */
    public static function appendCompanyAvatar($collection)
    {
        $companyCollection = $collection->map(function ($item) {
            return $item->followerPartner;
        });
        $companyCollection = $companyCollection->merge($collection->map(function ($item) {
            return $item->partner;
        }));
        $avatarCollection = ImageService::appendAvatars($companyCollection);
        $collection->transform(function ($partner) use ($avatarCollection) {
            if ($partner->followerPartner) {
                $partner->followerPartner->avatar = $avatarCollection
                    ->where('id', $partner->follower_partner_id)
                    ->first()?->avatar;
            }

            return $partner;
        });

        return $collection;
    }

    /**
     * This method receives $invite and delete all the invited except that one
     */
    public static function deleteDuplicateInvites(MSPFollowingPartner $invite): void
    {
        $companyUserEmails = $invite->followerPartner?->users?->pluck('email');
        if ($companyUserEmails) {
            MSPFollowingPartner::where('follower_partner_id', $invite->follower_partner_id)
                ->where('followed_partner_id', $invite->followed_partner_id)
                ->where('id', '<>', $invite->id)
                ->whereNull('deleted_at')
                ->update(['deleted_at' => now()]);
        }
    }

    public static function fetchUsersFromEmail(array $emails)
    {
        $users = User::whereIn('email', $emails)
            ->select('id', 'email', 'company_id')
            ->get();
        $emails = array_diff($emails, $users->pluck('email')->toArray());
        foreach ($emails as $email) {
            $users->push((object)['email' => $email]);
        }

        return $users;
    }

    /**
     * Send invite to users
     *
     * @throws ValidationException
     */
    public static function sendUsersInvite(
        Company $vendor,
        string $mspCompanyId,
        string $initiator,
        string $initiatorId,
        $users,
        $applicationSource = null
    ): array {
        $source = __CLASS__ . '::' . __FUNCTION__ . '::';
        Log::debug($source . 'Entering method');
        $errorMessages = [];
        $companyPartner = [];
        foreach ($users->filter() as $user) {
            $follower_partner_id = $initiator === PartnerPortalInvitationInitiator::User
                ? $mspCompanyId
                : $user->company_id ?? null;
            Log::debug($source . 'User to be invited ' . $user->email);
            if (!$user instanceof User) {
                try {
                    ValidationService::blackListDomainValidation($user->email);
                    ValidationService::validateEmailAddress($user->email);
                } catch (\Exception $e) {
                    $errorMessages[$user->email] = $e->getMessage();

                    continue;
                }
            }
            // Checking for existing invites
            $query = MSPFollowingPartner::select(
                'id','follower_partner_id', 'followed_partner_id', 'status', 'initiator',
                'invited_by', 'invited_at', 'accepted_by', 'accepted_at', 'rejected_by', 'rejected_at',
                'rejected_reason', 'email', 'accepted_reason', 'accepted_reason_other', 'rejected_reason_other',
                'deleted_at', 'source'
            )
                ->where([
                    'followed_partner_id' => $vendor->id,
                    'follower_partner_id' => $follower_partner_id,
                    'email' => $user->email,
                ])
                ->whereNull('deleted_at');
            $existingInvite = $query->first();
            if ($existingInvite) { // An invitation already exists for these companies
                Log::debug($source . 'Invite already exists ');
                if ($existingInvite->status === PartnerPortalInvitationStatus::Accepted) {
                    Log::debug($source . 'Already following company');
                    $errorMessages[$user->email] = config('genericMessages.error.MSP_ALREADY_PARTNER');
                } else if ($existingInvite->status === PartnerPortalInvitationStatus::Rejected) {
                    Log::debug($source . 'Invitation is rejected');
                    $errorMessages[$user->email] = config('genericMessages.error.PARTNER_INVITATION_IS_REJECTED');
                } else {
                    Log::debug($source . 'NOT following company. Status: ' . $existingInvite->status);
                    $errorMessages[$user->email] = config('genericMessages.error.MSP_ALREADY_INVITED_REQUESTED_TO_PORTAL');
                    $companyPartner[] = $existingInvite;
                    if ($existingInvite->initiator !== $initiator) {
                        $defaultAcceptReasonId = LookupService::getLookupOptionValueId(
                            'partner_invitation_accepting_reasons',
                            config('custom.prm.mutual_accept_reason')
                        );
                        match ($existingInvite->status) {
                            PartnerPortalInvitationStatus::Invited => InvitePartnerService::acceptInvitedInvitation(
                                $existingInvite,
                                $user,
                                $defaultAcceptReasonId
                            ),
                            PartnerPortalInvitationStatus::Requested => InvitePartnerService::acceptRequestedInvitation(
                                $existingInvite,
                                $user,
                                $defaultAcceptReasonId
                            ),
                            default => throw ValidationException::withMessages([
                                ['ERROR::' . $source . '::Logic not implemented for::' . $existingInvite->status],
                            ])
                        };
                    }
                }
            } else {
                Log::debug($source . 'Invite does NOT exists ');
                $status = match ($initiator) {
                    PartnerPortalInvitationInitiator::Partner => PartnerPortalInvitationStatus::Invited,
                    PartnerPortalInvitationInitiator::User => PartnerPortalInvitationStatus::Requested,
                    default => throw ValidationException::withMessages(
                        ['ERROR::' . $source . '::Logic not implemented for type::' . $initiator]
                    )
                };
                Log::debug($source . 'Creating new invite ');
                $inviteData = [
                    'followed_partner_id' => $vendor->id,
                    'follower_partner_id' => $follower_partner_id,
                    'status' => $status,
                    'initiator' => $initiator,
                    'invited_by' => $initiatorId,
                    'invited_at' => Carbon::now(),
                    'email' => $user->email,
                    'source' => $applicationSource,
                ];
                $companyPartner[] = MSPFollowingPartner::create($inviteData);
                if ($applicationSource === PartnerApplicationSource::HUBSPOT ||
                    $applicationSource === PartnerApplicationSource::SALESFORCE) {
                    VendorCustomer::where('cp_company_id', $vendor->id)
                        ->where('customer_email', $user->email)
                        ->update([
                            'status' => PartnerCustomerStatus::SendInvitation,
                        ]);
                }
            }
        }

        return [$errorMessages, $companyPartner];
    }

    /**
     * Retrieve or create an open invitation for a specific company.
     *
     * @param  Company  $company  The company for which to retrieve or create an open invitation.
     * @return CompanyPartners The open invitation instance.
     */
    public static function getOpenInvitation(Company $company): CompanyPartners
    {
        $invite = CompanyPartners::firstOrCreate([
            'followed_partner_id' => $company->id,
            'email' => null,
            'status' => PartnerPortalInvitationStatus::Invited,
            'source' => PartnerApplicationSource::SharedLink,
        ]);
        $invite->load('followed:id,name,subdomain');

        return $invite;
    }

    /**
     * Refresh an open invitation for a specific company.
     *
     * @param  Company  $company  The company for which to refresh the open invitation.
     * @return CompanyPartners The refreshed invitation instance.
     */
    public static function refreshOpenInvitation(Company $company): CompanyPartners
    {
        $currentInvite = CompanyPartners::firstWhere([
            'followed_partner_id' => $company->id,
            'email' => null,
            'status' => PartnerPortalInvitationStatus::Invited,
        ]);
        if ($currentInvite) {
            $currentInvite->update([
                'status' => PartnerPortalInvitationStatus::Rejected,
            ]);
        }

        return self::getOpenInvitation($company);
    }

    public static function updateAcceptedInvitationPartnership(
        MSPFollowingPartner $invite, array $inviteReason = []): MSPFollowingPartner
    {
        return self::updateValidatedPartnership($invite, $inviteReason);
    }

    public static function updateValidatedPartnership(MSPFollowingPartner $invite, $inviteReason): MSPFollowingPartner
    {
        $invite->accepted_reason = $inviteReason['accepted_reason'];
        $invite->accepted_reason_other = $inviteReason['accepted_reason_other'] ?? null;
        $invite->save();
        $invite->load('partner', 'followerPartner');
        self::appendUserObjectsToCollection(collect([$invite]));

        return $invite;
    }
}
