<?php

namespace App\Services\Permission;

use App\Enums\ActivityLogAction;
use App\Enums\Cache\CacheTTLEnum;
use App\Enums\ModelType;
use App\Jobs\SendClaimerWelcomeMail;
use App\Models\Company\Company;
use App\Models\Company\CompanyClaimer;
use App\Models\Permission\Permission;
use App\Models\Permission\Role\Role;
use App\Models\Permission\Role\RoleUser;
use App\Models\User;
use App\Services\ActivityLogs\ActivityLogsService;
use App\Services\AuthService;
use BenSampo\Enum\Exceptions\InvalidEnumMemberException;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class PermissionService
{
    /**
     * Transforms a route name from the format "METHOD:path/to/route" to "method_path_to_route"
     *
     * @param  string  $routeName  The route name in the format "METHOD:path/to/route"
     * @return string The transformed route name in the format "_method_path_to_route"
     */
    public static function transformRouteNameToKey(string $routeName): string
    {
        // Split the route name by colon to separate method and path
        $parts = explode(':', $routeName, 2);

        if (count($parts) !== 2) {
            return strtolower($routeName);
        }

        $method = strtolower($parts[0]);
        $path = $parts[1];

        // Replace slashes with underscores
        $path = str_replace('/', '_', $path);

        // Remove any leading or trailing underscores
        $path = trim($path, '_');

        // Combine method and path with underscores
        return $method . '_' . $path;
    }

    /**
     * Get a role by ID with caching
     */
    public static function getPermissionByTitle(string $title): ?Permission
    {
        $key = PermissionService::transformRouteNameToKey($title);
        $cacheKey = "permission_{$key}";

        return Cache::remember($cacheKey, now()->addSeconds(CacheTTLEnum::CACHE_TTL_WEEK),
            function () use ($title) {
                $permission = Permission::select('id', 'title')->where('title', $title)->first();

                return $permission;
            });
    }

    /**
     * Update only the affected permission in the global cache
     */
    public static function updatePermissionInGlobalCache(Permission $permission): void
    {
        $key = PermissionService::transformRouteNameToKey($permission->title);
        $cacheKey = "permission_{$key}";
        // Only invalidate caches related to this specific role and company
        // Clear specific permission cache
        Cache::forget($cacheKey);
        //RedisService::flushAllRedisPermissionsAndRoles();

        // Log cache invalidation for debugging
        Log::debug("Updated cache for role ID: {$permission->id}");
    }

    /**
     * Receives a template role ID and loads all the roles ids that depend on the template,
     * that have not been updated or been assigned to a user
     *
     * @param  string  $templateRoleId  The template role ID that will be used to find the roles
     * @return Collection $roles The collection of roles found that have not been assigned to a user or been updated
     */
    public static function findNotUpdatedOrAssignedRolesIds(string $templateRoleId): Collection
    {
        $roles = Role::select('roles.id', 'roles.company_id', 'roles.title', 'roles.description', 'roles.key',
            'roles.template_role_id', 'roles.created_at', 'roles.updated_at')
            ->leftJoin('roles_users', 'roles_users.role_id', '=', 'roles.id')
            ->whereColumn('roles.created_at', '=', 'roles.updated_at')
            ->where('roles.template_role_id', $templateRoleId)
            ->whereNull('roles_users.role_id')
            ->get();

        return $roles;
    }

    /**
     * Receives a template role ID and loads all the roles ids that depend on the template,
     * that have not been updated
     *
     * @param  string  $templateRoleId  The template role ID that will be used to find the roles
     * @return Collection $roles The collection of roles found that have not been updated
     */
    public static function findNotUpdatedRolesIds(string $templateRoleId): Collection
    {
        $roles = Role::select('roles.id', 'roles.company_id', 'roles.title', 'roles.description', 'roles.key',
            'roles.template_role_id', 'roles.created_at', 'roles.updated_at')
            ->whereColumn('roles.created_at', '=', 'roles.updated_at')
            ->where('roles.template_role_id', $templateRoleId)
            ->get();

        return $roles;
    }

    /**
     * Receives a user ID and a role and assigns the company role to the user
     *
     * @return RoleUser $roleUser
     *
     * @throws ValidationException
     */
    public static function initializeUserRole(string $userId, string $roleId): RoleUser
    {
        $roleUser = RoleUser::updateOrCreate(['role_id' => $roleId, 'user_id' => $userId]);

        return $roleUser;
    }

    /**
     * Adds a user as a super admin for a particular company, sends an email notification if the flag is set
     * to true
     *
     * @return void $roleUser
     *
     * @throws ValidationException
     * @throws InvalidEnumMemberException
     */
    public static function addSuperAdminForCompany(
        Company $company, User $user, string $roleId, bool $sendNotification = false): void
    {
        CompanyClaimer::updateOrCreate(['company_id' => $company->id, 'user_id' => $user->id]);
        self::initializeUserRole($user->id, $roleId);
        if ($sendNotification) {
            SendClaimerWelcomeMail::dispatch($user, $company->name, $company->enumType->type_is_of_vendor);
        }
        ActivityLogsService::store(ActivityLogAction::addedClaimer,
            ModelType::userType, AuthService::getLoggedInUserId() ?? $user->id,
            ModelType::companies, $company->id, ['claimer_id' => '' . $user->id]);
    }

    /**
     * Assigns a role to a user based on the role key and company type.
     *
     * @param  Company  $company  The company to search for the role.
     * @param  User  $user  The user to assign the role to.
     * @param  string  $roleKey  The key of the role to be assigned.
     *
     * @throws ValidationException If the role is not found for the given company and key.
     */
    public static function assignRoleToUser(
        Company $company,
        User $user,
        string $roleKey
    ): void {
        $role = Role::select('id')->whereHas('templateRole', function ($q) use ($company, $roleKey) {
            $q->whereHas('companyTypes', function ($q) use ($company) {
                $q->where('company_type_id', $company->type);
            })
                ->where('key', $roleKey);
        })->where('company_id', $company->id)->first();
        if (empty($role)) {
            throw ValidationException::withMessages(['Role not found.']);
        }
        RoleUser::updateOrCreate([
            'role_id' => $role->id,
            'user_id' => $user->id,
        ]);
    }
}
