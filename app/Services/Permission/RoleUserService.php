<?php

namespace App\Services\Permission;

use App\Enums\ActivityLogAction;
use App\Enums\ModelType;
use App\Models\Company\Company;
use App\Models\Company\CompanyClaimer;
use App\Models\Permission\Role\RoleUser;
use App\Models\User;
use App\Services\ActivityLogs\ActivityLogsService;
use App\Services\AuthService;
use App\Services\Company\CompanyClaimerResponseService;
use App\Services\Company\CompanyService;
use Illuminate\Validation\ValidationException;

class RoleUserService
{
    public static function userReachedLimitOfRolesByCompany(User $user, Company $company): bool
    {
        return $user->roles()->where('company_id', $company->id)->count() >= 2;
    }

    /*
     * CP Roles:
     *   - Users can have multiple Roles
     *   - The specific role should be updated instead of removing all
     * Others Companies:
     *   - Only one role per company
     *   - On Update or Create  the previous roles should be removed
     */
    public static function updateUserRoleByCompany(
        Company $company, string $roleId, User $user, ?string $prevRoleId = null): void
    {
        if ((string)$company->superAdminRole()->id === $roleId) {
            CompanyClaimerResponseService::addClaimer($company, $user, false, $roleId);
        } else {
            CompanyClaimer::select('id', 'company_id', 'user_id')
                ->where('company_id', $company->id)->where('user_id', $user->id)
                ->delete();
        }

        if (CompanyService::companyIsCP($company)) {
            if ($prevRoleId) {
                RoleUser::where('role_id', $prevRoleId)->where('user_id', $user->id)->update(['role_id' => $roleId]);
            } else {
                if (self::userReachedLimitOfRolesByCompany($user, $company)) {
                    throw ValidationException::withMessages([config('genericMessages.error.USER_ROLE_LIMIT')]);
                }
                PermissionService::initializeUserRole($user->id, $roleId);
            }
        } else {
            $companyRoles = $company->roles()->pluck('id')->toArray();
            RoleUser::whereIn('role_id', $companyRoles)->where('user_id', $user->id)->delete();
            PermissionService::initializeUserRole($user->id, $roleId);
        }
    }

    public static function deleteUserRoleByCompany(Company $company, string $userId, ?string $roleId = null): void
    {
        if (CompanyService::companyIsCP($company)) {
            // Delete specific role for CP company
            RoleUser::where('role_id', $roleId)
                ->where('user_id', $userId)
                ->delete();
        } else {
            $companyRoles = $company->roles()->pluck('id')->toArray();
            // We delete all the user roles from the company
            RoleUser::whereIn('role_id', $companyRoles)
                ->where('user_id', $userId)
                ->delete();

            // We assure that the company claimer for the user in the company is also deleted
            CompanyClaimer::select('id', 'company_id', 'user_id')
                ->where('company_id', $company->id)->where('user_id', $userId)
                ->delete();
        }

        ActivityLogsService::store(
            ActivityLogAction::removedUser,
            ModelType::userType,
            AuthService::getLoggedInUserId(),
            ModelType::companies,
            $company->id,
            ['user_id' => $userId, 'company_id' => '' . $company->id]
        );
    }
}
