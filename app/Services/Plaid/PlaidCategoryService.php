<?php

namespace App\Services\Plaid;

use App\Enums\Category\CategorySectionTypes;
use App\Models\Category\Category;
use App\Models\Category\Section\CategorySection;
use App\Models\Company\Company;
use App\Models\VendorExpenseCategoryMapping;
use Illuminate\Support\Facades\Log;

class PlaidCategoryService
{
    private const DEFAULT_CATEGORY = 'Software & Technology';

    private const CATEGORY_MAP = [
        'Other Expenses' => [
            'TRANSFER_OUT_WITHDRAWAL',
            'TRANSFER_OUT_OTHER_TRANSFER_OUT',
            'GOVERNMENT_AND_NON_PROFIT_DONATIONS',
            'GOVERNMENT_AND_NON_PROFIT_GOVERNMENT_DEPARTMENTS_AND_AGENCIES',
            'GOVERNMENT_AND_NON_PROFIT_TAX_PAYMENT',
            'GOVERNMENT_AND_NON_PROFIT_OTHER_GOVERNMENT_AND_NON_PROFIT',
        ],
        'Bank Fees' => [
            'BANK_FEES_ATM_FEES',
            'BANK_FEES_FOREIGN_TRANSACTION_FEES',
            'BANK_FEES_INSUFFICIENT_FUNDS',
            'BANK_FEES_INTEREST_CHARGE',
            'BANK_FEES_OVERDRAFT_FEES',
            'BANK_FEES_OTHER_BANK_FEES',
        ],
        'Business Services' => [
            'GENERAL_SERVICES_ACCOUNTING_AND_FINANCIAL_PLANNING',
            'GENERAL_SERVICES_CHILDCARE',
            'GENERAL_SERVICES_CONSULTING_AND_LEGAL',
        ],
        'Insurance' => [
            'GENERAL_SERVICES_INSURANCE',
        ],
        'Loan Payments' => [
            'LOAN_PAYMENTS_CAR_PAYMENT',
            'LOAN_PAYMENTS_CREDIT_CARD_PAYMENT',
            'LOAN_PAYMENTS_PERSONAL_LOAN_PAYMENT',
            'LOAN_PAYMENTS_MORTGAGE_PAYMENT',
            'LOAN_PAYMENTS_STUDENT_LOAN_PAYMENT',
            'LOAN_PAYMENTS_OTHER_PAYMENT',
        ],
        'Meals & Entertainment' => [
            'ENTERTAINMENT_CASINOS_AND_GAMBLING',
            'ENTERTAINMENT_MUSIC_AND_AUDIO',
            'ENTERTAINMENT_SPORTING_EVENTS_AMUSEMENT_PARKS_AND_MUSEUMS',
            'ENTERTAINMENT_TV_AND_MOVIES',
            'ENTERTAINMENT_VIDEO_GAMES',
            'ENTERTAINMENT_OTHER_ENTERTAINMENT',
            'FOOD_AND_DRINK_BEER_WINE_AND_LIQUOR',
            'FOOD_AND_DRINK_COFFEE',
            'FOOD_AND_DRINK_FAST_FOOD',
            'FOOD_AND_DRINK_GROCERIES',
            'FOOD_AND_DRINK_RESTAURANT',
            'FOOD_AND_DRINK_VENDING_MACHINES',
            'FOOD_AND_DRINK_OTHER_FOOD_AND_DRINK',
        ],
        'Office Supplies' => [
            'GENERAL_MERCHANDISE_BOOKSTORES_AND_NEWSSTANDS',
            'GENERAL_MERCHANDISE_CLOTHING_AND_ACCESSORIES',
            'GENERAL_MERCHANDISE_CONVENIENCE_STORES',
            'GENERAL_MERCHANDISE_DEPARTMENT_STORES',
            'GENERAL_MERCHANDISE_DISCOUNT_STORES',
            'GENERAL_MERCHANDISE_GIFTS_AND_NOVELTIES',
            'GENERAL_MERCHANDISE_OFFICE_SUPPLIES',
            'GENERAL_MERCHANDISE_ONLINE_MARKETPLACES',
            'GENERAL_MERCHANDISE_PET_SUPPLIES',
            'GENERAL_MERCHANDISE_SPORTING_GOODS',
            'GENERAL_MERCHANDISE_SUPERSTORES',
            'GENERAL_MERCHANDISE_TOBACCO_AND_VAPE',
            'GENERAL_MERCHANDISE_OTHER_GENERAL_MERCHANDISE',
            'HOME_IMPROVEMENT_FURNITURE',
            'HOME_IMPROVEMENT_HARDWARE',
            'HOME_IMPROVEMENT_REPAIR_AND_MAINTENANCE',
            'HOME_IMPROVEMENT_SECURITY',
            'HOME_IMPROVEMENT_OTHER_HOME_IMPROVEMENT',
            'GENERAL_SERVICES_POSTAGE_AND_SHIPPING',
        ],
        'Rent & Utilities' => [
            'GENERAL_SERVICES_STORAGE',
            'RENT_AND_UTILITIES_GAS_AND_ELECTRICITY',
            'RENT_AND_UTILITIES_RENT',
            'RENT_AND_UTILITIES_SEWAGE_AND_WASTE_MANAGEMENT',
            'RENT_AND_UTILITIES_TELEPHONE',
            'RENT_AND_UTILITIES_WATER',
            'RENT_AND_UTILITIES_OTHER_UTILITIES',

        ],
        'Training & Education' => [
            'GENERAL_SERVICES_EDUCATION',
        ],
        'Travel' => [
            'TRANSPORTATION_BIKES_AND_SCOOTERS',
            'TRANSPORTATION_GAS',
            'TRANSPORTATION_PARKING',
            'TRANSPORTATION_PUBLIC_TRANSIT',
            'TRANSPORTATION_TAXIS_AND_RIDE_SHARES',
            'TRANSPORTATION_TOLLS',
            'TRANSPORTATION_OTHER_TRANSPORTATION',
            'TRAVEL_FLIGHTS',
            'TRAVEL_LODGING',
            'TRAVEL_RENTAL_CARS',
            'TRAVEL_OTHER_TRAVEL',
        ],
        'Vehicles' => [
            'GENERAL_SERVICES_AUTOMOTIVE',
        ],

        self::DEFAULT_CATEGORY => [
            'GENERAL_MERCHANDISE_ELECTRONICS',
            'GENERAL_SERVICES_OTHER_GENERAL_SERVICES',
            'RENT_AND_UTILITIES_INTERNET_AND_CABLE',
        ],
    ];

    private const INGORE_PLAID_CATEGORIES = [
        'INCOME_DIVIDENDS',
        'INCOME_INTEREST_EARNED',
        'INCOME_RETIREMENT_PENSION',
        'INCOME_TAX_REFUND',
        'INCOME_UNEMPLOYMENT',
        'INCOME_WAGES',
        'INCOME_OTHER_INCOME',
        'TRANSFER_IN_CASH_ADVANCES_AND_LOANS',
        'TRANSFER_IN_DEPOSIT',
        'TRANSFER_IN_INVESTMENT_AND_RETIREMENT_FUNDS',
        'TRANSFER_IN_SAVINGS',
        'TRANSFER_IN_ACCOUNT_TRANSFER',
        'TRANSFER_IN_OTHER_TRANSFER_IN',
        'TRANSFER_OUT_INVESTMENT_AND_RETIREMENT_FUNDS',
        'TRANSFER_OUT_SAVINGS',
        'TRANSFER_OUT_ACCOUNT_TRANSFER',
    ];

    /**
     * @link $data https://plaid.com/docs/api/products/transactions/#transactions-sync-response-added
     */
    public static function isIgnoredPlaidCategory(array $data): bool
    {
        if (!isset($data['personal_finance_category'])) {
            return false;
        }

        return in_array(
            $data['personal_finance_category']['detailed'],
            self::INGORE_PLAID_CATEGORIES,
            true
        );
    }

    /**
     * Get plaid category mapped by our categories in the database
     *
     *  - If vendor company matches with transaction name:
     *     1- Use vendor category map
     *     2- Use self::DEFAULT_CATEGORY if is category empty
     * - If vendor company is not matching:
     *     1- Use map categories that we have on self::CATEGORY_MAP
     *     2- If category is not found on map, use  self::DEFAULT_CATEGORY
     *
     * @link $data https://plaid.com/docs/api/products/transactions/#transactions-sync-response-added
     */
    public static function getPlaidCategoryId(array $data)
    {
        $source = __CLASS__ . '::' . __FUNCTION__;
        $categoryName = self::DEFAULT_CATEGORY;

        $vendorCompanyTransaction = Company::where('name', $data['merchant_name'])
            ->first();

        if ($vendorCompanyTransaction) {
            $categoryMap = VendorExpenseCategoryMapping::select('category_id', 'vendor_id')->where('vendor_id', $vendorCompanyTransaction->id)->first();
            if ($categoryMap) {
                Log::debug($source . '::Adding Plaid Category from vendor category mapping' . 'Vendor ID => ' . $categoryMap->vendor_id . 'Category ID => ' . $categoryMap->category_id);

                return $categoryMap->category_id;
            }
        }

        foreach (self::CATEGORY_MAP as $category => $categoryDetail) {
            if (in_array($data['personal_finance_category']['detailed'], $categoryDetail, true)) {
                $categoryName = $category;
                Log::debug($source . '::Adding plaid category from mapping' . 'Category Map Found => ' . $category . 'Original Plaid Category => ' . $data['personal_finance_category']['detailed']);

                break;
            }
        }

        $category = self::getOrcreatePlaidCategory($categoryName);

        return $category->id;
    }

    private static function getOrcreatePlaidCategory(string $name)
    {
        $plaidSectionId = CategorySection::where('section', CategorySectionTypes::PLAID)->first()->id;

        $category = Category::where('name', $name)->whereHas('sections', function ($query) use ($plaidSectionId) {
            $query->where('category_sections.id', $plaidSectionId);
        })->first();

        if (!$category) {
            $category = Category::create([
                'name' => $name,
                'is_hidden' => true,
            ]);

            $category->sections()->attach($plaidSectionId);
        }

        return $category;
    }
}
