<?php

namespace App\Services\Plaid;

use App\Enums\AppConfigEnum;
use App\Enums\Company\CompanyType as CompanyTypeEnum;
use App\Models\Company\Company;
use App\Models\Contract\Contract;
use App\Models\MyStack\CustomerStack;
use App\Models\MyStack\MyStack;
use App\Models\Plaid\PlaidBankAccount;
use App\Models\Plaid\PlaidSubscription;
use App\Services\AppConfig;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PlaidExpensesService
{
    public static function getExpencesTransactionsDateFormat($query, ?string $year = null, ?array $dateRange = null): Collection
    {
        $collection = $query->selectRaw('EXTRACT(YEAR FROM date) as year, EXTRACT(MONTH FROM date) as month, SUM(amount) as total_expenses')
            ->when($year, function ($query) use ($year) {
                $query->whereYear('date', $year);
            })->when(empty(!$dateRange), function ($query) use ($dateRange) {
                $query->whereBetween('date', $dateRange);
            })->groupByRaw('EXTRACT(YEAR FROM date), EXTRACT(MONTH FROM date)')
            ->orderByRaw('EXTRACT(YEAR FROM date), EXTRACT(MONTH FROM date)')
            ->notIgnored()
            ->get();

        return self::getLabelvalueFromDateResult($collection);
    }

    public static function getLabelvalueFromDateResult(Collection $collection): Collection
    {
        return $collection->map(function ($expense) {
            return [
                'label' => date('F Y', mktime(0, 0, 0, $expense->month, 1, $expense->year)),
                'value' => round($expense->total_expenses, 2),
            ];
        });
    }

    public static function getAllExpenses(Company $company, array $date_range = [])
    {
        return $company->plaidTransactions()
            ->notIgnored()
            ->when(!empty($date_range), function ($query) use ($date_range) {
                $query->whereBetween('plaid_transactions.date', $date_range);
            });
    }

    public static function getMyStackExpenses(Company $company, array $date_range = [])
    {
        return $company->plaidTransactions()
            ->notIgnored()
            ->when(!empty($date_range), function ($query) use ($date_range) {
                $query->whereBetween('plaid_transactions.date', $date_range);
            })->stackOnly();
    }

    public static function getUnmappedExpenses(Company $company, array $dateRange = [])
    {
        return $company->plaidTransactions()
            ->notIgnored()
            ->unmappedExpenses()
            ->when(!empty($dateRange), function ($query) use ($dateRange) {
                $query->whereBetween('plaid_transactions.date', $dateRange);
            });
    }

    public static function getSubscriptionExpenses(Company $company, array $date_range = [])
    {
        return $company->plaidSubscriptions()
            ->join('plaid_transactions', 'plaid_transactions.plaid_subscription_id', '=', 'plaid_subscriptions.id')
            ->notIgnored()
            ->when(!empty($date_range), function ($query) use ($date_range) {
                $query->whereBetween('plaid_transactions.date', $date_range);
            });
    }

    public static function getSubscriptions(Company $company, array $date_range = [])
    {
        return $company->plaidSubscriptions()
            ->select('plaid_subscriptions.*')
            ->notIgnored()
            ->join('plaid_transactions', 'plaid_transactions.plaid_subscription_id', '=', 'plaid_subscriptions.id')
            ->when(!empty($date_range), function ($query) use ($date_range) {
                $query->whereBetween('plaid_transactions.date', $date_range);
            })->groupBy('plaid_subscriptions.id');
    }

    public static function getMyStackSubscriptions(Company $company, array $date_range = [])
    {
        return $company->plaidSubscriptions()
            ->select('plaid_subscriptions.*')
            ->join('plaid_transactions', 'plaid_transactions.plaid_subscription_id', '=', 'plaid_subscriptions.id')
            ->when(!empty($date_range), function ($query) use ($date_range) {
                $query->whereBetween('plaid_transactions.date', $date_range);
            })
            ->notIgnored()
            ->stackOnly()
            ->groupBy('plaid_subscriptions.id');
    }

    public static function getPercentageDiff(?float $previousAmount, ?float $currentAmount): object
    {
        if (is_null($previousAmount) || is_null($currentAmount)) {
            return (object)[
                'previous_amount' => '0',
                'increased' => false,
                'change_percentage' => '0',
            ];
        }

        if ($previousAmount <= 0) {
            $changePercentage = $currentAmount > 0 ? 100 : 0;
        } else {
            $changePercentage = (($previousAmount - $currentAmount) / $previousAmount) * 100;
        }

        $changePercentage = abs($changePercentage) > 100 ? 100 : $changePercentage;

        return (object)[
            'previous_amount' => '' . $previousAmount,
            'increased' => $currentAmount > $previousAmount,
            'change_percentage' => '' . round(abs($changePercentage), 2),
        ];
    }

    public static function getNotLinkedStackContractsBySubCategory(Company $company, $subCategoryId)
    {
        $isMspClient = $company->enumType->value === CompanyTypeEnum::MSP_CLIENT;
        if ($isMspClient) {
            $queryCustomerStack = CustomerStack::select('customer_stack.product_id',
                DB::raw('null as contract_id'),
                DB::raw('null as contract_name'),
                'products.name as product_name',
                'vendor_companies.id as vendor_id',
                'vendor_companies.name as vendor_name',
            )
                ->join('client_products as products', 'products.id', 'customer_stack.product_id')
                ->leftJoin('client_vendors as vendor_companies', 'vendor_companies.id', 'products.owner_id')
                ->where('customer_stack.category_id', $subCategoryId)
                ->where('customer_stack.company_id', $company->id)
                ->groupBy('customer_stack.id', 'vendor_companies.id', 'products.id');

            $queryMyContracts = Contract::select('contracts.client_product_id as product_id',
                'contracts.id as contract_id',
                'contracts.name as contract_name',
                'products.name as product_name',
                'vendor_companies.id as vendor_id',
                'vendor_companies.name as vendor_name',
            )->leftJoin('client_products as products', 'products.id', 'contracts.client_product_id')
                ->leftJoin('companies as vendor_companies', 'vendor_companies.id', 'contracts.client_vendor_id')
                ->where('contracts.category_id', $subCategoryId)
                ->where('contracts.owner_id', $company->id)
                ->groupBy('contracts.id', 'contracts.id', 'vendor_companies.id', 'products.id');

            $query = $queryCustomerStack->union($queryMyContracts);
        } else {
            $queryMyStack = MyStack::select('my_stack.product_id',
                DB::raw('null as contract_id'),
                DB::raw('null as contract_name'),
                'products.name as product_name',
                'vendor_companies.id as vendor_id',
                'vendor_companies.name as vendor_name',
            )
                ->join('products', 'products.id', 'my_stack.product_id')
                ->join('companies as vendor_companies', 'vendor_companies.id', 'products.company_id')
                ->where('my_stack.category_id', $subCategoryId)
                ->where('my_stack.company_id', $company->id)
                ->groupBy('my_stack.id', 'vendor_companies.id', 'products.id');

            $queryMyContracts = Contract::select('contracts.product_id',
                'contracts.id as contract_id',
                'contracts.name as contract_name',
                'products.name as product_name',
                'vendor_companies.id as vendor_id',
                'vendor_companies.name as vendor_name',
            )
                ->join('products', 'products.id', 'contracts.product_id')
                ->join('companies as vendor_companies', 'vendor_companies.id', 'products.company_id')
                ->where('contracts.category_id', $subCategoryId)
                ->where('contracts.owner_id', $company->id)
                ->groupBy('contracts.id', 'contracts.id', 'vendor_companies.id', 'products.id');

            $query = $queryMyStack->union($queryMyContracts);
        }

        return $query;
    }

    /**
     * Get the previous transaction that is already set with the information order by most not null info
     */
    public static function getPreviouTransactionByMerchantEntityId(Company $company, string $merchantEntityId): ?object
    {
        return self::getAllExpenses($company)
            ->where('merchant_entity_id', $merchantEntityId)
            ->where(function ($query) {
                $query->whereNotNull(['category_id', 'sub_category_id', 'product_id', 'contract_id'])
                    ->orWhereNotNull(['category_id', 'sub_category_id', 'product_id'])
                    ->orWhereNotNull(['category_id', 'sub_category_id'])
                    ->orWhereNotNull(['category_id']);
            })
            ->orderByRaw('
                IF(category_id IS NOT NULL, 1, 0) + 
                IF(sub_category_id IS NOT NULL, 1, 0) + 
                IF(product_id IS NOT NULL, 1, 0) + 
                IF(contract_id IS NOT NULL, 1, 0) DESC
            ')
            ->first();
    }

    /**
     * @link $data https://plaid.com/docs/api/products/transactions/#transactions-sync-response-added
     */
    public static function updateorCreateTransaction(array $data, Company $company): void
    {
        $source = __CLASS__ . '::' . __FUNCTION__;

        $merchanName = $data['merchant_name'] ?: $data['name'];
        $description = $data['original_description'] ?? null;

        if ($data['amount'] > 0 && PlaidBankAccount::where('account_id', $data['account_id'])->exists()) {
            $transactionData = [
                'amount' => $data['amount'],
                'date' => $data['date'],
                'iso_currency_code' => $data['iso_currency_code'],
                'merchant_entity_id' => $data['merchant_entity_id'] ?? '',
                'merchant_name' => self::removeBlackListWords($merchanName),
                'description' => self::removeBlackListWords($description),
                'account_id' => $data['account_id'],
                'logo_url' => $data['logo_url'] ?? null,
                'deleted_at' => null,
                'extra_info' => $data['personal_finance_category'],
            ];

            $previousTransaction = self::getPreviouTransactionByMerchantEntityId($company, $data['merchant_entity_id'] ?? '');

            if ($previousTransaction) {
                $transactionData = array_merge($transactionData, [
                    'category_id' => $previousTransaction->category_id ?? null,
                    'sub_category_id' => $previousTransaction->sub_category_id ?? null,
                    'product_id' => $previousTransaction->product_id ?? null,
                    'contract_id' => $previousTransaction->contract_id ?? null,
                ]);
            }

            $transactionData['plaid_category_id'] = PlaidCategoryService::getPlaidCategoryId($data);

            $transactionData['ignore'] = PlaidCategoryService::isIgnoredPlaidCategory($data);

            $company->plaidTransactions()->updateOrCreate([
                'transaction_id' => $data['transaction_id'],
            ], $transactionData);
        } else {
            Log::debug($source . '::Transaction Skipped because account doesn\'t exist or amount is 0:: Account = ' . $data['account_id'] . ':: Amount =' . $data['amount'] . ':: Company ID =' . $company->id);
        }
    }

    public static function getUpcomingExpenses($company, $dateRange = [])
    {
        return self::getSubscriptions($company)
            ->where('plaid_subscriptions.predicted_next_date', '>', Carbon::now())
            ->when(!empty($dateRange), function ($query) use ($dateRange) {
                $query->whereBetween('plaid_subscriptions.predicted_next_date', $dateRange);
            })
            ->orderBy('plaid_subscriptions.predicted_next_date')
            ->groupBy('plaid_subscriptions.predicted_next_date')
            ->get();
    }

    public static function getUpcomingExpensesDate($company): string
    {
        $predictedNextDate = self::getSubscriptions($company)
            ->where('predicted_next_date', '>', Carbon::now())
            ->select('predicted_next_date')
            ->first();

        if ($predictedNextDate) {
            return Carbon::parse($predictedNextDate->predicted_next_date)->toISOString();
        }

        return '';
    }

    public static function deleteBankAccount($plaidBankAccount): void
    {
        $plaidBankAccount->transactions()->delete();
        $subscriptions = $plaidBankAccount->subscriptions()->get();
        foreach ($subscriptions as $subscription) {
            if ($subscription->plaidCompanyNotifications) {
                $subscription->plaidCompanyNotifications()->delete();
            }
        }

        $plaidBankAccount->subscriptions()->delete();
        $plaidBankAccount->delete();
    }

    public static function removeBlackListWords(?string $description): ?string
    {
        if (is_null($description)) {
            return null;
        }

        $blackListWords = json_decode(AppConfig::loadAppConfigByKey(AppConfigEnum::PLAID_REMOVE_BLACKLIST_WORDS_FROM_DESCRIPTION, '[]')->value, true);

        return str_replace($blackListWords, '', $description);
    }

    public static function syncSubscriptionTransactionsData(PlaidSubscription $subscription): void
    {
        $subscription->refresh();
        $subscription->transactions()->update([
            'my_stack_id' => $subscription->my_stack_id,
            'vendor_id' => $subscription->vendor_id,
            'category_id' => $subscription->category_id,
            'sub_category_id' => $subscription->sub_category_id,
            'plaid_category_id' => $subscription->plaid_category_id,
            'plaid_sub_category_id' => $subscription->plaid_sub_category_id,
            'contract_id' => $subscription->contract_id,
            'product_id' => $subscription->product_id,
            'productable_type' => $subscription->productable_type,
            'stackable_type' => $subscription->stackable_type,
            'ignore' => $subscription->ignore,
        ]);
    }
}
