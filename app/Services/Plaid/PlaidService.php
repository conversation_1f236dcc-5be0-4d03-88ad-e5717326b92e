<?php

namespace App\Services\Plaid;

use App\Enums\AppConfigEnum;
use App\Models\Company\Company;
use App\Models\User;
use App\Services\AppConfig;
use App\Services\AuthService;
use App\Services\Company\CompanyService;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Http;
use Illuminate\Validation\ValidationException;

class PlaidService
{
    /**
     * @link https://plaid.com/docs/api/link/#linktokencreate
     */
    public static function createLinkToken(User $user)
    {
        $countryCodes = explode(',', AppConfig::loadAppConfigByKey(AppConfigEnum::PLAID_COUNTRY_ALLOW_LIST, 'US,CA')->value);

        $response = Http::plaid()->post('link/token/create', self::buildParameters([
            'client_name' => 'BetterTracker',
            'user' => [
                'client_user_id' => '' . $user->id,
                'email_address' => $user->email,
            ],
            'products' => ['transactions'],
            'country_codes' => $countryCodes,
            'transactions' => [
                'days_requested' => 730, // max days
            ],
            'language' => 'en',
            'webhook' => route('api.plaid.webhook'),
        ]));

        return $response->json();
    }

    /**
     * @link https://plaid.com/docs/api/products/transactions/#transactionssync
     */
    public static function transactionSync(string $accessToken, array $optionFields = [])
    {
        $response = Http::plaid()->post(
            'transactions/sync',
            self::buildParameters(
                array_merge(
                    $optionFields,
                    [
                        'access_token' => $accessToken,
                    ]
                )
            )
        );

        return $response->json();
    }

    /**
     * @link https://plaid.com/docs/api/items/#remove-an-item
     */
    public static function removeItem(string $accessToken)
    {
        $response = Http::plaid()->post(
            'item/remove',
            self::buildParameters(
                [
                    'access_token' => $accessToken,
                ]
            )
        );

        return $response->json();
    }

    /**
     * @link https://plaid.com/docs/api/products/transactions/#transactionsrecurringget
     */
    public static function transactionRecurringGet(string $accessToken, array $optionFields = [])
    {
        $response = Http::plaid()->post(
            'transactions/recurring/get',
            self::buildParameters(
                array_merge(
                    $optionFields,
                    [
                        'access_token' => $accessToken,
                    ]
                )
            )
        );

        return $response->json();
    }

    /**
     * @link https://plaid.com/docs/api/items/#itempublic_tokenexchange
     */
    public static function exchangePublicToken(string $publicToken)
    {
        $response = Http::plaid()->post('item/public_token/exchange', self::buildParameters([
            'public_token' => $publicToken,
        ]));

        return $response->json();
    }
    /**
     * @link https://plaid.com/docs/api/institutions/#institutionsget_by_id
     */
    public static function getInstitutionById(string $institutionId)
    {
        $response = Http::plaid()->post('institutions/get_by_id', self::buildParameters([
            'institution_id' => $institutionId,
            'country_codes' => ['US'],
            'options' => [
                'include_optional_metadata' => true,
            ],
        ]));

        return $response->json();
    }

    private static function buildParameters(array $parameters): array
    {
        return array_merge($parameters, [
            'client_id' => config('plaid.client_id'),
            'secret' => config('plaid.secret'),
        ]);
    }

    public static function companyBankAccountExists(Company $company, array $account, ?string $institutionId): bool
    {
        return $company->plaidBankAcounts()
            ->where('account_id', $account['id'])
            ->orWhere(function ($query) use ($account, $institutionId, $company) {
                $query->where('mask', $account['mask'] ?? '')
                    ->where('name', $account['name'])
                    ->where('plaid_institution_id', $institutionId)
                    ->where('company_id', $company->id);
            })
            ->exists();
    }

    /**
     * @throws ValidationException
     */
    public static function validateIfUserBelongToCompany(Company $company): void
    {
        $response = Gate::inspect('plaid', $company);
        if (!$response->allowed()) {
            abort(403, $response->message());
        }
        $user = AuthService::getAuthUser();
        $companyAccessible = CompanyService::companyIsAccessible($user, app('asCompanyId'), $company->id);
        $userBelongToCompany = CompanyService::loggedUserBelongToCompany($company);
        if (!$companyAccessible && !$userBelongToCompany) {
            throw ValidationException::withMessages([
                config('genericMessages.error.ROLE_NOT_BELONGS_TO_COMPANY'),
            ]);
        }
    }
}
