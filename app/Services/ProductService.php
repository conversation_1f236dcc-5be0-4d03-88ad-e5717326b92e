<?php

namespace App\Services;

use App\Enums\AppConfigEnum;
use App\Enums\ProductListOrderFilter;
use App\Helpers\UtilityHelper;
use App\Jobs\SyncHubspotCompanyCategory;
use App\Models\Category\Category;
use App\Models\Company\Company;
use App\Models\Contract\Contract;
use App\Models\Product;
use App\Models\ProductFeature;
use App\Services\Company\CompanyService;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class ProductService
{
    /**
     * Stores a new product associated with a company.
     *
     * This method creates a product with the provided data, ensuring a unique
     * friendly URL, and syncs related categories, features, and pricing.
     * If any error occurs, the transaction is rolled back.
     *
     * @param  array  $productData  Product details including categories, features, and pricing.
     * @param  Company  $company  The company to which the product belongs.
     * @return Product The created product instance.
     *
     * @throws ValidationException If validation fails during creation.
     */
    public static function storeProduct(
        array $productData,
        Company $company
    ): Product {
        $source = __CLASS__ . '::' . __FUNCTION__ . '::';
        DB::beginTransaction();

        try {
            $friendlyUrl = UtilityHelper::generateUniqueWord('products', 'friendly_url', $productData['name'], '-');
            $product = Product::create(
                Arr::collapse([
                    ['company_id' => $company->id],
                    ['friendly_url' => $friendlyUrl],
                    $productData,
                ])
            );
            // Syncing categories and hubspot
            if (isset($productData['product_categories_ids'])) {
                $subCategoryIds = Category::whereIn('id', $productData['product_categories_ids'])
                    ->pluck('id');
                $product->categories()->sync($subCategoryIds);
                SyncHubspotCompanyCategory::dispatch($company);
            }
            // Syncing features
            if (isset($productData['features']) && !empty($productData['features'])) {
                $product->features()->createMany($productData['features']);
            }
            // Syncing Pricing
            if (isset($productData['pricing'])) {
                $product->pricing()->createMany($productData['pricing']);
            }
            DB::commit();

            return $product;
        } catch (ValidationException $e) {
            Log::error($source . $e->getMessage());
            DB::rollBack();

            throw $e;
        }
    }

    /**
     * Checks if a product is a dependency of another record and throws a ValidationException if true.
     *
     * @param  Product  $product  The product to check for related records.
     *
     * @throws ValidationException If the product is a dependency for another record.
     */
    public static function checkProductUsage(Product $product): void
    {
        // Checking contracts
        $hasContracts = Contract::where(['product_id' => $product->id])->exists();
        if ($hasContracts) {
            throw ValidationException::withMessages([
                'message' => config('genericMessages.error.PRODUCT_HAS_RELATED_CONTRACTS'),
                'errors' => [],
            ]);
        }
    }

    public static function appendImages($collection)
    {
        $modelIds = $collection->pluck('id');
        $mainImagesCollection = Media::whereIn(
            'collection_name',
            [config('custom.media_collections.product_main_image')]
        )
            ->whereIn('model_id', $modelIds)
            ->get();

        return ImageService::appendImages($collection, $mainImagesCollection);
    }

    public static function appendVideos($collection)
    {
        $modelIds = $collection->pluck('id');
        $mainVideosCollection = Media::whereIn(
            'collection_name',
            [config('custom.media_collections.product_main_video')]
        )
            ->whereIn('model_id', $modelIds)
            ->get();

        return VideoService::appendVideos($collection, $mainVideosCollection);
    }

    public static function appendReviewersAvatars($products)
    {
        $reviewers = collect();
        $products->each(function ($product) use ($reviewers) {
            if ($product->total_reviews > 0) {
                $product->reviews->each(function ($review) use ($reviewers) {
                    $reviewers->push($review->reviewer);
                });
            }
        });
        ImageService::appendUsersAvatars($reviewers);

        return $products;
    }

    public static function findUserNewsFeedItems($user, $startDate, $endDate, $onlyShowFollowingContent = true)
    {
        $query = Product::query();
        if ($onlyShowFollowingContent) {
            $query->whereIn('company_id', function ($query) use ($user) {
                $query->select('followed_company_id')
                    ->from('users_following_companies')
                    ->join('companies', 'followed_company_id', '=', 'companies.id')
                    ->where('follower_user_id', $user->id)
                    ->whereExists(function ($query) {
                        $query->select(DB::raw(1))
                            ->from('company_claimers')
                            ->whereColumn('company_claimers.company_id', 'companies.id');
                    });
            });
        } else {
            $query->whereIn('company_id', function ($query) {
                $query->select('id')
                    ->from('companies')
                    ->whereExists(function ($query) {
                        $query->select(DB::raw(1))
                            ->from('company_claimers')
                            ->whereColumn('company_claimers.company_id', 'companies.id');
                    });
            });
        }
        $query->whereBetween('created_at', [$endDate, $startDate]);
        $items = $query->get();
        $finalItems = ProductService::appendImages($items);

        return self::appendCompaniesToCollection($finalItems);
    }

    public static function findUserNewsFeedFeatures($user, $startDate, $endDate, $onlyShowFollowingContent = true)
    {
        $query = ProductFeature::query();
        if ($onlyShowFollowingContent) {
            $query
                ->select('product_features.*', 'products.company_id')
                ->join('products', 'products.id', '=', 'product_features.product_id')
                ->where('display_on_feed', 1)
                ->whereIn('products.company_id', function ($query) use ($user) {
                    $query->select('followed_company_id')
                        ->from('users_following_companies')
                        ->join('companies', 'followed_company_id', '=', 'companies.id')
                        ->where('follower_user_id', $user->id)
                        ->whereExists(function ($query) {
                            $query->select(DB::raw(1))
                                ->from('company_claimers')
                                ->whereColumn('company_claimers.company_id', 'companies.id');
                        });
                });
        } else {
            $query
                ->select('product_features.*', 'products.company_id')
                ->join('products', 'products.id', '=', 'product_features.product_id')
                ->where('display_on_feed', 1)
                ->whereIn('products.company_id', function ($query) {
                    $query->select('id')
                        ->from('companies')
                        ->whereExists(function ($query) {
                            $query->select(DB::raw(1))
                                ->from('company_claimers')
                                ->whereColumn('company_claimers.company_id', 'companies.id');
                        });
                });
        }
        $query->whereBetween('product_features.created_at', [$endDate, $startDate]);
        $items = $query->get();
        $finalItems = ProductService::appendProducts($items);

        return self::appendCompaniesToCollection($finalItems);
    }

    public static function findCompanyContentFeedFeatures(Company $company)
    {
        $query = ProductFeature::query();
        $query
            ->select('product_features.*', 'products.company_id')
            ->join('products', 'products.id', '=', 'product_features.product_id')
            ->where('display_on_feed', 1)
            ->where('products.company_id', $company->id);

        $items = $query->with('author')->get();
        $finalItems = ProductService::appendProducts($items);

        return self::appendCompaniesToCollection($finalItems);
    }

    public static function appendProducts(Collection $productCollection, $loadImages = true)
    {
        $productsCollection = ProductService::getByIdList($productCollection->pluck('product_id'), $loadImages)
            ->keyBy('id');

        $productCollection->transform(function ($item) use ($productsCollection) {
            $item->product = $productsCollection->get($item->product_id);

            return $item;
        });

        return $productCollection;
    }

    public static function getByIdList(Collection $idList, bool $loadImages = true)
    {
        $fields = [
            'products.*',
        ];
        $query = Product::select($fields)
            ->whereIn('products.id', $idList);

        if ($loadImages) {
            return ProductService::appendImages($query->get());
        }

        return $query->get();
    }

    public static function appendCompaniesToCollection(Collection $productCollection, $loadAvatars = true)
    {
        $companiesCollection = CompanyService::getByIdList($productCollection->pluck('company_id'), $loadAvatars)
            ->keyBy('id');

        $productCollection->transform(function ($item) use ($companiesCollection) {
            $item->company = $companiesCollection->get($item->company_id);

            return $item;
        });

        return $productCollection;
    }

    /**
     * @throws ValidationException
     */
    public static function getTopTrendingResults(
        Collection $results,
        ?Carbon $startDate,
        ?Carbon $endDate,
        int $numOfProducts,
        array $ignoreIdsList = []
    ): Collection {
        if (!is_null($startDate) &&
            UtilityHelper::getDaysBetweenDates(now(), $startDate) > (int)AppConfig::loadAppConfigByKey(
                AppConfigEnum::TOP_TRENDING_DAYS, config('common.topTrendingDays'))->value) {
            return $results;
        }
        $periodResult = self::prepareReviewRating($startDate, $endDate, $ignoreIdsList);
        if ($periodResult->count() > 0) {
            $products = Product::select('id', 'friendly_url', 'name', 'company_id')
                ->with(['company.avatar', 'categories:id,parent_id,name,friendly_url',
                    'stackedIn' => function ($query) use ($startDate, $endDate) {
                        $query->select('id')->whereBetween('created_at', [$startDate, $endDate]);
                    },
                    'reviews:id,approved_date'])
                ->whereIn('id', $periodResult->pluck('id')->toArray())->get();
            $products = self::calculateProductReviewsStatistics($products);
            $products->transform(function ($item) use ($periodResult, $startDate, $endDate) {
                $found = $periodResult->where('id', $item->id)->first();
                $item->period = $startDate . ' - ' . $endDate;
                $item->calculation_rating = $found?->rating ? $found?->rating * (float)AppConfig::loadAppConfigByKey(
                    AppConfigEnum::REVIEW_COUNT_PERCENTAGE,
                    config('common.trending.companies.review_count_percentage'))->value : 0;
                $item->calculation_rating = $item->calculation_rating + count($item->stackedIn) * (float)AppConfig::loadAppConfigByKey(
                    AppConfigEnum::STACK_COUNT_PERCENTAGE,
                    config('common.trending.companies.stack_count_percentage'))->value;
                $item->rating = floatval($item->rating);

                return $item;
            });
            $results = $results->merge($products->sortBy('name')
                ->sortByDesc('calculation_rating')
                ->take($numOfProducts));
        }
        if ($results->count() < $numOfProducts) {
            $nextEndDate = new Carbon($startDate->subDay()->endOfDay());
            $nextStartDate = new Carbon($startDate->subDays(2)->startOfDay());

            return self::getTopTrendingResults($results, $nextStartDate, $nextEndDate,
                $numOfProducts, array_merge($results->pluck('id')->toArray(), $ignoreIdsList));
        }

        return $results;
    }

    private static function prepareReviewRating($startDate, $endDate, $ignoreIdsList)
    {
        $startDateStatement = !$startDate ? '' : ' AND reviews.approved_date > \'' . $startDate . '\' ';
        $endDateStatement = !$endDate ? '' : ' AND reviews.approved_date < \'' . $endDate . '\' ';

        return collect(DB::select("SELECT products.id,
                                    ROUND(AVG(review_question_options.impact_rating_value), 2) AS rating,
                                    COUNT(DISTINCT(reviews.id)) AS total_reviews
                                FROM reviews
                                JOIN products
                                    ON reviews.model_id = products.id
                                JOIN review_answers
                                    ON reviews.id = review_answers.review_id
                                JOIN review_questions
                                    ON review_questions.id = review_answers.review_question_id
                                JOIN review_question_options
                                    ON review_answers.review_question_option_id = review_question_options.id
                                WHERE reviews.status = 'approved'
                                    " . $startDateStatement . $endDateStatement . '
                                    AND review_questions.impacts_rating = true
                                    AND review_question_options.impact_rating_value NOTNULL
                                    AND products.id NOT IN (' . implode(',', $ignoreIdsList) . ')
                                GROUP BY 1;'));
    }

    public static function calculateProductReviewsStatistics($collection, $startDate = null, $endDate = null)
    {
        $collection = $collection->filter();
        if ($collection->count() === 0) {
            return $collection;
        }
        $ids = implode(',', $collection->pluck('id')->toArray());
        $dateRange = $startDate && $endDate
            ? "AND reviews.approved_date > '" . $startDate . "' AND reviews.approved_date < '" . $endDate . "'"
            : '';
        $results = DB::select("SELECT products.id,
                                    ROUND(AVG(review_question_options.impact_rating_value), 2) AS satisfaction_rating,
                                    COUNT(DISTINCT reviews.id) AS total_reviews
                                FROM reviews
                                JOIN products
                                    ON reviews.model_id = products.id
                                JOIN review_answers
                                    ON reviews.id = review_answers.review_id
                                JOIN review_questions
                                    ON review_questions.id = review_answers.review_question_id
                                JOIN review_question_options
                                    ON review_answers.review_question_option_id = review_question_options.id
                                WHERE reviews.status = 'approved'
                                    AND review_questions.impacts_rating = true
                                    AND review_question_options.impact_rating_value NOTNULL
                                    AND products.id IN (" . $ids . ')
                                    ' . $dateRange . '
                                GROUP BY 1;');
        $collection->transform(function ($item) use ($results) {
            if (!empty($item) && !empty($item->id)) {
                $filteredResults = collect($results)->filter(function ($row) use ($item) {
                    return $row->id === $item->id;
                });

                $filteredResults = $filteredResults->values();

                if ($filteredResults->count() > 0) {
                    $item->rating = $filteredResults[0]->satisfaction_rating;
                    $item->total_reviews = $filteredResults[0]->total_reviews;
                }
            }

            return $item;
        });

        return $collection;
    }

    public static function calculateProductsRecommendationRatings($products)
    {
        $products = $products->filter();
        if ($products->count() === 0) {
            return $products;
        }
        $products->transform(function ($product) {
            $result = null;
            // THE RECOMMENDATION RATING IS CALCULATED GETTING THE AVERAGE OF THE recommended_by_reviewer_value
            // OF THE REVIEWS OF THE PRODUCT ROUNDED TO 2 DECIMALS
            if ($product->relationLoaded('reviews')) {
                $result = $product->reviews->avg('recommended_by_reviewer_value');
            }
            $product->recommendation_rating = $result ? round($result, 2) : null;

            return $product;
        });

        return $products;
    }

    public static function createRatingFieldSelect($alias = 'rating')
    {
        $sqlString = "(
            SELECT
               ROUND(AVG(review_question_options.impact_rating_value), 2)
            FROM reviews
                     JOIN products p2 ON reviews.model_id = p2.id
                     JOIN review_answers ON reviews.id = review_answers.review_id
                     JOIN review_questions ON review_questions.id=review_answers.review_question_id
                     JOIN review_question_options ON review_answers.review_question_option_id = review_question_options.id
            WHERE reviews.status = 'approved'
            AND review_questions.impacts_rating = true
            AND review_question_options.impact_rating_value NOTNULL
            AND p2.id=reviews.model_id
        )
        AS {$alias}";

        return DB::raw($sqlString);
    }

    public static function setOrdering($productsQuery, $orderByRequestValue)
    {
        $orderBy = ProductListOrderFilter::coerce($orderByRequestValue);
        switch ($orderBy) {
            case ProductListOrderFilter::HighestSatisfactionRating:
                $productsQuery->orderBy('rating', 'desc');

                break;
            case ProductListOrderFilter::LowestSatisfactionRating:
                $productsQuery->orderBy('rating');

                break;
            case ProductListOrderFilter::MostReviewed:
                $productsQuery->orderBy('total_reviews', 'desc');

                break;
        }
        $productsQuery->orderBy('company_profile_types.order');
        if (empty($orderBy)) {
            $productsQuery->orderBy('rating', 'desc');
        }
        $productsQuery->orderBy('companies.name');
    }

    /**
     * @throws ValidationException
     */
    public static function validateProductCompanyOwnership($product, $companyId)
    {
        if (!$product instanceof Product) {
            $product = Product::findOrfail($product);
        }
        if ($product->company_id != $companyId) {
            throw ValidationException::withMessages([
                'Product ' . $product->id . ' does not belong to company ' . $companyId,
            ]);
        }
    }
}
