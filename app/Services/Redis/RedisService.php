<?php

namespace App\Services\Redis;

use App\Helpers\ModelHelper;
use App\Models\Company\Company;
use App\Services\BusinessRulesService;
use App\Services\ProductService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class RedisService
{
    /**
     * @throws ValidationException
     */
    public static function setModelById(Model $model, $id, int $expirationSeconds = 0): void
    {
        $modelType = ModelHelper::getModelTypeByModelClass(get_class($model));
        $redisId = $modelType . '_' . $id;
        Cache::delete($redisId);

        if ($expirationSeconds < 1) {
            Cache::remember($redisId, now(), static function () use ($model) {
                return $model;
            });
        } else {
            Cache::remember($redisId, now()->addSeconds($expirationSeconds), static function () use ($model) {
                return $model;
            });
        }
    }

    /**
     * @throws ValidationException
     */
    public static function getModelById(string $modelClass, $id)
    {
        $modelType = ModelHelper::getModelTypeByModelClass($modelClass);

        return Cache::get($modelType . '_' . $id);
    }

    /**
     * @throws ValidationException
     */
    public static function deleteModelById(string $modelClass, $id): bool
    {
        $modelType = ModelHelper::getModelTypeByModelClass($modelClass);

        return Cache::delete($modelType . '_' . $id);
    }

    /**
     * @throws ValidationException
     */
    public static function setCollectionByModelClass(string $modelClass, Collection $collection, int $expirationSeconds = 0): void
    {
        $modelType = ModelHelper::getModelTypeByModelClass($modelClass);
        Cache::delete($modelType);

        if ($expirationSeconds < 1) {
            Cache::remember($modelType, null, static function () use ($collection) {
                return $collection;
            });
        } else {
            Cache::remember($modelType, now()->addSeconds($expirationSeconds), function () use ($collection) {
                return $collection;
            });
        }
    }

    /**
     * @throws ValidationException
     */
    public static function setConfigByModelClass(string $modelClass, array $config, int $expirationSeconds = 0): void
    {
        $modelType = ModelHelper::getModelTypeByModelClass($modelClass) . 'Config';
        Cache::delete($modelType);

        if ($expirationSeconds < 1) {
            Cache::remember($modelType, null, static function () use ($config) {
                return $config;
            });
        } else {
            Cache::remember($modelType, now()->addSeconds($expirationSeconds), function () use ($config) {
                return $config;
            });
        }
    }

    /**
     * @throws ValidationException
     */
    public static function getConfigByModelClass(string $modelClass)
    {
        $modelType = ModelHelper::getModelTypeByModelClass($modelClass) . 'Config';

        if (!Cache::has($modelType)) {
            $rules = BusinessRulesService::getConfigRulesAndValues();
            self::setConfigByModelClass($modelClass, $rules);
        }

        return Cache::get($modelType);
    }

    /**
     * @throws ValidationException
     */
    public static function getCollectionByModelClass(string $modelClass)
    {
        $modelType = ModelHelper::getModelTypeByModelClass($modelClass);

        return Cache::get($modelType);
    }

    /**
     * @throws ValidationException
     */
    public static function deleteCollectionByModelClass(string $modelClass): bool
    {
        $modelType = ModelHelper::getModelTypeByModelClass($modelClass);

        return Cache::delete($modelType);
    }

    public static function setCollectionByName(string $name, Collection $collection, int $expirationSeconds = 0): void
    {
        Cache::delete($name);

        if ($expirationSeconds < 1) {
            Cache::remember($name, null, static function () use ($collection) {
                return $collection;
            });
        } else {
            Cache::remember($name, now()->addSeconds($expirationSeconds), static function () use ($collection) {
                return $collection;
            });
        }
    }

    public static function getCollectionByName(string $name)
    {
        return Cache::get($name);
    }

    public static function deleteCollectionByName(string $name): bool
    {
        return Cache::delete($name);
    }

    public static function flushAllRedis(): void
    {
        Cache::flush();
    }

    public static function flushAllRedisPermissionsAndRoles(): void
    {
        // Get Redis instance
        $redis = Cache::getRedis();
        $prefix = Cache::getPrefix();
        // Get and delete keys containing "company_roles_for_user_ids_"
        $companyRolesKeys = $redis->keys('*company_roles_for_user_ids_*');
        foreach ($companyRolesKeys as $key) {
            Cache::forget(str_replace($prefix, '', $key));
        }

        // Get and delete keys containing "user_roles_"
        $userRolesKeys = $redis->keys('*user_roles_*');
        foreach ($userRolesKeys as $key) {
            Cache::forget(str_replace($prefix, '', $key));
        }

        // Get and delete keys containing "permissions_groups_for_user_company_"
        $userRolesKeys = $redis->keys('*permissions_groups_for_user_company_*');
        foreach ($userRolesKeys as $key) {
            Cache::forget(str_replace($prefix, '', $key));
        }

        // Get and delete keys containing "admin_company_roles_for_user_ids_"
        $userRolesKeys = $redis->keys('*admin_company_roles_for_user_ids_*');
        foreach ($userRolesKeys as $key) {
            Cache::forget(str_replace($prefix, '', $key));
        }

        // Get and delete keys containing "admin_permissions_groups_for_user_company_"
        $userRolesKeys = $redis->keys('*admin_permissions_groups_for_user_company_*');
        foreach ($userRolesKeys as $key) {
            Cache::forget(str_replace($prefix, '', $key));
        }
    }

    public static function getInstanceConfig(): array
    {
        return [
            'client' => env('REDIS_CLIENT', 'predis'),
            'options' => [
                'prefix' => env('REDIS_PREFIX', Str::slug(env('APP_NAME', 'laravel'), '_') . '_database_'),
                'ssl' => ['verify_peer' => false], // Since we dont have TLS cert to verify
                'parameters' => ['password' => env('REDIS_PASSWORD')],
            ],
            'default' => [
                'host' => env('REDIS_HOST', '127.0.0.1'),
                'password' => env('REDIS_PASSWORD'),
                'port' => env('REDIS_PORT', 6379),
            ],
            'cache' => [
                'host' => env('REDIS_HOST', '127.0.0.1'),
                'password' => env('REDIS_PASSWORD'),
                'port' => env('REDIS_PORT', 6379),
            ],
        ];
    }

    public static function getClusterConfig(): array
    {
        return [
            'client' => env('REDIS_CLIENT', 'predis'),
            'options' => [
                'cluster' => env('REDIS_CLUSTER', 'redis'),
                'prefix' => env('REDIS_PREFIX', Str::slug(env('APP_NAME', 'laravel'), '_') . '_database_'),
                'ssl' => ['verify_peer' => false], // Since we dont have TLS cert to verify
                'parameters' => ['password' => env('REDIS_PASSWORD')],
            ],
            'clusters' => [
                'default' => [
                    [
                        'host' => env('REDIS_HOST', 'localhost'),
                        'password' => env('REDIS_PASSWORD', null),
                        'port' => env('REDIS_PORT', 6379),
                    ],
                ],
                'cache' => [
                    [
                        'host' => env('REDIS_HOST', '127.0.0.1'),
                        'password' => env('REDIS_PASSWORD', null),
                        'port' => env('REDIS_PORT', 6379),
                    ],
                ],
            ],
        ];
    }

    public static function getCompanyRating(Company $company)
    {
        return Cache::remember('rating-points-' . $company->id, now()->addMinutes(10), function () use ($company) {
            $company->load('products');

            if (!$company->has('products')) {
                return 0;
            }

            $products = $company->products()->get();

            $rating = 0;

            if (!$products->count()) {
                return $rating;
            }

            ProductService::calculateProductReviewsStatistics($products);

            $product_count = 0;

            foreach ($products as $product) {
                if ($product->rating !== null) {
                    $rating += (float)$product->rating;
                    $product_count++;
                }
            }

            if ($product_count > 0) {
                $rating /= $product_count;
            }

            return $rating;
        });
    }
}
