<?php

namespace App\Services;

use App\Enums\ActivityLogAction;
use App\Enums\Company\CompanyType as CompanyTypeEnum;
use App\Enums\HubspotCreatedSource;
use App\Enums\ModelType;
use App\Enums\VerificationMode;
use App\Http\Resources\Register\RegisterCompanyExistenceValidationResource;
use App\Jobs\FinishUserConfirmation;
use App\Models\Company\Company;
use App\Models\Company\CompanyClaimer;
use App\Models\Company\CompanyType;
use App\Models\User;
use App\Services\ActivityLogs\ActivityLogsService;
use App\Services\Company\CompanyService;
use App\Services\Permission\PermissionService;
use App\Services\User\UserCompanyRequestService;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class RegisterService
{
    /**
     * VALIDATE IF EMAIL IS ALREADY REGISTERED AND VERIFIED
     * IF EMAIL IS NOT VERIFIED, PROCESS SHOULD START AGAIN FROM THE BEGINNING
     * DELETE EXISTING USER IF EXISTS
     *
     * @throws ValidationException
     */
    public static function processEmailValidation(string $email, bool $skipMailGunValidation = false): string
    {
        ValidationService::blackListDomainValidation($email);

        $emailVerifyResults = '';
        if (!$skipMailGunValidation) {
            $emailVerifyResults = ValidationService::validateEmailAddress($email);
        }

        // WE NEED TO VALIDATE IF EMAIL IS ALREADY REGISTERED AND VERIFIED.
        $user = User::where('email', $email)->first();
        // IF EMAIL IS NOT VERIFIED, PROCESS SHOULD START AGAIN FROM THE BEGINNING
        // DELETE EXISTING USER IF EXISTS
        if (!empty($user)) {
            self::validateUserIsVerified($user);
            $user->delete();
        }

        return $emailVerifyResults;
    }

    /**
     * Finish the registration process for the new user and triggers the register laravel event
     * to send welcome email
     *
     * @throws ValidationException
     */
    public static function registerUserCompany(
        User $user,
        $firstSend,
        $companyId,
        $companyName,
        $companyTypeValue,
        $parentId = null,
        $affiliateBrandId = null
    ): AnonymousResourceCollection|Company {
        $source = __CLASS__ . '::' . __FUNCTION__ . '::';
        // Search company by companyId
        $company = !empty($companyId)
            ? Company::with('enumType')->findOrFail($companyId)
            : null;
        // Extracting user subdomain

        $subdomain = UtilityService::getDomainFromEmail($user->email, false);
        if ($firstSend && !$company) {
            $newCompanyName = strtolower(str_replace(' ', '', $companyName));
            $subdomain = strtolower($subdomain);
            $companyType = CompanyType::firstWhere('value', $companyTypeValue);
            Log::debug($source . '[FIRST_SEND] Received Data. New Company Name: ' . $newCompanyName . ', Subdomain: ' . $subdomain . ', CompanyType: ' . $companyType->id);
            // Checking if all data match
            $company = Company::whereRaw("LOWER(REPLACE(name, ' ', '')) = '" . $newCompanyName . "'")
                ->select('id', 'name', 'type')
                ->with(
                    'avatar:id,model_type,model_id,uuid,collection_name,name,file_name,mime_type,disk,conversions_disk,size,custom_properties',
                    'enumType:id,label,value,type_is_of_vendor,order'
                )
                ->where('type', $companyType->id)
                ->when(($companyTypeValue !== CompanyTypeEnum::FranchiseMsp
                    && $companyTypeValue !== CompanyTypeEnum::MSP_LOCATION),
                    function ($q) use ($subdomain, $companyType) {
                        $fieldName = $companyType->type_is_of_vendor ? 'subdomain' : 'friendly_url';
                        $q->whereRaw('LOWER(' . $fieldName . ") = '" . $subdomain . "'");
                    })
                ->first();
            if (!empty($company)) {
                Log::debug($source . '[FIRST_SEND] Company Found: ' . $company->name . '(' . $company->id . ')');

                return $company;
            }
            // Checking if Company Name OR Company Subdomain matches
            $companies = Company::whereRaw("LOWER(REPLACE(name, ' ', '')) = '" . $newCompanyName . "'")
                ->select('id', 'name', 'type')
                ->with(
                    'avatar:id,model_type,model_id,uuid,collection_name,name,file_name,mime_type,disk,conversions_disk,size,custom_properties',
                    'enumType:id,label,value,type_is_of_vendor,order'
                )
                ->when(($companyTypeValue !== CompanyTypeEnum::FranchiseMsp
                    && $companyTypeValue !== CompanyTypeEnum::MSP_LOCATION),
                    function ($q) use ($subdomain, $companyType) {
                        $fieldName = $companyType->type_is_of_vendor ? 'subdomain' : 'friendly_url';
                        $q->orWhereRaw('LOWER(' . $fieldName . ") = '" . $subdomain . "'");
                    })
                // ->orWhereRaw("LOWER(subdomain) = '" . $subdomain . "'")
                ->get();
            if (!empty($companies) && $companies->count() > 0) {
                Log::debug($source . '[FIRST_SEND] Many Companies Found: ' . json_encode($companies->pluck('name')));

                return RegisterCompanyExistenceValidationResource::collection($companies);
            }
        }
        if (!$company) {
            Log::debug($source . 'No Companies Found');
            $company = CompanyService::getCompany($companyName, $companyTypeValue, true, null, $parentId, $affiliateBrandId);

            return $company;
        }

        return $company;
    }

    /**
     * Finish the registration process for the new user and triggers the register laravel event
     * to send welcome email
     */
    public static function finishUserRegistration(
        User $user,
        bool $isMSP = false,
        bool $sendUserConfirmationEmail = true,
        bool $useBetterTrackerConfig = false
    ): User {
        $company = $user->company;
        $profileType = UserProfileTypeService::getForCompanyType($company->enumType);
        $user->user_profile_types_id = $profileType?->id;
        $user->registered_all_pitches = UserService::autoRegisterForAllPitchEvents();
        self::subscribeToEventNotification($user);
        $user->save();

        // Invites needs to be updated with the corresponding info
        CompanyService::ActivateCompanyInvites($user, $company->enumType->value);

        ActivityLogsService::store(
            ActivityLogAction::addedPlan,
            ModelType::userType,
            $user->id,
            ModelType::companies,
            $company->id,
            ['company_profile_types_id' => '' . $company->company_profile_types_id]
        );

        if ($user->company->enumType->value !== CompanyTypeEnum::MSP_CLIENT) {
            HubspotService::sendContact(
                $user->company->name,
                $user->email,
                $user->first_name,
                $user->last_name,
                $user->company->enumType->value,
                HubspotCreatedSource::RegisterPage(),
                '' . $user->referred_by,
                $user->id,
                $user->phone ?? '',
                $user->registration_url ?? '',
                false,
                $user->jobTitle ? $user->jobTitle->name : '',
                $user->country ?? '',
                $user->state ?? '',
                $user->city ?? ''
            );
        }

        if ($user->verification_mode === VerificationMode::EMAIL) {
            $user->email_verified_at = now();
        } else {
            $user->phone_verified_at = now();
        }
        UserService::updateUserFields($user);

        // Don't add a role for users that already have it, for example when the role was set in the invite.
        $user->refresh();
        if (!$user->roles()->count()) {
            // If the company already has claimers then the user is assigned as a read only user, if not, it will be
            // assigned as a super admin of the company (claimer)
            if ($user->company->claimers->count() === 0) {
                $user->is_under_review = false;
                $user->save();
                $role = $user->company->superAdminRole();
                CompanyClaimer::updateOrCreate([
                    'company_id' => $user->company->id,
                    'user_id' => $user->id,
                ]);
            } else {
                $role = (bool)$user->is_under_review
                    ? $user->company->restrictedAccessRole()
                    : $user->company->readOnlyRole();
                // Sending a new request to the company for non invited users
                UserCompanyRequestService::createUserCompanyRequest($user->company, $user);
            }
            PermissionService::initializeUserRole($user->id, $role->id);
        }

        if ($sendUserConfirmationEmail) {
            FinishUserConfirmation::dispatch($user, $isMSP, $useBetterTrackerConfig);
        }

        return $user;
    }

    /**
     * Validate if the user's EMAIL is not verified in the database
     *
     * @throws ValidationException
     */
    public static function validateUserIsVerified(User $user): void
    {
        if (!empty($user->email_verified_at)) {
            throw ValidationException::withMessages(
                ['email' => config('genericMessages.error.EMAIL_ALREADY_EXISTS')]
            );
        }
    }

    /**
     * Process the verification code and make all corresponding validations
     *
     * @throws ValidationException
     */
    public static function processVerificationCode(User $user, $verificationCode): void
    {
        if (
            empty($user->verification_code) ||
            intval($verificationCode) !== intval($user->verification_code)
        ) {
            throw ValidationException::withMessages([
                'verification_code' => config('genericMessages.error.INVALID_VERIFICATION_CODE'),
            ]);
        }

        if ($user->verification_code_verified) {
            throw ValidationException::withMessages([
                'verification_code' => config('genericMessages.error.VERIFIED_VERIFICATION_CODE'),
            ]);
        }

        if (now() > $user->verification_code_expiration_date) {
            throw ValidationException::withMessages([
                'verification_code' => config('genericMessages.error.EXPIRED_VERIFICATION_CODE'),
            ]);
        }
        $user->verification_code_verified = true;
        $user->save();
    }

    /**
     * Process the verification mode selected by the user
     *
     * @param  null  $phone
     *
     * @throws ValidationException
     */
    public static function processVerificationMode(User $user, $verificationMode, $phone = null): void
    {
        $length = intval(config('common.plivo.codeSize'));
        $user->verification_code = substr(('' . now()->getTimestamp()), -$length);
        $user->verification_code_expiration_date = now()->addMinutes(config('common.plivo.expirationMinutes'));
        if ($verificationMode === VerificationMode::PHONE) {
            $user->mobile_phone = $phone;
            $user->verification_mode = VerificationMode::PHONE;
        } else {
            $user->verification_mode = VerificationMode::EMAIL;
        }
        $user->sendVerificationCode($verificationMode);
        $user->save();
    }

    public static function subscribeToEventNotification(User $user): void
    {
        if (AppConfig::loadAppConfigByKey('AUTO_SUBSCRIBE_REGISTERED_USER_TO_EVENT_NOTIFICATION', 'true')
            ->value === 'true') {
            $user->event_email_subscribed = true;
        } else {
            $user->event_email_subscribed = false;
        }
    }
}
