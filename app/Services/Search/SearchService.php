<?php

namespace App\Services\Search;

use App\Enums\Company\CompanyType;
use App\Enums\PrmMediaVisibility;
use App\Enums\SearchType;
use App\Enums\UserStatus;
use App\Helpers\PaginateHelper;
use App\Helpers\PermissionsHelper;
use App\Helpers\UtilityHelper;
use App\Http\Requests\Search\SearchRequest;
use App\Http\Resources\Blog\BlogResource;
use App\Http\Resources\DocumentResource;
use App\Http\Resources\Media\MediaGalleryResource;
use App\Http\Resources\Search\SearchVideoResource;
use App\Models\Category\Category;
use App\Models\Company\Company;
use App\Models\MediaGallery;
use App\Models\Product;
use App\Models\SearchesPerformed;
use App\Models\User;
use App\Services\AuthService;
use App\Services\BlogService;
use App\Services\DocumentService;
use App\Services\ImageService;
use App\Services\MediaGalleryService;
use App\Services\MediaService;
use App\Services\ProductService;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class SearchService
{
    public static function findCategories(SearchRequest $request)
    {
        try {
            $activeCompany = PermissionsHelper::getActiveCompany();
        } catch (\Exception $ex) {
            $activeCompany = null;
        }

        $formattedString = strtolower(str_replace("'", "''", $request->search_word));

        $query = Category::whereRaw('(name ilike ?)', ['%' . $formattedString . '%'])
            ->when($activeCompany, function ($q) use ($activeCompany) {
                $q->where(function ($q) use ($activeCompany) {
                    $q->whereHas('companyTypes', function ($q) use ($activeCompany) {
                        $q->where('company_types.id', $activeCompany->type);
                    })->orDoesntHave('companyTypes');
                });
            })
            ->with('parentCategory')
            ->orderBy('name');

        return UtilityHelper::getSearchRequestQueryResults($request, $query, $request->items_per_page);
    }

    public static function findPeople(SearchRequest $request)
    {
        $searchWords = [];
        $orderSearchWord = '';
        $completeSearchWord = '';
        if ($request->has('search_word')) {
            $searchWords = explode(' ', str_replace("'", "''", strtolower($request->search_word)));
            $orderSearchWord = str_replace("'", "''", strtolower("{$request->search_word}"));
            $completeSearchWord = str_replace("'", "''", strtolower("%{$request->search_word}%"));
        }
        $userQuery = User::with('userProfileType')
            ->statusIs(UserStatus::Active)
            ->where(function ($query) use ($searchWords, $request, $completeSearchWord) {
                if ($request->has('search_word')) {
                    $handleLikeParameter = str_replace('@', '', $completeSearchWord);
                    $query->orWhereRaw("lower(concat(trim(first_name),' ',trim(last_name))) LIKE ?", [$completeSearchWord]);
                    foreach ($searchWords as $searchWord) {
                        $query->orWhereRaw('lower(first_name) LIKE ?', [$searchWord]);
                        $query->orWhereRaw('lower(last_name) LIKE ?', [$searchWord]);
                        $query->orWhereRaw('lower(handle) LIKE ?', [$handleLikeParameter]);
                    }
                }
            });
        if (!AuthService::userIsSuperAdmin()) {
            $userQuery->where('users.is_private', false);
        }
        // ORDER BY
        if ($orderSearchWord) {
            $userQuery->orderByRaw("lower(concat(trim(first_name),' ',trim(last_name))) = '{$orderSearchWord}' DESC");
        }
        $userQuery->orderBy('first_name')->orderBy('last_name');
        $result = UtilityHelper::getSearchRequestQueryResults($request, $userQuery, $request->items_per_page);
        if ($result instanceof LengthAwarePaginator) {
            $pageResults = $result->getCollection();
        } else {
            $pageResults = $result;
        }
        $pageResults = ImageService::appendUsersAvatars($pageResults);
        if ($result instanceof LengthAwarePaginator) {
            $result->setCollection($pageResults);
        } else {
            $result = $pageResults;
        }

        return $result;
    }

    public static function findProducts(SearchRequest $request)
    {
        $searchWord = strtolower(str_replace("'", "''", $request->search_word));

        $query = Product::onlyApproved()->select(['products.*'])
            ->with('company.avatar', 'categories')
            ->where(function ($q) use ($searchWord) {
                $q->whereHas('categories', function ($query) use ($searchWord) {
                    $query->whereRaw("(lower(categories.name) like '%" .
                        $searchWord . "%')");
                })->orWhereRaw("(lower(companies.name) like '%" .
                    $searchWord . "%')")
                    ->orWhereRaw("(lower(products.name) like '%" . $searchWord . "%')");
            })
            ->join('companies', 'products.company_id', '=', 'companies.id')
            ->join('company_profile_types', 'company_profile_types_id', '=', 'company_profile_types.id')
            ->orderBy('company_profile_types.order')
            ->orderBy('products.name', 'ASC');

        $result = UtilityHelper::getSearchRequestQueryResults($request, $query, $request->items_per_page);
        if ($result instanceof LengthAwarePaginator) {
            $pageResults = $result->getCollection();
        } else {
            $pageResults = $result;
        }
        ImageService::appendCompanyAvatars($pageResults->pluck('company'));
        $pageResults = ProductService::appendImages($pageResults);
        if ($result instanceof LengthAwarePaginator) {
            $result->setCollection($pageResults);
        } else {
            $result = $pageResults;
        }

        return $result;
    }

    public static function findVendors(SearchRequest $request)
    {
        $searchWord = strtolower(str_replace("'", "''", $request->search_word));

        $query = Company::onlyGlobal()->select(['companies.*'])
            ->whereHas('claimers')
            ->whereHas('enumType', function ($q) {
                $q->where('type_is_of_vendor', true);
            })->where(function ($query) use ($searchWord) {
                $query->whereHas('categories', function ($query) use ($searchWord) {
                    $query->whereRaw("(lower(name) like '%" .
                        $searchWord . "%')");
                })->orWhereRaw("(lower(name) like '%" .
                    $searchWord . "%' OR lower(profile_vendor_handle) like '%" .
                    $searchWord . "%')");
            })->join('company_profile_types', 'company_profile_types_id', '=', 'company_profile_types.id')
            ->orderBy('company_profile_types.order');

        $result = UtilityHelper::getSearchRequestQueryResults($request, $query, $request->items_per_page);
        if ($result instanceof LengthAwarePaginator) {
            $pageResults = $result->getCollection();
        } else {
            $pageResults = $result;
        }
        $pageResults = ImageService::appendCompanyAvatars($pageResults);
        if ($result instanceof LengthAwarePaginator) {
            $result->setCollection($pageResults);
        } else {
            $result = $pageResults;
        }

        return $result;
    }

    public static function findVideos(SearchRequest $request, $appendParent = false, $appendAuthorTagAndCategories = false, $ids = [], $whereIn = true)
    {
        $videos = self::getVideos($request, $ids, $whereIn);
        if ($videos instanceof LengthAwarePaginator) {
            $pageResults = $videos->getCollection();
        } else {
            $pageResults = $videos;
        }
        $pageResults = MediaService::appendThumbnails($pageResults);
        if ($appendAuthorTagAndCategories) {
            $pageResults = MediaService::appendAuthors($pageResults);
            $pageResults = MediaService::appendTags($pageResults);
            $pageResults = MediaService::appendCategories($pageResults);
        }
        if ($appendParent) {
            $pageResults = MediaService::appendParentsFromDB($pageResults);
        }
        if ($videos instanceof LengthAwarePaginator) {
            $videos->setCollection($pageResults);
        }

        return $videos;
    }

    public static function findMediaGalleries(SearchRequest $request, $appendAuthorAndTags = false, $ids = [], $whereIn = true)
    {
        return MediaGalleryService::findByOwner($request, $request->company_id, $appendAuthorAndTags, $ids, $whereIn);
    }

    public static function findBlogs(SearchRequest $request, $ids = [], $whereIn = true)
    {
        $result = BlogService::searchBlogsByTitle($request, $ids, $whereIn);
        if ($result instanceof LengthAwarePaginator) {
            $pageResults = $result->getCollection();
        } else {
            $pageResults = $result;
        }
        $blogs = BlogService::appendImagesToCollection($pageResults);
        ImageService::appendCompanyAvatars($pageResults->pluck('subject'));
        if ($result instanceof LengthAwarePaginator) {
            $result->setCollection($blogs);
        } else {
            $result = $blogs;
        }

        return $result;
    }

    public static function findImages(SearchRequest $request)
    {
        $isPrivate = '';
        if (!AuthService::userIsSuperAdmin()) {
            $isPrivate = 'AND (users.is_private = FALSE OR users.is_private IS NULL)';
        }
        $isActiveUser = "AND (users.status = '" . UserStatus::Active . "'  OR users.status IS NULL)";
        if ($request->has('is_partner_content') && $request->is_partner_content) {
            $isPartnerContent = 'AND media.model_id=' . $request->company_id;
            $isActive = '';
        } else {
            $isActive = 'AND is_active = true';
            $isPartnerContent = 'AND (media_galleries.custom_properties->>\'is_partner_content\' = \'0\' OR media_galleries.custom_properties->>\'is_partner_content\' IS NULL)';
        }

        $searchWord = str_replace("'", "''", $request->search_word);

        $query = <<<EOT
                    SELECT
                        DISTINCT media_galleries.*
                    FROM media_galleries
                        LEFT JOIN users ON media_galleries.model_id=users.id
                        LEFT JOIN media ON media_galleries.id=media.model_id
                        LEFT JOIN LATERAL jsonb_array_elements(media_galleries.custom_properties->'tags') AS tag ON TRUE
                        LEFT JOIN tags ON tag::jsonb::text = concat('"',tags.id::text,'"')
                    WHERE
                          (
                            LOWER(tags.name) LIKE LOWER('%{$searchWord}%')
                            OR
                            LOWER(media_galleries.custom_properties->>'description') LIKE LOWER('%{$searchWord}%')
                            OR
                            LOWER(media_galleries.custom_properties->>'title') LIKE LOWER('%{$searchWord}%')
                          )
                        {$isActive}
                        {$isPrivate}
                        {$isActiveUser}
                        {$isPartnerContent}
                    ORDER BY media_galleries.created_at DESC
                    EOT;

        $mediaGalleries = MediaGallery::hydrate(DB::select($query));

        return self::checkPagination($request, $mediaGalleries->load(['media', 'parent']));
    }

    public static function findDocuments(SearchRequest $request, $appendParent = false, $appendAuthorTagAndCategories = false, $ids = [], $whereIn = true)
    {
        $documents = self::findMedia($request, config('custom.media_collections.documents'), $ids, $whereIn);
        if ($documents instanceof LengthAwarePaginator) {
            $pageResults = $documents->getCollection();
        } else {
            $pageResults = $documents;
        }
        $pageResults = DocumentService::appendDocumentsFromDB($pageResults);
        $pageResults = MediaService::appendThumbnails($pageResults);
        if ($appendAuthorTagAndCategories) {
            $pageResults = MediaService::appendAuthors($pageResults);
            $pageResults = MediaService::appendTags($pageResults);
            $pageResults = MediaService::appendCategories($pageResults);
        }
        if ($appendParent) {
            $pageResults = MediaService::appendParentsFromDB($pageResults);
        }
        if ($documents instanceof LengthAwarePaginator) {
            $documents->setCollection($pageResults);
        }

        return $documents;
    }

    public static function findTemplate(SearchRequest $request, $appendParent = false, $appendAuthorTagAndCategories = false, $ids = [], $whereIn = true)
    {
        $documents = self::findMedia($request, config('custom.media_collections.brandable'), $ids, $whereIn);
        if ($documents instanceof LengthAwarePaginator) {
            $pageResults = $documents->getCollection();
        } else {
            $pageResults = $documents;
        }
        $pageResults = DocumentService::appendBrandableDocumentsFromDB($pageResults);
        $pageResults = MediaService::appendThumbnails($pageResults);
        if ($appendAuthorTagAndCategories) {
            $pageResults = MediaService::appendAuthors($pageResults);
            $pageResults = MediaService::appendTags($pageResults);
            $pageResults = MediaService::appendCategories($pageResults);
        }
        if ($appendParent) {
            $pageResults = MediaService::appendParentsFromDB($pageResults);
        }
        if ($documents instanceof LengthAwarePaginator) {
            $documents->setCollection($pageResults);
        }

        return $documents;
    }

    public static function saveSearchPerformed(string $search_word, $search_type = SearchType::MainSearch)
    {
        if (trim($search_word) !== '') {
            SearchesPerformed::create([
                'user_id' => AuthService::getLoggedInUserId(),
                'search_type' => $search_type,
                'search_text' => $search_word,
            ]);
        }
    }

    public static function getVideos(SearchRequest $request, $ids = [], $whereIn = true)
    {
        return self::findMedia($request, config('custom.media_collections.videos'), $ids, $whereIn);
    }

    public static function getContent(SearchRequest $request): Collection
    {
        $results = collect();
        if ($request->has('only_videos') && $request->only_videos) {
            return $results->put('videos', SearchVideoResource::collection(self::findVideos($request, true)));
        }

        if ($request->has('only_blogs') && $request->only_blogs) {
            return $results->put('blogs', BlogResource::collection(self::findBlogs($request)));
        }

        if ($request->has('only_images') && $request->only_images) {
            return $results->put('images', MediaGalleryResource::collection(self::findImages($request)));
        }

        if ($request->has('only_documents') && $request->only_documents) {
            return $results->put('documents', DocumentResource::collection(self::findDocuments($request, true)));
        }

        $results->put('videos', self::findVideos($request, true));
        $results->put('blogs', self::findBlogs($request));
        $results->put('images', self::findImages($request));
        $results->put('documents', self::findDocuments($request, true));

        return $results;
    }

    /**
     * @return mixed
     */
    public static function findMedia(SearchRequest $request, $collectionType = null, $ids = [], $whereIn = true)
    {
        $query = self::getMediaSearchQuery($request, $collectionType, $ids, $whereIn);
        $result = UtilityHelper::getSearchRequestQueryResults($request, $query, 0, 'media');
        if ($result instanceof LengthAwarePaginator) {
            $media = $result->getCollection();
        } else {
            $media = $result;
        }
        $media = Media::hydrate($media->toArray());
        if ($result instanceof LengthAwarePaginator) {
            $result->setCollection($media);
        } else {
            $result = $media;
        }

        return $result;
    }

    public static function getMediaSearchQuery($request, $collectionType = null, $ids = [], $whereIn = true)
    {
        $isPrivate = '';
        $orderBy = 'media.created_at DESC';
        if (!AuthService::userIsSuperAdmin()) {
            $isPrivate = 'AND (users.is_private = FALSE OR users.is_private IS NULL)';
        }
        $isActiveUser = "AND (users.status = '" . UserStatus::Active . "' OR users.status IS NULL)";
        if ($request->has('is_partner_content') && $request->is_partner_content) {
            $isPartnerContent = 'AND (media.custom_properties->>\'is_partner_content\' = \'1\'';
            $isActiveContent = '';
        } else {
            $isPartnerContent = 'AND (media.custom_properties->>\'is_partner_content\' = \'0\' OR media.custom_properties->>\'is_partner_content\' IS NULL)';
            $isActiveContent = 'AND (media.custom_properties->>\'is_active\' = \'true\' OR media.custom_properties->>\'is_active\' IS NULL)';
        }
        if ($request->has('order_by')) {
            $orderBy = self::createOrderByRawString($request->order_by, $request->sort ?: 'ASC', 'media');
        }
        $formattedSearchWord = str_replace("'", "''", $request->search_word);

        if ($request->has('search_word')) {
            $searchWord = "AND (
                LOWER(tags.name) LIKE LOWER('%" . $formattedSearchWord . "%')
                OR
                LOWER(categories.name) LIKE LOWER('%" . $formattedSearchWord . "%')
                OR
                LOWER(media.custom_properties->>'description') LIKE LOWER('%" . $formattedSearchWord . "%')
                OR
                LOWER(media.custom_properties->>'title') LIKE LOWER('%" . $formattedSearchWord . "%'))";
        } else {
            $searchWord = '';
        }
        if ($request->has('tags') && is_array($request->tags)) {
            $searchWord .= ' AND tags.id in (' . implode(',', $request->tags) . ')';
        }
        if ($request->has('authors') && is_array($request->authors)) {
            $searchWord .= ' AND users.id in (' . implode(',', $request->authors) . ')';
        }
        if (count($ids)) {
            $whereIn = ($whereIn) ? 'IN' : 'NOT IN';
            $searchWord .= " AND media.id {$whereIn} (" . implode(',', $ids) . ')';
        }
        $typeofCollectionType = ' TRUE';
        if (!empty($collectionType)) {
            $typeofCollectionType = "media.collection_name IN ('{$collectionType}') ";
        }
        $query = DB::table('media')
            ->select('media.*')
            ->distinct()
            ->leftJoin('users', 'media.model_id', '=', 'users.id')
            ->leftJoin(DB::raw('LATERAL jsonb_array_elements(custom_properties->\'tags\') AS tag'), function ($join) {
                $join->on(DB::raw('TRUE'), '=', DB::raw('TRUE'));
            })
            ->leftJoin(DB::raw('LATERAL jsonb_array_elements(custom_properties->\'categories\') AS category'), function ($join) {
                $join->on(DB::raw('TRUE'), '=', DB::raw('TRUE'));
            })
            ->leftJoin('categories', DB::raw('category::jsonb::text'), '=', DB::raw('concat(\'"\',categories.id::text,\'"\')'))
            ->leftJoin('tags', DB::raw('tag::jsonb::text'), '=', DB::raw('concat(\'"\',tags.id::text,\'"\')'))
            ->whereRaw($typeofCollectionType . $searchWord . $isPrivate . $isActiveUser . $isPartnerContent . $isActiveContent)
            ->orderByRaw($orderBy);

        if ($request->company_id == config('custom.channel_program_company.id') && $request->has('media_visibility')) {
            $company = Company::find($request->company_id);
            if ($company->enumType->type_is_of_vendor) {
                $mediaVisibility = PrmMediaVisibility::VENDOR;
            } elseif ($company->enumType->value === CompanyType::DIRECT) {
                $mediaVisibility = PrmMediaVisibility::DIRECT;
            } else {
                $mediaVisibility = PrmMediaVisibility::MSP;
            }

            if ($company) {
                $query->whereRaw('media.custom_properties->>\'media_visibility\' =  ? OR media.custom_properties->>\'media_visibility\' IS NULL
                        OR media.custom_properties->>\'media_visibility\' = ?', [$mediaVisibility, PrmMediaVisibility::ALL]);
            } else {
                $query->whereRaw('media.custom_properties->>\'media_visibility\' =  ?', [$mediaVisibility]);
            }
        }

        return $query;
    }

    /**
     * @return LengthAwarePaginator|mixed
     */
    public static function checkPagination($request, $content)
    {
        if ($request->has('paged') && $request->paged) {
            $itemsPerPage = $request->items_per_page > 0 ?
                $request->items_per_page : config('common.searchPagingLength');
            $content = PaginateHelper::paginate($request, $content, 'page', $itemsPerPage);
        }

        return $content;
    }

    public static function createOrderByRawString($field, $sort = 'ASC', $alias = '')
    {
        $mediaCustomProperties = ['title', 'description'];
        $orderBy = '';
        switch ($field) {
            case 'created_at':
            case 'updated_at':
                $orderBy = $alias . '.' . $field . ' ' . $sort;

                break;
            default:
                $field = (in_array($field, $mediaCustomProperties)) ? "custom_properties->>'{$field}'::text" : $field;
                $orderBy = "lower( {$alias}" . '.' . "{$field}) " . $sort;
        }

        return $orderBy;
    }
}
