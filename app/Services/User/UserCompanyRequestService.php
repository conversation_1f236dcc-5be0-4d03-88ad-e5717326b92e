<?php

namespace App\Services\User;

use App\Enums\ActivityLogAction;
use App\Enums\ModelType;
use App\Enums\User\UserCompanyRequestFiltersEnum;
use App\Enums\User\UserCompanyRequestStatusEnum;
use App\Enums\UserStatus;
use App\Jobs\StoreUsersNotifications;
use App\Models\Company\Company;
use App\Models\User;
use App\Models\UserCompanyRequest;
use App\Services\ActivityLogs\ActivityLogsService;
use App\Services\Permission\RoleUserService;
use BenSampo\Enum\Exceptions\InvalidEnumMemberException;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class UserCompanyRequestService
{
    /**
     * Validates that the user request belongs to the specified company.
     *
     * @param  Company  $company  The company to check against.
     * @param  UserCompanyRequest  $userRequest  The user request to validate.
     *
     * @throws ValidationException if the request does not belong to the company.
     */
    public static function validateRequestIsFromCompany(
        Company $company,
        UserCompanyRequest $userRequest
    ): void {
        if ($userRequest->company_id !== $company->id) {
            throw ValidationException::withMessages(
                ['deal' => config('genericMessages.error.USER_REQUEST_NOT_BELONGS_TO_COMPANY')]
            );
        }
    }

    /**
     * Loads filters available for administrators, including statuses and companies.
     *
     * @return array The filters with status and company data.
     */
    public static function loadFiltersForAdmins(): array
    {
        $filters = [
            'statuses' => UserCompanyRequestFiltersEnum::admins['statuses'],
        ];

        // Getting statuses
        $filters['statuses']['items'] = [];
        $keys = array_filter(UserCompanyRequestStatusEnum::getKeys(), function ($key) {
            return $key !== UserCompanyRequestStatusEnum::getKey(UserCompanyRequestStatusEnum::approved);
        });
        foreach ($keys as $key) {
            $filters['statuses']['items'][$key] = UserCompanyRequestStatusEnum::getValue($key);
        }
        // Getting companies
        $companies = Company::whereIn('id',
            UserCompanyRequest::whereIn('status', $keys)->pluck('company_id')->toArray()
        )
            ->select(['id', 'name', 'type'])
            ->with('enumType')
            ->orderBy('name');
        $companies = $companies->get();
        if ($companies && $companies->count() > 0) {
            $filters['companies'] = UserCompanyRequestFiltersEnum::admins['companies'];
            $filters['companies']['items'] = $companies->map(function ($company) {
                return [
                    'id' => '' . $company->id,
                    'name' => $company->name,
                    'type' => $company->enumType,
                    'is_distributor' => $company->is_distributor,
                ];
            })->values();
        }

        return [
            'filters' => $filters,
        ];
    }

    /**
     * Creates a new user company request for the specified company and user.
     *
     * @param  Company  $company  The company for which the request is made.
     * @param  User  $user  The user making the request.
     * @return UserCompanyRequest The created user company request.
     *
     * @throws ValidationException if an error occurs during the creation.
     */
    public static function createUserCompanyRequest(
        Company $company,
        User $user
    ): UserCompanyRequest {
        try {
            $userRequest = UserCompanyRequest::create([
                'user_id' => $user->id,
                'company_id' => $company->id,
                'requested_at' => Carbon::now(),
                'status' => UserCompanyRequestStatusEnum::getKey(UserCompanyRequestStatusEnum::unverified),
            ]);
            // Adding activity log
            ActivityLogsService::store(
                ActivityLogAction::userCompanyRequestSent,
                ModelType::userType,
                $user->id,
                ModelType::user_company_requests,
                $userRequest->id,
                ['event' => 'Created "Request"']
            );
            DB::commit();

            // Notifying Super Admins
            $claimers = $company->claimers()->get();
            $claimersIDs = $claimers->pluck('id')->toArray();
            foreach ($claimersIDs as $claimerId) {
                Log::error(__CLASS__ . '::' . __FUNCTION__ . '::Notifying user ID: ' . $claimerId);
                dispatch(new StoreUsersNotifications(
                    $claimerId,
                    $claimerId,
                    User::class,
                    'user-company-request',
                    [
                        'company_id' => $company->id,
                        'friendly_url' => $company->friendly_url,
                    ]
                ));
            }

            return $userRequest;
        } catch (ValidationException $error) {
            DB::rollBack();
            Log::error(__CLASS__ . '::' . __FUNCTION__ . '::' . $error->getMessage());

            throw ValidationException::withMessages([$error->getMessage()]);
        }
    }

    /**
     * Loads user company requests based on optional filters such as relations, search words, statuses, and companies.
     *
     * @param  array|null  $relations  Relations to load with the requests.
     * @param  string|null  $searchWord  Optional search word for filtering.
     * @param  array|null  $statuses  Optional statuses to filter by.
     * @param  array|null  $companies  Optional companies to filter by.
     * @return Builder A query builder for further processing.
     *
     * @throws InvalidEnumMemberException
     */
    public static function loadUserCompanyRequests(
        ?array $relations = [],
        ?string $searchWord = null,
        ?array $statuses = null,
        ?array $companies = null,
    ): Builder {
        $query = UserCompanyRequest::with($relations)
            ->whereNot('user_company_requests.status', UserCompanyRequestStatusEnum::getKey(UserCompanyRequestStatusEnum::approved))
            ->when(!empty($statuses), function ($query) use ($statuses) {
                $query->whereIn('user_company_requests.status', $statuses);
            })
            ->when(!empty($searchWord), function ($query) use ($searchWord) {
                $query->where(function ($query) use ($searchWord) {
                    $searchWords = explode(' ', strtolower($searchWord));
                    foreach ($searchWords as $searchWord) {
                        $searchLikeParameter = "%{$searchWord}%";
                        $query->orWhereRaw('lower(users.first_name) LIKE ?', [$searchLikeParameter])
                            ->orWhereRaw('lower(users.last_name) LIKE ?', [$searchLikeParameter])
                            ->orWhereRaw('lower(users.email) LIKE ?', [$searchLikeParameter])
                            ->orWhereRaw('lower(companies.name) LIKE ?', [$searchLikeParameter]);
                    }
                });
            })
            ->when(!empty($companies), function ($query) use ($companies) {
                $query->whereIn('user_company_requests.company_id', $companies);
            })
            ->leftJoin('users', 'users.id', '=', 'user_company_requests.user_id')
            ->leftJoin('users as reviewers', 'reviewers.id', '=', 'user_company_requests.reviewer_id')
            ->leftJoin('companies', 'companies.id', '=', 'user_company_requests.company_id');

        return $query;
    }

    /**
     * Flags or unflags a user company request and logs the action.
     *
     * @param  UserCompanyRequest  $userRequest  The request to flag or unflag.
     * @param  User  $loggedUser  The user performing the action.
     * @param  bool  $redFlagged  Indicates whether the request is flagged.
     * @param  string  $redFlagReason  The reason for flagging the request.
     *
     * @throws ValidationException if an error occurs during the operation.
     */
    public static function flagRequest(
        UserCompanyRequest $userRequest,
        User $loggedUser,
        bool $redFlagged,
        string $redFlagReason,
    ): void {
        DB::beginTransaction();

        try {
            $userRequest->load('user');
            $userRequest->user->fill([
                'red_flagged' => $redFlagged,
                'red_flagged_by' => $redFlagged ? $loggedUser->id : null,
                'red_flag_reason' => $redFlagged ? $redFlagReason : null,
            ]);
            $userRequest->user->save();
            // Updating the request status only if the current status is unverified
            if ($userRequest->status === UserCompanyRequestStatusEnum::getKey(UserCompanyRequestStatusEnum::unverified)) {
                $userRequest->status = UserCompanyRequestStatusEnum::getKey(UserCompanyRequestStatusEnum::flagged);
                $userRequest->save();
            }
            // Adding activity log
            ActivityLogsService::store(
                ActivityLogAction::userCompanyRequestFlagged,
                ModelType::userType,
                $loggedUser->id,
                ModelType::user_company_requests,
                $userRequest->id,
                [
                    'event' => $redFlagged ? 'Flagged "Request"' : 'Unflagged "Request"',
                    'description' => $redFlagged ? $redFlagReason ?? '' : '',
                ]
            );
            DB::commit();
        } catch (ValidationException $error) {
            DB::rollBack();
            Log::error(__CLASS__ . '::' . __FUNCTION__ . '::' . $error->getMessage());

            throw ValidationException::withMessages([$error->getMessage()]);
        }
    }

    /**
     * Approves a user company request and assigns a new role to the user.
     *
     * @param  User  $loggedUser  The user performing the approval.
     * @param  UserCompanyRequest  $userRequest  The request being approved.
     * @param  string  $roleId  The role ID to assign to the user.
     *
     * @throws ValidationException if an error occurs during the approval.
     */
    public static function approveRequest(
        User $loggedUser,
        UserCompanyRequest $userRequest,
        string $roleId,
        Company $company
    ): void {
        DB::beginTransaction();

        try {
            // Updating deal status
            $userRequest->update([
                'status' => UserCompanyRequestStatusEnum::getKey(UserCompanyRequestStatusEnum::approved),
                'status_reason' => '',
                'reason_description' => '',
                'reviewer_id' => $loggedUser->id,
            ]);
            // Changing usser's review status
            $userRequest->user->is_under_review = false;
            $userRequest->user->status = UserStatus::Active;
            $userRequest->user->save();
            // Changing user's role
            RoleUserService::updateUserRoleByCompany($company, $roleId, $userRequest->user);
            // Adding activity log
            ActivityLogsService::store(
                ActivityLogAction::userCompanyRequestApproved,
                ModelType::userType,
                $loggedUser->id,
                ModelType::user_company_requests,
                $userRequest->id,
                ['event' => 'Approved "Request"']
            );
            DB::commit();
        } catch (ValidationException $error) {
            DB::rollBack();
            Log::error(__CLASS__ . '::' . __FUNCTION__ . '::' . $error->getMessage());

            throw ValidationException::withMessages([$error->getMessage()]);
        }
    }

    /**
     * Declines a user company request and deactivates the user account.
     *
     * @param  User  $loggedUser  The user performing the decline.
     * @param  UserCompanyRequest  $userRequest  The request being declined.
     * @param  string  $reason  The reason for declining the request.
     * @param  string  $reasonDescription  A description for the decline.
     *
     * @throws ValidationException if an error occurs during the decline.
     */
    public static function declineRequest(
        User $loggedUser,
        UserCompanyRequest $userRequest,
        string $reason,
        string $reasonDescription
    ): void {
        DB::beginTransaction();

        try {
            // Updating deal status
            $userRequest->update([
                'status' => UserCompanyRequestStatusEnum::getKey(UserCompanyRequestStatusEnum::declined),
                'status_reason' => $reason,
                'reason_description' => $reasonDescription,
                'reviewer_id' => $loggedUser->id,
            ]);
            // Deactivating user account
            $userRequest->user->update(['status' => UserStatus::Inactive]);
            // Adding activity log
            ActivityLogsService::store(
                ActivityLogAction::userCompanyRequestDeclined,
                ModelType::userType,
                $loggedUser->id,
                ModelType::user_company_requests,
                $userRequest->id,
                ['event' => 'Declined "Request"']
            );
            DB::commit();
        } catch (ValidationException $error) {
            DB::rollBack();
            Log::error(__CLASS__ . '::' . __FUNCTION__ . '::' . $error->getMessage());

            throw ValidationException::withMessages([$error->getMessage()]);
        }
    }
}
