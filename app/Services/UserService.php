<?php

namespace App\Services;

use App\Enums\ExplorerProfileFilter;
use App\Enums\UserStatus;
use App\Helpers\UtilityHelper;
use App\Models\ActivityLog\ActivityLog;
use App\Models\Analytics\Analytics;
use App\Models\Blog\Blog;
use App\Models\BroadcastMessage;
use App\Models\ChannelDeals\ChannelDeal;
use App\Models\Chat\ChatMessage;
use App\Models\Comment\Comment;
use App\Models\Company\Company;
use App\Models\Company\CompanyContactList;
use App\Models\Company\CompanyInvite;
use App\Models\Contract\Contract;
use App\Models\Deal\Deal;
use App\Models\FeatureFlag;
use App\Models\MediaGallery;
use App\Models\PartnerBrandableContactInfo;
use App\Models\Permission\Role\RoleUser;
use App\Models\Review\Review;
use App\Models\ShoutOut\ShoutOut;
use App\Models\User;
use App\Models\UserProfileType;
use App\Models\VideoPlaylist;
use App\Services\Analytics\AnalyticService;
use App\Services\Contract\ContractService;
use App\Services\PitchEvent\PitchEventsAttendantsUsersService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class UserService
{
    /**
     * Validates the user is the owner or a super admin
     *
     * @throws ValidationException
     */
    public static function validateUserIsOwnerOrSuperAdmin($loggedUser, $userId): void
    {
        if ($userId !== $loggedUser->id && !AuthService::userIsSuperAdmin($loggedUser)) {
            throw ValidationException::withMessages([config('genericMessages.error.UNAUTHORIZED')]);
        }
    }

    /**
     * Validates the user is the owner or an admin
     *
     * @throws ValidationException
     */
    public static function validateUserIsOwnerOrAdmin($loggedUser, $userId): void
    {
        if ($userId !== $loggedUser->id && !self::validateUserIsAdmin($loggedUser)) {
            throw ValidationException::withMessages([config('genericMessages.error.UNAUTHORIZED')]);
        }
    }

    /**
     * Validates the user has an admin role
     */
    public static function validateUserIsAdmin($loggedUser = null): bool
    {
        $tempUser = $loggedUser ?? AuthService::getAuthUser();
        if (!$tempUser) {
            return false;
        }

        return AuthService::userIsAdmin($tempUser);
    }

    /**
     * Loads the profile info into a user
     *
     * @return User $user
     */
    public static function loadProfileInfo(User $user): User
    {
        $user->load([
            'media' => function ($query) {
                $query->whereIn('collection_name', [
                    config('custom.media_collections.avatar.user'),
                    config('custom.media_collections.top_banner'),
                ]);
            },
            'profileVideos' => function ($query) {
                $query->orderBy('created_at', 'desc')
                    ->limit(5);
            },
            'company.avatar', 'company.enumType',
        ]);

        $isBeingFollowed = false;
        $authUser = AuthService::getAuthUser();
        if ($authUser) {
            $isBeingFollowed = ($authUser->usersIFollow()->where('followed_user_id', $user->id)->exists());
        }
        $user->profileVideos = MediaService::appendThumbnails($user->profileVideos);
        $user->profileVideos = VideoService::appendViewsCounts($user->profileVideos);
        $user->profileVideos = VideoService::appendTagsToCollection($user->profileVideos);
        $user->profileVideos = VideoService::appendCategoriesToCollection($user->profileVideos);
        $user->profileVideos = VideoService::appendLikesCount($user->profileVideos, UserService::getLoggedUserLikes());
        $user->isBeingFollowed = $isBeingFollowed;
        $user->loadCount(['usersFollowingMe']);

        return $user;
    }

    /**
     * Loads the profile info into a user
     *
     * @return User $user
     */
    public static function loadRightBarProfileInfo(User $user): User
    {
        $user->load([
            'media' => function ($query) {
                $query->whereIn('collection_name', [
                    config('custom.media_collections.avatar.user'),
                    config('custom.media_collections.top_banner'),
                ]);
            },
            'profileVideos' => function ($query) {
                $query->orderBy('created_at', 'desc');
            },
        ]);

        $authUser = AuthService::getAuthUser();
        $isBeingFollowed = false;
        if ($authUser) {
            $isBeingFollowed = ($authUser->usersIFollow()->where('followed_user_id', $user->id)->exists());
        }
        $user->profileVideos = MediaService::appendThumbnails($user->profileVideos);
        $user->profileVideos = VideoService::appendViewsCounts($user->profileVideos);
        $user->profileVideos = VideoService::appendShareLinks($user->profileVideos);
        $user->profileVideos = VideoService::appendCategoriesToCollection($user->profileVideos);
        $user->profileVideos = VideoService::appendTagsToCollection($user->profileVideos);
        $user->profileVideos = VideoService::appendLikesCount($user->profileVideos, UserService::getLoggedUserLikes());
        $user->isBeingFollowed = $isBeingFollowed;
        $user->loadCount(['usersFollowingMe']);

        return $user;
    }

    /**
     * Validates if the user visibility is PUBLIC
     */
    public static function validateUserVisibility(User $user): bool
    {
        $loggedInUser = AuthService::getAuthUser();
        $isAdmin = self::validateUserIsAdmin($loggedInUser);
        if (!$isAdmin && (($loggedInUser && $loggedInUser->getAuthIdentifier() !== $user->id && $user->is_private)
                || (!$loggedInUser && $user->is_private))) {
            abort(403, config('genericMessages.error.USER_NOT_PUBLIC'));
        }

        return $isAdmin;
    }

    public static function autoRegisterForAllPitchEvents(bool $userChoice = false): bool
    {
        $featureFlag = self::findFeatureFlagByName('AUTO_REGISTER_ALL_PITCHES');
        $isFeatureOn = $featureFlag && $featureFlag->activated !== false;

        return $isFeatureOn || $userChoice;
    }

    /**
     * @return mixed
     */
    public static function findFeatureFlagByName($name)
    {
        return FeatureFlag::where('name', $name)->first();
    }

    public static function profileUserIsCreatorOrAdmin(User $user, $modelId): bool
    {
        return AuthService::userIsSuperAdmin($user) || ($user->id === $modelId);
    }

    public static function getEnum(): object
    {
        $userProfileTypes = UserProfileType::select('id', 'label')->orderBy('order', 'asc')->get();
        $enums = $userProfileTypes->mapWithKeys(function ($item) {
            return ['' . $item->id => $item->label];
        });

        return (object)$enums;
    }

    public static function getSomeRandomInfluencersWithAvatars($itemsPerPage)
    {
        $loggedInUser = AuthService::getAuthUser();
        $loggedInUserId = $loggedInUser?->id ?? 0;
        $rawUserIsFollowing = "exists(
             Select 1 from users_following_users where follower_user_id={$loggedInUserId} and followed_user_id=users.id
            ) as current_user_is_following";
        $userQuery = User::with('userProfileType')
            ->join('job_titles', 'users.job_title_id', '=', 'job_titles.id')
            ->whereHas('userProfileType', function ($query) {
                $query->where('value', ExplorerProfileFilter::INFLUENCER);
            })
            ->select([
                'users.*',
                DB::raw($rawUserIsFollowing),
                DB::raw('job_titles.name as job_title_name'),
            ])
            ->withCount('usersFollowingMe')
            ->inRandomOrder()
            ->limit($itemsPerPage);
        if (!AuthService::userIsSuperAdmin($loggedInUser)) {
            $userQuery->where('users.is_private', false);
        }
        $profiles = ImageService::appendUsersAvatars($userQuery->get());

        return AnalyticService::appendLikesCounts($profiles, UserService::getLoggedUserLikes());
    }

    public static function getSomeRandomUsersWithAvatars($itemsPerPage)
    {
        $loggedInUser = AuthService::getAuthUser();
        $loggedInUserId = $loggedInUser?->id ?? 0;
        $rawUserIsFollowing = "exists(
             Select 1 from users_following_users where follower_user_id={$loggedInUserId} and followed_user_id=users.id
            ) as current_user_is_following";

        $userQuery = User::with('userProfileType')
            ->join('job_titles', 'users.job_title_id', '=', 'job_titles.id')
            ->whereHas('userProfileType', function ($query) {
                $query->where('value', '!=', ExplorerProfileFilter::INFLUENCER);
            })
            ->select([
                'users.*',
                DB::raw($rawUserIsFollowing),
                DB::raw('job_titles.name as job_title_name'),
            ])
            ->withCount('usersFollowingMe')
            ->inRandomOrder()
            ->limit($itemsPerPage);

        if (!AuthService::userIsSuperAdmin($loggedInUser)) {
            $userQuery->where('users.is_private', false);
        }
        $profiles = ImageService::appendUsersAvatars($userQuery->get());

        return AnalyticService::appendLikesCounts($profiles, UserService::getLoggedUserLikes());
    }

    public static function getPaginatedInfluencersWithAvatars($request, $resultsPerPage)
    {
        $loggedInUserId = AuthService::getLoggedInUserId() ?? 0;
        $rawUserIsFollowing = "exists(
             Select 1 from users_following_users where follower_user_id={$loggedInUserId} and followed_user_id=users.id
            ) as current_user_is_following";

        $query = User::with('userProfileType')
            ->join('job_titles', 'users.job_title_id', '=', 'job_titles.id')
            ->whereHas('userProfileType', function ($query) {
                $query->where('value', ExplorerProfileFilter::INFLUENCER);
            })
            ->select([
                'users.*',
                DB::raw($rawUserIsFollowing),
                DB::raw('job_titles.name as job_title_name'),
            ])
            ->withCount('usersFollowingMe')
            ->where('status', UserStatus::Active)
            ->orderBy('first_name')
            ->orderBy('last_name');

        return self::getPaginatedResults($request, $query, $resultsPerPage);
    }

    public static function getPaginatedUsersWithAvatars($request, $resultsPerPage)
    {
        $query = User::with('userProfileType')
            ->whereHas('userProfileType', function ($query) {
                $query->where('value', '!=', ExplorerProfileFilter::INFLUENCER);
            })
            ->where('status', UserStatus::Active)
            ->orderBy('first_name')
            ->orderBy('last_name');

        return self::getPaginatedResults($request, $query, $resultsPerPage);
    }

    /**
     * If pitch_event_id is received, the system should try to register the user to it
     *
     *
     * @throws ValidationException
     */
    public static function processPitchEventRegistration($pitchEventId, $user): void
    {
        if ($pitchEventId) {
            Log::debug('Pitch event id on registration is' . $pitchEventId);
            PitchEventsAttendantsUsersService::processUserAttendanceToPitchEvent($pitchEventId, $user->id);
        } else {
            Log::debug('Pitch event id on registration is not sent in');
        }
    }

    /**
     * Updates all the fields of the user when confirms the email
     */
    public static function updateUserFields($user): void
    {
        $word = $user->first_name . $user->last_name;
        if ($user->handle === null) {
            $handle = ['handle' => UtilityHelper::generateUniqueWord(
                'users',
                'handle',
                UtilityHelper::generateUniqueWord(
                    'companies',
                    'friendly_url',
                    $word
                )
            )];
            $user->update($handle);
        }
        if ($user->friendly_url === null) {
            $friendlyUrl = ['friendly_url' => UtilityHelper::generateUniqueWord(
                'users',
                'friendly_url',
                UtilityHelper::generateUniqueWord(
                    'companies',
                    'friendly_url',
                    $word
                )
            )];
            $user->update($friendlyUrl);
        }
    }

    /**
     * Unsubscribe the user from all industry calendar emails using the identification token
     */
    public static function unsubscribeEmailWithToken(string $unsubscription_token): bool
    {
        $user = User::where(DB::raw('sha256(concat(cast(id as string), email))'), $unsubscription_token)
            ->select(['id', 'event_email_subscribed'])
            ->first();
        if (!$user) {
            return false;
        }

        if ($user->event_email_subscribed !== false) {
            $user->update(['event_email_subscribed' => false]);
        }

        return true;
    }

    /**
     * Returns all the logged user Analytics for likes, if the user is not logged in
     * returns an empty collection.
     */
    public static function getLoggedUserLikes(): mixed
    {
        $authUser = AuthService::getAuthUser();
        $loggedUserLikes = collect();
        if ($authUser) {
            $loggedUserLikes = $authUser->whatILike;
        }

        return $loggedUserLikes;
    }

    /**
     * @throws \Throwable
     */
    public static function deleteUser($userId): void
    {
        // there are a lot of steps to deleting a user because some of our tables are reused and foreign keys cannot be placed.
        $user = User::where('id', $userId)->first();

        DB::beginTransaction();

        try {
            Log::info(
                "Deleting user with email {$user->email}. User deleting this user is " .
                AuthService::getAuthUser()->email
            );

            // null author_id on activity logs where author_type is App\Models\User
            ActivityLog::where('author_id', $userId)
                ->where('author_type', User::class)
                ->update(['author_id' => null]);

            // null author_id on analytics where author_type is App\Models\User
            Analytics::where('author_id', $userId)->where('author_type', User::class)
                ->update(['author_id' => null]);

            // delete author_id on blogs where author_type is App\Models\User
            Blog::where('author_id', $userId)
                ->where('author_type', User::class)->delete();

            // delete author_id from broadcast_messages
            BroadcastMessage::where('author_id', $userId)->delete();

            // delete contracts for author_id
            $parentContracts = Contract::select('contracts.id', 'contracts.parent_id')
                ->with('addOns:id,parent_id')
                ->where('author_id', $userId)
                ->whereNull('parent_id')
                ->get();
            foreach ($parentContracts as $parentContract) {
                $productContracts = $parentContract->addOns;
                if ($productContracts->count() === 0) {
                    ContractService::deleteContract($user, $parentContract);
                } else {
                    foreach ($productContracts as $productContract) {
                        ContractService::deleteContract($user, $productContract);
                    }
                }
            }

            // delete reviews with the reviewer id
            Review::where('reviewer_user_id', $userId)->orWhere('model_id', $userId)->delete();

            // delete author_id on shout_outs where author_type is App\Models\User
            ShoutOut::where('author_id', $userId)->where('author_type', User::class)->delete();

            // delete model_id from video_playlists
            VideoPlaylist::where('model_id', $userId)->where('model_type', User::class)->delete();

            // delete all chat messages for user
            ChatMessage::where(DB::raw('(receiver)::varchar'), $userId)
                ->orWhere(DB::raw('(creator)::varchar'), $userId)->delete();

            // delete all comments for user
            Comment::where('author_id', $userId)->delete();

            // Delete all contact infos from partner brand content
            PartnerBrandableContactInfo::where('author_id', $userId)->delete();

            // Delete all deals from user
            Deal::where('author_id', $userId)->orWhere('requester_id', $userId)->delete();

            // Delete all channel deals from user
            ChannelDeal::where('created_by_user_id', $userId)->orWhere('reviewer_user_id', $userId)->delete();

            // Delete all company contact list from user
            CompanyContactList::where('creator_id', $userId)->delete();

            // delete from media table - get all media for the user  (automatically deletes from backblaze)
            $userMedia = Media::where('model_id', $userId)
                ->where('model_type', User::class)->get();
            foreach ($userMedia as $media) {
                MediaService::deleteMedia($media);
            }

            // delete model_id from media_galleries (automatically deletes from backblaze)
            $mediaGalleries = MediaGallery::where('model_id', $userId)
                ->where('model_type', User::class)->get();
            foreach ($mediaGalleries as $mediaGallery) {
                $mediaGallery->delete();
            }

            // delete from hubspot - (delete by email because of merged accounts in hubspot)
            HubspotService::deleteViaEmailAddress($user->email);

            // delete company invites - (delete by email because that's the only reference)
            CompanyInvite::where('email', $user->email)->delete();

            // LAST: delete from users
            $user->delete();
            // If everything went OK, save all changes to the DB
            DB::commit();
        } catch (\Throwable $ex) {
            // If anything goes bad, rollback the complete transaction
            DB::rollBack();
            Log::error('Rolling back user deletion: User email: ' . $user->email . ' | Message:' . $ex->getTraceAsString());

            throw $ex;
        }
    }

    private static function getPaginatedResults($request, $query, $resultsPerPage)
    {
        if (!AuthService::userIsSuperAdmin()) {
            $query->where('users.is_private', false);
        }

        if (!empty($request->search_word)) {
            $searchWord = strtolower("%{$request->search_word}%");
            $query->where(function ($q) use ($searchWord) {
                $completeNameLikeParameter = '%' . strtolower($searchWord) . '%';
                $q->whereRaw("lower(concat(trim(first_name),' ',trim(last_name))) LIKE ?", [$completeNameLikeParameter]);
                $searchWords = explode(' ', strtolower($searchWord));
                foreach ($searchWords as $searchWord) {
                    $nameLikeParameter = '%' . strtolower($searchWord) . '%';
                    $handleLikeParameter = '%' . str_replace('@', '', strtolower($searchWord)) . '%';
                    $q->orWhereRaw('lower(users.first_name) LIKE ?', [$nameLikeParameter]);
                    $q->orWhereRaw('lower(users.last_name) LIKE ?', [$nameLikeParameter]);
                    $q->orWhereRaw('lower(users.handle) LIKE ?', [$handleLikeParameter]);
                }
            });
        }

        $result = UtilityHelper::getSearchRequestQueryResults($request, $query, $resultsPerPage);

        if ($result instanceof LengthAwarePaginator) {
            $pageResults = $result->getCollection();
        } else {
            $pageResults = $result;
        }

        $pageResults = ImageService::appendUsersAvatars($pageResults);

        if ($result instanceof LengthAwarePaginator) {
            $result->setCollection($pageResults);
        } else {
            $result = $pageResults;
        }

        return $result;
    }

    public static function checkUserIsActive($user): void
    {
        if ($user->status !== UserStatus::Active) {
            throw ValidationException::withMessages([
                config('genericMessages.error.USER_IS_INACTIVE'),
            ]);
        }
    }

    /**
     * Retrieves a collection or paginated list of users for a specified company based on the given request parameters.
     *
     * @param  FormRequest  $request  The request object containing filtering parameters.
     * @param  Company  $company  The company for which users are being retrieved.
     * @return Collection|LengthAwarePaginator A collection or paginated list of users.
     */
    public static function getActiveUsersForACompany(
        FormRequest $request,
        Company $company
    ): Collection|LengthAwarePaginator {
        // Getting user's IDs for the company
        $userIds = RoleUser::select('roles_users.user_id')
            ->usersByCompanyRoles($company->id)
            ->when($request->has('roles'), function ($query) use ($request) {
                $query->whereIn('roles.id', $request->get('roles'));
            })
            ->get()
            ->pluck('user_id')
            ->toArray();
        // Getting users
        $query = User::whereIn('users.id', $userIds)
            ->join('roles_users', 'roles_users.user_id', '=', 'users.id')
            ->join('roles', 'roles.id', '=', 'roles_users.role_id')
            ->where('roles.company_id', $company->id)
            ->when($request->has('roles'), function ($query) use ($request) {
                $query->whereIn('roles.id', $request->get('roles'));
            })
            ->with('roles', function ($q) use ($company, $request) {
                $q->selectRaw('roles.id,roles.title,roles.is_admin,roles.description,roles.key,roles.company_id,roles.display_name')
                    ->where('company_id', $company->id)
                    ->when($request->has('roles'), function ($query) use ($request) {
                        $query->whereIn('roles.id', $request->get('roles'));
                    });
            })
            ->with('userProfileType:id,label,value')
            ->with('company:id,name')
            ->selectRaw('users.id, users.first_name, users.last_name, users.email, users.email_verified_at, users.phone_verified_at,
                                users.created_at, users.hubspot_contact_id, users.handle, users.friendly_url, users.user_profile_types_id,
                                users.is_private, users.is_profile_complete, users.company_id,
                                users.last_logged_in_at, users.status, users.red_flagged, users.red_flag_reason, roles.display_name, roles.id as company_role_id')
            ->where('users.is_under_review', false)
            // Group all possible order fields to avoid duplicate entries avoid, we can't use `distinct` method because it is not working well with pagination
            ->groupBy('users.id', 'users.first_name', 'users.last_name', 'users.email', 'users.email_verified_at', 'users.phone_verified_at', 'users.created_at', 'roles.display_name', 'roles.id');
        $query->where('users.status', $request->has('status') ? $request->status : UserStatus::Active);

        if ($request->has('search_word')) {
            $query->where(function ($q) use ($request) {
                $value = strtolower(trim($request->search_word));
                $q->orWhereRaw('lower(users.first_name) like ?', ['%' . $value . '%']);
                $q->orWhereRaw('lower(users.last_name) like ?', ['%' . $value . '%']);
                $q->orWhereRaw('lower(users.email) like ?', ['%' . $value . '%']);
                $q->orWhereRaw('lower(users.status) like ?', ['%' . $value . '%']);
                $q->orWhereRaw('lower(roles.display_name) like ?', ['%' . $value . '%']);
            });
        }

        if ($request->has('last_login')) {
            $query->whereIn(DB::raw('DATE(users.last_logged_in_at)'), $request->get('last_login'));
        }

        if ($request->has('registered_date')) {
            $query->whereIn(DB::raw('DATE(users.created_at)'), $request->get('registered_date'));
        }
        $result = UtilityHelper::getSearchRequestQueryResults($request, $query);

        return $result;
    }

    public static function resetTFA(string $userId): void
    {
        $user = User::findOrFail($userId);
        $user->two_factor_secret = null;
        $user->two_factor_recovery_codes = null;
        $user->two_factor_confirmed = false;
        $user->save();
    }
}
