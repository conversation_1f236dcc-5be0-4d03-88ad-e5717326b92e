<?php

namespace App\Services;

use App\Http\Controllers\Api\EmailBlackListController;
use App\Http\Controllers\Api\FeatureFlagController;
use App\Models\Company\Company;
use App\Models\Email\EmailBlackList;
use App\Models\EmailBlock\EmailBlockReasons;
use App\Models\EmailBlock\EmailBlockResults;
use App\Models\EmailBlock\EmailBlockRisks;
use App\Services\Company\CompanyService;
use App\Services\Email\EmailWhitelistService;
use App\Services\Permission\RoleUserService;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use stdClass;

class ValidationService
{
    /**
     * make sure email isn't blacklisted if the flag is on in the DB
     *
     * @throws ValidationException
     */
    public static function blackListDomainValidation(string $email): void
    {
        $featureFlag = FeatureFlagController::findFeatureFlagByName('BLACKLIST_EMAIL_DOMAINS');
        if (!empty($featureFlag) && $featureFlag->activated) {
            $domain_name = substr(strrchr($email, '@'), 1);
            $blacklisted = (new EmailBlackListController())->showIsBlacklisted($domain_name);

            if ($blacklisted || preg_match('/(^|\.)gov(\.|$)|(^|\.)mil(\.|$)/i', $domain_name)) {
                throw ValidationException::withMessages([config('genericMessages.error.EMAIL_BLACKLISTED')]);
            }
        }
    }

    /**
     * Validates a list of emails against a blacklist of domains.
     *
     * @param  array|string  $emails  List of email addresses or a single email to validate.
     * @param  bool  $thowsOnError  Whether to throw a ValidationException if blacklisted emails are found (default: true).
     * @return array Returns an array of blacklisted emails if $thowsOnError is false, or throws an exception otherwise.
     *
     * This method checks whether the provided emails are associated with blacklisted domains.
     * It respects the feature flag 'BLACKLIST_EMAIL_DOMAINS' to enable or disable the validation.
     *
     * @throws ValidationException If blacklisted emails are found and $thowsOnError is true.
     */
    public static function blackListDomainListValidation(
        array|string $emails,
        bool $thowsOnError = true
    ): array {
        $featureFlag = FeatureFlagService::findByName('BLACKLIST_EMAIL_DOMAINS');
        if (!empty($featureFlag) && $featureFlag->activated) {
            $emailList = is_array($emails) ? $emails : [$emails];
            $emailSubdomains = collect($emailList)->map(fn ($email) => [
                'email' => $email,
                'subdomain' => strtolower(substr(strrchr($email, '@'), 1)),
            ]);

            $blackListedDomains = EmailBlackList::pluck('domain')->toArray();

            $pattern = '/(^|\.)gov(\.|$)|(^|\.)mil(\.|$)/i';

            $blackListedEmails = $emailSubdomains->filter(function ($emailData) use ($blackListedDomains, $pattern) {
                $subdomain = $emailData['subdomain'];
                if (in_array($subdomain, $blackListedDomains)) {
                    return true;
                }

                return preg_match($pattern, $subdomain);
            })->pluck('email')->toArray();

            if (count($blackListedEmails) > 0) {
                if ($thowsOnError) {
                    throw ValidationException::withMessages([
                        'messages' => config('genericMessages.error.EMAIL_BLACKLISTED'),
                        'emails' => $blackListedEmails,
                    ]);
                }

                return $blackListedEmails;
            }
        }

        return [];
    }

    /**
     * call api to see if the email is a valid email
     *
     *
     * @throws ValidationException
     */
    public static function validateEmailAddress(string $email): string
    {
        $emailVerifyResults = '';
        $featureFlag = FeatureFlagController::findFeatureFlagByName('VALIDATE_EMAIL_VALIDITY');
        if (!empty($featureFlag) && $featureFlag->activated) {
            $valid = true;

            try {
                // see if this email domain is whitelisted
                $whitelisted = EmailWhitelistService::isWhitelisted(UtilityService::getDomainFromEmail($email));

                if (!$whitelisted) {
                    $mailgunValidateUrl = config('services.mailgun.endpoint') . config('services.mailgun.validate_email_api_url');

                    $params = ['address' => $email];

                    $response = Http::withBasicAuth(config('services.mailgun.api_username'), config('services.mailgun.secret'))
                        ->get($mailgunValidateUrl, $params);

                    if (!$response->successful()) {
                        // if something is going wrong with mailgun, let the code continue
                        Log::error($response->toException());
                    } else {
                        $resp = json_decode($response->body());
                        $reason = $resp->reason; // this is an array
                        $result = $resp->result;
                        $risk = $resp->risk;

                        $emailVerifyResults = $risk . '/' . $result;

                        // check the results to see if they should be blocked
                        // custom validation that if risk is medium but the result is deliverable to let it pass thru
                        if (strtolower($risk) === 'medium' && strtolower($result) === 'deliverable') {
                            $valid = true;
                            Log::info("NOT Blocking user {$email} from registering because the risk is {$risk} and the result is {$result}");
                        } else {
                            $hasRisks = EmailBlockRisks::where('risk', $risk)->count();

                            if ($hasRisks > 0) {
                                $valid = false;
                                Log::info("Blocking user {$email} from registering because the risk is {$risk}");
                            } else {
                                $hasResults = EmailBlockResults::where('result', $result)->count();

                                if ($hasResults > 0) {
                                    $valid = false;
                                    Log::info("Blocking user {$email} from registering because the result is {$result}");
                                } else {
                                    $hasReasons = EmailBlockReasons::whereIn('reason', $reason)->count();

                                    if ($hasReasons > 0) {
                                        $valid = false;
                                        Log::info("Blocking user {$email} from registering because the reason is {$reason}");
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (\Exception $ex) {
                // if something is going wrong with mailgun, let the code continue
                Log::error($ex->getMessage());
            }

            if (!$valid) {
                throw ValidationException::withMessages([config('genericMessages.error.EMAIL_INVALID')]);
            }
        }

        return $emailVerifyResults;
    }

    public static function processEmailsList(
        Company $company, array $emailsList, bool $validateCompanyUsers, string $companyType): StdClass
    {
        $blackListedEmails = [];
        $emailsToSend = [];
        $errorReasons = [];
        $users = collect();
        if ($validateCompanyUsers) {
            $users = $company->allUsers()
                ->whereIn('users.email', $emailsList)
                ->with('roles:id,company_id')
                ->get();
        }
        if (CompanyService::companyIsCP($company)) {
            $existingEmails = $users->filter(function ($user) use ($company) {
                return RoleUserService::userReachedLimitOfRolesByCompany($user, $company);
            })->pluck('email')->toArray();
        } else {
            $existingEmails = $users->pluck('email')->toArray();
        }
        $existingInvitations = $company->invites(null, $companyType)->pluck('email')->toArray();
        $existingEmails = array_unique(array_merge($existingInvitations, $existingEmails));
        $repeatedEmails = array_intersect(array_map('strtolower', $existingEmails),
            array_map('strtolower', $emailsList));
        // Remove the existing users from the list to be sent
        $emailsToVerify = array_values(array_diff($emailsList, $existingEmails));
        foreach ($emailsToVerify as $email) {
            try {
                self::blackListDomainValidation($email);
                $emailsToSend[] = $email;
            } catch (ValidationException $e) {
                Log::debug(__CLASS__ . '::' . __FUNCTION__ . '::' . $e->getMessage());
                $blackListedEmails[] = $email;
            }
        }
        if (count($blackListedEmails) > 0) {
            $errorReasons[] = config('genericMessages.error.EMAIL_BLACKLISTED');
        }
        if (count($repeatedEmails) > 0) {
            $errorReasons[] = 'Emails already exist in ' . $company->name;
        }
        $result = new StdClass();
        $result->ok_emails = $emailsToSend;
        $result->not_ok_emails = array_merge($repeatedEmails, $blackListedEmails);
        $result->not_ok_reason = implode(', ', $errorReasons);

        return $result;
    }
}
