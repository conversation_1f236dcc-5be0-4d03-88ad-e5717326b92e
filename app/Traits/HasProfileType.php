<?php

namespace App\Traits;

use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Log;

trait HasProfileType
{
    public static function bootHasProfileType()
    {
        static::booted(function ($model) {
            if (!method_exists($model, 'profileType')) {
                throw new Exception(
                    'Profile not implemented for ' . get_class($model)
                );
            }
        });
    }

    public function getProfileRuleValue(
        string $ruleName
    ): mixed {
        $source = __CLASS__ . '::' . __FUNCTION__ . '::';
        $this->loadMissing('profileType');
        $profileTypeKey = $this->profileType->value;
        $ruleConfigName = self::prepareConfigurationName($profileTypeKey, $ruleName);
        $profileRulesValues = self::getProfileRulesValues($profileTypeKey, $this);
        $ruleValueArray = $profileRulesValues[$ruleName] ?? null;
        if ($ruleValueArray === null) {
            Log::warning("No value configured for Profile rule '{$ruleConfigName}'");

            return null;
        }
        Log::debug($source . 'Rule Value Array: ' . json_encode($ruleValueArray, JSON_PRETTY_PRINT));

        return self::parseRuleValue($ruleValueArray);
    }

    public function validateRuleMaxValue(
        string $ruleName,
        float $value,
        bool $trueOnNull = true
    ): bool {
        $ruleValue = $this->getProfileRuleValue($ruleName);
        if ($ruleValue === null) {
            return $trueOnNull;
        }

        return $value <= $ruleValue;
    }

    public function validateRuleBooleanValue(
        string $ruleName,
        bool $trueOnNull = true
    ): bool {
        $ruleValue = $this->getProfileRuleValue($ruleName);
        if ($ruleValue === null) {
            return $trueOnNull;
        }

        return in_array(strtolower($ruleValue), ['true', '1', 'yes', true]);
    }

    public function validateRuleValueBetweenDates(
        string $ruleName,
        Carbon $startDate,
        Carbon $endDate,
        bool $trueOnNull = true
    ): bool {
        $ruleValue = $this->getProfileRuleValue($ruleName);
        if ($ruleValue === null) {
            return $trueOnNull;
        }
        $date = Carbon::parse($ruleValue);

        return $date->between($startDate, $endDate);
    }

    public function vaidateRuleValueBeforeDate(
        string $ruleName,
        Carbon $date,
        bool $trueOnNull = true
    ): bool {
        $ruleValue = $this->getProfileRuleValue($ruleName);
        if ($ruleValue === null) {
            return $trueOnNull;
        }
        $dateToCompare = Carbon::parse($ruleValue);

        return $dateToCompare->lt($date);
    }

    public function vaidateRuleValueAfterDate(
        string $ruleName,
        Carbon $date,
        bool $trueOnNull = true
    ): bool {
        $ruleValue = $this->getProfileRuleValue($ruleName);
        if ($ruleValue === null) {
            return $trueOnNull;
        }
        $dateToCompare = Carbon::parse($ruleValue);

        return $dateToCompare->gt($date);
    }
}
