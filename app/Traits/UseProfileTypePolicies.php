<?php

namespace App\Traits;

use App\Helpers\RulesHelper;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Log;

trait UseProfileTypePolicies
{
    /**
     * Boot the trait and validate that the model implements the profileType method.
     */
    public static function bootUseProfileTypePolicies()
    {
        static::booted(function ($model) {
            if (!method_exists($model, 'profileType')) {
                throw new Exception(
                    'Profile not implemented for ' . get_class($model)
                );
            }
        });
    }

    /**
     * Get the value of all profile rules for this model.
     *
     * @return mixed The rule value or null if not found
     */
    public function getProfileRulesValues(): array
    {
        $source = __CLASS__ . '::' . __FUNCTION__ . '::';
        $this->loadMissing('profileType');
        $profileTypeKey = $this->profileType->value;
        $profileRulesValues = RulesHelper::getProfileRulesValues($profileTypeKey, $this);
        Log::debug($source . 'Profile Rules Values: ' . json_encode($profileRulesValues, JSON_PRETTY_PRINT));

        return $profileRulesValues;
    }

    /**
     * Get the value of a specific profile rule for this model.
     *
     * @param  string  $ruleName  The name of the rule to retrieve
     * @return mixed The rule value or null if not found
     */
    public function getProfileRuleValue(
        string $ruleName
    ): mixed {
        $source = __CLASS__ . '::' . __FUNCTION__ . '::';
        $this->loadMissing('profileType');
        $profileTypeKey = $this->profileType->value;
        $ruleConfigName = RulesHelper::prepareConfigurationName($profileTypeKey, $ruleName);
        $profileRulesValues = RulesHelper::getProfileRulesValues($profileTypeKey, $this);
        $ruleValueArray = $profileRulesValues[$ruleName] ?? null;
        if ($ruleValueArray === null) {
            Log::warning("No value configured for Profile rule '{$ruleConfigName}'");

            return null;
        }
        Log::debug($source . 'Rule Value Array: ' . json_encode($ruleValueArray, JSON_PRETTY_PRINT));

        return RulesHelper::parseRuleValue($ruleValueArray);
    }

    /**
     * Validate that a given value does not exceed the maximum allowed by a rule.
     *
     * @param  string  $ruleName  The name of the rule to check
     * @param  float  $value  The value to validate
     * @param  bool  $trueOnNull  Return true if rule value is null
     * @return bool True if value is within limit, false otherwise
     */
    public function validateRuleMaxValue(
        string $ruleName,
        float $value,
        bool $trueOnNull = true
    ): bool {
        $ruleValue = $this->getProfileRuleValue($ruleName);
        if ($ruleValue === null) {
            return $trueOnNull;
        }

        return $value <= $ruleValue;
    }

    /**
     * Validate a boolean rule value.
     *
     * @param  string  $ruleName  The name of the rule to check
     * @param  bool  $trueOnNull  Return true if rule value is null
     * @return bool True if rule evaluates to true, false otherwise
     */
    public function validateRuleBooleanValue(
        string $ruleName,
        bool $trueOnNull = true
    ): bool {
        $ruleValue = $this->getProfileRuleValue($ruleName);
        if ($ruleValue === null) {
            return $trueOnNull;
        }

        return in_array(strtolower($ruleValue), ['true', '1', 'yes', true]);
    }

    /**
     * Validate that a rule date value falls between two dates.
     *
     * @param  string  $ruleName  The name of the rule to check
     * @param  Carbon  $startDate  The start date for comparison
     * @param  Carbon  $endDate  The end date for comparison
     * @param  bool  $trueOnNull  Return true if rule value is null
     * @return bool True if rule date is between the given dates
     */
    public function validateRuleValueBetweenDates(
        string $ruleName,
        Carbon $startDate,
        Carbon $endDate,
        bool $trueOnNull = true
    ): bool {
        $ruleValue = $this->getProfileRuleValue($ruleName);
        if ($ruleValue === null) {
            return $trueOnNull;
        }
        $date = Carbon::parse($ruleValue);

        return $date->between($startDate, $endDate);
    }

    /**
     * Validate that a rule date value is before a given date.
     *
     * @param  string  $ruleName  The name of the rule to check
     * @param  Carbon  $date  The date for comparison
     * @param  bool  $trueOnNull  Return true if rule value is null
     * @return bool True if rule date is before the given date
     */
    public function vaidateRuleValueBeforeDate(
        string $ruleName,
        Carbon $date,
        bool $trueOnNull = true
    ): bool {
        $ruleValue = $this->getProfileRuleValue($ruleName);
        if ($ruleValue === null) {
            return $trueOnNull;
        }
        $dateToCompare = Carbon::parse($ruleValue);

        return $dateToCompare->lt($date);
    }

    /**
     * Validate that a rule date value is after a given date.
     *
     * @param  string  $ruleName  The name of the rule to check
     * @param  Carbon  $date  The date for comparison
     * @param  bool  $trueOnNull  Return true if rule value is null
     * @return bool True if rule date is after the given date
     */
    public function vaidateRuleValueAfterDate(
        string $ruleName,
        Carbon $date,
        bool $trueOnNull = true
    ): bool {
        $ruleValue = $this->getProfileRuleValue($ruleName);
        if ($ruleValue === null) {
            return $trueOnNull;
        }
        $dateToCompare = Carbon::parse($ruleValue);

        return $dateToCompare->gt($date);
    }
}
