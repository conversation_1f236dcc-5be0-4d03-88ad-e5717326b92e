<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withProviders([
        \PHPOpenSourceSaver\JWTAuth\Providers\LaravelServiceProvider::class,
        \Barryvdh\DomPDF\ServiceProvider::class,
    ])
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        api: __DIR__ . '/../routes/api.php',
        commands: __DIR__ . '/../routes/console.php',
        channels: __DIR__ . '/../routes/channels.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->redirectGuestsTo(fn () => route('login'));

        $middleware->append(\Spatie\HttpLogger\Middlewares\HttpLogger::class);

        $middleware->throttleApi();
        $middleware->api([\App\Http\Middleware\DomainRequestMiddleware::class,
            \App\Http\Middleware\ValidateRolesPermissionsMiddleware::class]);

        $middleware->alias([
            'bulk-upload' => \App\Http\Middleware\BulkUploadRolesPermissionsMiddleware::class,
            'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
            'jwt.auth' => \PHPOpenSourceSaver\JWTAuth\Middleware\GetUserFromToken::class,
            'jwt.refresh' => \PHPOpenSourceSaver\JWTAuth\Middleware\RefreshToken::class,
            'jwt.verify' => \App\Http\Middleware\JwtMiddleware::class,
            'verified' => \App\Http\Middleware\EnsureUserIsVerified::class,
        ]);

        $middleware->validateCsrfTokens(except: [
            '/telescope/*', // <-- exclude this route
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
