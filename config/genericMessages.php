<?php
/*
 * THIS FILE CONTAINS THE GENERIC MESSAGES AND CODES FOR THE COMUNICATION BETWEEN FE AND BE
 * IS DIVIDED WITH 3 SECTIONS (success, warning and error messages)
 * THIS IS THE WAY TO USE IT
 *      throw ValidationException::withMessages(["message" => config("genericMessages.error.ANSWER_ALREADY_EXISTS")]);
*/
return [
    'success' => [
        'VERIFICATION_SUCCESSFUL' => 'Verification Successful',
        'RESEND_VERIFICATION_SUCCESSFUL' => 'Resent verification',
        'VERIFICATION_CODE_SUCCESSFULLY_SENT' => 'Verification code sent',
        'NO_RESULTS' => 'No records found',
        'NO_METRICS_RESULTS' => 'No metrics found',
        'MESSAGE_SENT_SUCCESSFULLY' => 'Your message was successfully sent to the list "%s"',
        'PLAID_BANK_LINKED' => 'Bank account linked.',
        'INVITE_SENT' => 'Invite sent.',
        'INVITE_WERE_SENT' => 'The pending invites were resent.',
    ],
    'warning' => [
        'EMAIL_PARAMETER_NOT_FOUND' => 'EMAIL_PARAMETER_NOT_FOUND',
        'VERIFICATION_LINK_ALREADY_USED' => 'VERIFICATION_LINK_ALREADY_USED',
        'CAN_NOT_SET_SAME_PROFILE_TYPE' => 'CAN_NOT_SET_SAME_PROFILE_TYPE',
        'FEATURE_FLAG_NOT_FOUND' => 'FEATURE_FLAG_NOT_FOUND',
        'FEATURE_FLAG_NOT_ACTIVE' => 'FEATURE_FLAG_NOT_ACTIVE',
        'PLEASE_TRY_AGAIN' => 'We encountered an issue. Please try again. If the problem persists, contact Support for further assistance.',
    ],
    'error' => [
        'CAN_NOT_BE_VISIBLE_WHEN_PARENT_IS_NOT_VISIBLE' => 'Cannot make this visible because the parent category is not visible.',
        'ROLE_NOT_BELONGS_TO_COMPANY' => 'The selected role does not belong to the specified company.',
        'USER_CAN_NOT_DELETE_OWN_ROLE' => 'You can not delete your own role.',
        'USER_CAN_NOT_UPDATE_OWN_ROLE' => 'You can not update your own role.',
        'TEMPLATE_ROLE_NOT_DELETABLE' => 'The selected template role can not be deleted. It is a system predefined role.',
        'CONTRACT_NOT_BELONGS_TO_COMPANY' => 'The selected contract does not belong to the specified company.',
        'CONTACT_NOT_BELONGS_TO_COMPANY' => 'The selected contact does not belong to the specified company.',
        'DEAL_CANNOT_BE_APPROVED' => 'This deal cannot be approved.',
        'DEAL_CANNOT_BE_DECLINE' => 'This deal cannot be declined.',
        'DEAL_CANNOT_BE_DELETED' => 'This deal cannot be deleted.',
        'DEAL_CANNOT_BE_CHANGED' => 'This deal cannot be changed.',
        'DEAL_CANNOT_BE_WITHDRAWN' => 'This deal cannot be withdrawn.',
        'DEAL_USER_CANNOT_REQUEST_INFO' => 'You cannot request info for this deal.',
        'DEAL_NOT_BELONGS_TO_COMPANY' => 'The selected deal does not belong to the specified company.',
        'DEAL_DOCUMENT_NOT_BELONGS_TO_COMPANY' => 'The selected document does not belong to the specified company.',
        'NOTIFICATION_NOT_BELONGS_TO_CONTRACT' => 'The selected notification does not belong to the specified contract.',
        'CLIENT_NOT_BELONGS_TO_COMPANY' => 'The selected company does not belong to the specified parent company.',
        'PLEASE_LOG_IN' => 'Please log in',
        'UNAUTHORIZED' => 'The user is not authorized',
        'UNAUTHORIZED_PERMISSION' => 'The user is not authorized, add the permission to the user´s role for the selected company.',
        'USER_NOT_PUBLIC' => 'USER_NOT_PUBLIC',
        'USER_IS_INACTIVE' => 'USER_IS_INACTIVE',
        'USER_HAS_NO_COMPANY' => 'User does not a company or role assigned, contact us for assistance.',
        'USER_HAS_NO_MAIN_COMPANY' => 'User does not a main company, contact us for assistance.',
        'USER_IN_ACTIVE_REVIEW_MESSAGE' => 'User no longer active',
        'NOT_FOUND' => 'Not found',
        'COMPANY_ID_NOT_FOUND' => 'as_company_id not found, can not continue with the request.',
        'COMPANY_TYPE_NOT_FOUND' => 'The selected company has no TYPE. BE Logic ERROR. Contact BE team.',
        'CONFIGURATION_KEY_NOT_FOUND' => 'Configuration Key not found, remember to use UPPERCASE key if needed.',
        'FILE_NOT_FOUND' => 'File not found',
        'VERIFICATION_NEEDED' => 'The user did not finished the registration process, the account is not verified.',
        'UNABLE_TO_FIND_ACCOUNT' => 'We`re unable to find your account. Please confirm your email address and try again.',
        'VERIFICATION_LINK_EXPIRED' => 'The verification link is expired',
        'INVALID_VERIFICATION_LINK' => 'Your link is invalid, make sure do you have not changed the link sent in the email',
        'EMAIL_DOES_NOT_EXISTS' => 'This email does not exist in our database',
        'ACCOUNT_ALREADY_CONFIRMED' => 'ACCOUNT_ALREADY_CONFIRMED',
        'EMAIL_ALREADY_VERIFIED' => 'EMAIL_ALREADY_VERIFIED',
        'EMAIL_BLACKLISTED' => 'The entered email domain has not been accepted. Please try again and ensure you haven’t used Gmail or Yahoo. If the problem persists please let us know and we’ll look into it for you.',
        'EMAIL_INVALID' => 'There seems to be an issue with your domain. Please contact us to resolve this.',
        'ATTENDANCE_ALREADY_EXISTS' => 'ATTENDANCE_ALREADY_EXISTS',
        'USER_HAS_NO_ATTEND_PITCH_EVENT' => 'USER_HAS_NO_ATTEND_PITCH_EVENT',
        'USER_ALREADY_ANSWERED_QUESTION' => 'USER_ALREADY_ANSWERED_QUESTION',
        'POLL_QUESTION_ALREADY_SHOWN' => 'POLL_QUESTION_ALREADY_SHOWN',
        'POLL_QUESTION_NOT_DELETABLE_HAS_ANSWERS' => 'You cannot delete this poll questions because users have answered it previously',
        'POLL_QUESTION_ACCEPTS_ONLY_SINGLE_ANSWER' => 'POLL_QUESTION_ACCEPTS_ONLY_SINGLE_ANSWER',
        'VENDOR_ID_REQUIRED' => 'VENDOR_ID_REQUIRED',
        'AD_LOCATION_NOT_VALID' => 'The selected locations can not be used for the advertisement card type',
        'AD_ALREADY_SCHEDULED' => 'Selected vendor is already sponsoring this category for the selected period',
        'POLL_OPTION_NOT_DELETABLE_HAS_ANSWERS' => 'You cannot delete this poll option because users have answered it previously',
        'POLL_OPTION_NOT_DELETABLE_HAS_CHART_OPTIONS' => 'You cannot remove this option. Please check ChannelChart Metrics to modify a metric related to this item',
        'POLL_QUESTION_NOT_DELETABLE_HAS_CHART_QUESTIONS' => 'You cannot remove this question. Please check ChannelChart Metrics to modify a metric related to this item',
        'PITCH_EVENT_NOT_FOUND' => 'PITCH_EVENT_NOT_FOUND',
        'COMPANY_IS_NOT_OF_TYPE_VENDOR' => 'COMPANY_IS_NOT_OF_TYPE_VENDOR',
        'COMPANY_IS_NOT_OF_TYPE_MSP' => 'COMPANY_IS_NOT_OF_TYPE_MSP',
        'COMPANY_IS_NOT_OF_TYPE_MSP_CLIENT' => 'The selected company is not a valid MSP Client',
        'COMPANY_IS_NOT_VALID_AFFILIATE' => 'The selected company is not a valid affiliate',
        'COMPANY_CAN_NOT_BE_LOCATION' => 'The selected company can not be a location company',
        'VALUE_ALREADY_TAKEN' => 'The value is already taken',
        'FIX_ID_AND_TRY_AGAIN' => 'Fix the ID and try again',
        'SUPER_ADMIN_ROLE_NOT_ALLOWED_TO_BE_MODIFIED' => 'The super admin role is not allowed to be modified',
        'VENDOR_ALREADY_SCHEDULED' => 'VENDOR_ALREADY_SCHEDULED',
        'CATEGORY_ALREADY_ASSIGNED' => 'CATEGORY_ALREADY_ASSIGNED',
        'CATEGORY_NAME_ALREADY_EXISTS' => 'Category name already exists',
        'SUBCATEGORY_MUST_BE_RELATED_TO_CATEGORY' => 'The subcategory must be a child of the category',
        'MAX_VENDOR_CATEGORIES_REACHED' => 'MAX_VENDOR_CATEGORIES_REACHED',
        'INVALID_VENDOR_HANDLE' => 'INVALID_VENDOR_HANDLE',
        'HANDLE_ALREADY_EXISTS' => 'HANDLE_ALREADY_EXISTS',
        'DATA_ALREADY_EXISTS' => 'DATA_ALREADY_EXISTS',
        'COMPANY_NOT_ALLOWED_TO_HAVE_CLIENTS' => 'The company cannot have clients based on the current subscription. Please contact sales for more information.',
        'INVALID_INVITATION_SIGNATURE' => 'This invitation is no longer available. If you need assistance, please contact us for assistance.',
        'EMAIL_ALREADY_EXISTS' => 'The email address already exists',
        'INVALID_VERIFICATION_CODE' => 'The code you have entered is not valid',
        'EXPIRED_VERIFICATION_CODE' => 'The code you have entered has expired',
        'VERIFIED_VERIFICATION_CODE' => 'The code you have entered has already been verified',
        'VERIFICATION_CODE_NOT_VERIFIED' => 'You need to verify the verification code to continue',
        'INVALID_REFERRAL' => 'Only referral responses allow to have a referral value',
        'FRIENDLY_URL_ALREADY_EXISTS' => 'FRIENDLY_URL_ALREADY_EXISTS',
        'CANNOT_DELETE_MEDIA_FROM_OTHER_USERS' => 'CANNOT_DELETE_MEDIA_FROM_OTHER_USERS',
        'MEDIA_IS_NOT_COMPANY_AVATAR_OR_TOPBANNER' => 'MEDIA_IS_NOT_COMPANY_AVATAR_OR_TOPBANNER',
        'CANNOT_CREATE_MEDIA_ON_OTHER_USERS_PROFILE' => 'CANNOT_CREATE_MEDIA_ON_OTHER_USERS_PROFILE',
        'CANNOT_DOWNLOAD_MEDIA_FROM_OTHER_USERS' => 'CANNOT_DOWNLOAD_MEDIA_FROM_OTHER_USERS',
        'CANNOT_EDIT_MEDIA_FROM_OTHER_USERS' => 'CANNOT_EDIT_MEDIA_FROM_OTHER_USERS',
        'MEDIA_DOES_NOT_BELONG_TO_REQUESTED_USER' => 'MEDIA_DOES_NOT_BELONG_TO_REQUESTED_USER',
        'MEDIA_DOES_NOT_BELONG_TO_REQUESTED_COMPANY' => 'MEDIA_DOES_NOT_BELONG_TO_REQUESTED_COMPANY',
        'BROADCAST_MESSAGE_DOES_NOT_BELONG_TO_USER' => 'BROADCAST_MESSAGE_DOES_NOT_BELONG_TO_USER',
        'INVALID_CLAIMER_USER' => 'The user is not a valid Super Admin of the company.',
        'USER_NOT_FROM_COMPANY' => 'The user is not from the company.',
        'INVALID_PARTNER_USER' => 'INVALID_PARTNER_USER',
        'INVALID_FILTER' => 'INVALID_FILTER',
        'USER_IS_ALREADY_CLAIMER' => 'USER_IS_ALREADY_CLAIMER',
        'COMPANY_ALREADY_HAS_PARTNER_PAGE' => 'COMPANY_ALREADY_HAS_PARTNER_PAGE',
        'ANSWER_ALREADY_EXISTS' => 'ANSWER_ALREADY_EXISTS',
        'VENDOR_ALREADY_EXISTS' => 'VENDOR_ALREADY_EXISTS',
        'QUESTION_ALREADY_EXISTS' => 'A question with the same type and question title already exists',
        'FORM_ALREADY_EXISTS' => 'A form with the same type already exists',
        'REGISTER_HAS_DEPENDANT_VALUES' => 'Unable to modify, register has dependant values',
        'JOB_TITLE_HAS_DEPENDANT_VALUES' => 'Unable to delete because the job title is assigned to users',
        'OPTION_ALREADY_EXISTS' => 'The option for the selected question already exits',
        'MORE_ANSWERS_NOT_ALLOWED' => 'Type of question does not allows to have multiple answers',
        'MIN_ANSWERS_VALIDATION' => 'Type of question need at least the following answers: ',
        'MAX_ANSWERS_VALIDATION' => 'Type of question need not more than the following answers: ',
        'DELETE_OPTIONS_BEFORE_UPDATING_QUESTION_TYPE' => 'You must delete the question´s options before updating the question type to',
        'OPTIONS_NOT_FROM_QUESTION' => 'Option is not from the selected question',
        'OPTIONS_NOT_ALLOWED' => 'Options can not be added to this type of question',
        'OPTIONS_NOT_MODIFIABLE' => 'Options can not be modified to this type of question',
        'FIELD_REQUIRED_FOR_TYPE_OF_QUESTION' => 'The field is required for the selected question type',
        'FIELD_REQUIRED' => 'The field is required',
        'DATE_NOT_VALID' => 'The field is not a valid date',
        'PROFILE_RULE_ALREADY_EXISTS' => 'PROFILE_RULE_ALREADY_EXISTS',
        'INVALID_ENUM_TYPE' => 'INVALID_ENUM_TYPE',
        'DEFAULT_USER_PROFILE_TYPE_NOT_DEFINED' => 'DEFAULT_USER_PROFILE_TYPE_NOT_DEFINED',
        'DEFAULT_USER_PROFILE_TYPE_HAS_MULTIPLE_VALUES' => 'DEFAULT_USER_PROFILE_TYPE_HAS_MULTIPLE_VALUES',
        'USER_PROFILE_TYPE_TABLE_IS_EMPTY' => 'USER_PROFILE_TYPE_TABLE_IS_EMPTY',
        'DEFAULT_COMPANY_PROFILE_TYPE_NOT_DEFINED' => 'DEFAULT_COMPANY_PROFILE_TYPE_NOT_DEFINED',
        'COMPANY_PROFILE_TYPE_TABLE_IS_EMPTY' => 'COMPANY_PROFILE_TYPE_TABLE_IS_EMPTY',
        'SUBJECT_ID_DOES_NOT_EXIST_FOR_SUBJECT_TYPE' => 'SUBJECT_ID_DOES_NOT_EXIST_FOR_SUBJECT_TYPE',
        'MODEL_ID_DOES_NOT_EXIST_FOR_MODEL_TYPE' => 'MODEL_ID_DOES_NOT_EXIST_FOR_MODEL_TYPE',
        'ACTION_NOT_IMPLEMENTED' => 'ACTION_NOT_IMPLEMENTED',
        'CONFIGURATION_VALUE_NOT_FOUND' => 'CONFIGURATION_VALUE_NOT_FOUND',
        'USER_ALREADY_SEND_FEEDBACK' => 'USER_ALREADY_SEND_FEEDBACK',
        'USER_ROLE_LIMIT' => 'The user has reached the limit of roles per company',
        'USER_HAS_ROLE' => 'The user already has the role',
        'ROLE_DOES_NOT_EXIST' => 'The role does not exist',
        'ROLE_HAS_PERMISSION' => 'The role already has the permission',
        'ORDER_PROPERTY_REQUIRED' => 'The element must contain ORDER property',
        'MEDIA_DOES_NOT_BELONG_TO_MEDIA_GALLERY' => 'Media does not belong to selected media gallery',
        'MEDIA_GALLERY_DOES_NOT_BELONG_TO_USER' => 'Media Gallery does not belong to user',
        'UNAUTHORIZED_CANNOT_VIEW_REQUESTED_RESOURCE' => 'UNAUTHORIZED_CANNOT_VIEW_REQUESTED_RESOURCE',
        'MEDIA_GALLERY_MAX_ITEMS_LIMIT' => 'MEDIA_GALLERY_MAX_ITEMS_LIMIT',
        'MSP_NOT_FOUND' => 'MSP_NOT_FOUND',
        'SOME_FOCUS_OPTION_IDS_DO_NOT_BELONG_TO_SENT_FOCUS' => 'SOME_FOCUS_OPTION_IDS_DO_NOT_BELONG_TO_SENT_FOCUS',
        'OPTIONS_PERCENTAGE_SUM_IS_NOT_100' => 'Percentage total must equal 100',
        'INVALID_SUBJECT_TYPE' => 'INVALID_SUBJECT_TYPE',
        'USER_PRODUCT_BOOKMARK_ALREADY_EXISTS' => 'The chosen product is already bookmarked for this user.',
        'PRODUCT_HAS_RELATED_CONTRACTS' => 'The chosen product cannot be removed because there are contracts related to it.',
        'QUESTION_ANSWER_NOT_FROM_QUESTION' => 'QUESTION_ANSWER_NOT_FROM_QUESTION',
        'QUESTION_ONLY_ACCEPTS_OPTIONS' => 'QUESTION_ONLY_ACCEPTS_OPTIONS',
        'QUESTION_ONLY_ACCEPTS_ANSWER' => 'QUESTION_ONLY_ACCEPTS_ANSWER',
        'REVIEW_QUESTION_ONLY_ACCEPTS_OPTIONS' => 'REVIEW_QUESTION_ONLY_ACCEPTS_OPTIONS',
        'REVIEW_QUESTION_ONLY_ACCEPTS_ANSWER' => 'REVIEW_QUESTION_ONLY_ACCEPTS_ANSWER',
        'REVIEW_ANSWER_NOT_VALID' => 'Only open text answers can be updated.',
        'REVIEW_ANSWER_NOT_FROM_REVIEW' => 'REVIEW_ANSWER_NOT_FROM_REVIEW',
        'REVIEW_REPLY_NOT_FROM_REVIEW' => 'REVIEW_REPLY_NOT_FROM_REVIEW',
        'USER_ALREADY_HAS_REVIEWS_FOR_PRODUCT' => 'User already has reviews for this product.',
        'REVIEW_CREATION_FORBIDDEN' => 'You cannot write a review for this product.',
        'REVIEW_ALREADY_APPROVED' => 'The review is already approved.',
        'INVALID_REVIEW_STATUS' => 'The review status is not valid.',
        'PROFANITY_FOUND' => 'A profane word has been detected in the text. Please review and adjust the content as necessary.',
        'INVALID_INDUSTRY_EVENT_APPROVED' => 'The actual industry event status can only be updated to archived or draft.',
        'INVALID_INDUSTRY_EVENT_AWAITING_APPROVAL' => 'The actual industry event status can only be updated to approved or rejected.',
        'INVALID_INDUSTRY_EVENT_DRAFT' => 'The actual industry event status can only be updated to approved or archived.',
        'INVALID_INDUSTRY_EVENT_ARCHIVE' => 'The actual industry event status can only be updated to draft or approved.',
        'INVALID_INDUSTRY_EVENT_REJECTED' => 'The actual industry event status can not be modified.',
        'INVALID_INDUSTRY_EVENT_REJECTED_2' => 'The actual industry event status can only be updated to awaiting_approval.',
        'INVALID_INDUSTRY_EVENT_UNARCHIVED' => 'Industry events can only be unarchived when they are in archived or approved status.',
        'INVALID_INDUSTRY_EVENT_ARCHIVED' => 'Industry events can only be archived when they are in approved or awaiting_approval status.',
        'INVALID_VALUES_LIST' => 'Values list contains elements that are not valid. ',
        'USER_BANNED_PUBLIC_CHAT' => 'User is banned from public chat',
        'MESSAGE_NOT_FOUND' => 'Message not found',
        'MESSAGE_NOT_SENT' => 'Failed to send your message to the list "%s". Please try again. If the issue persists, contact us for assistance.',
        'MESSAGE_SENT_WITH_ERRORS' => 'The message you sent to the list "%s" could not be delivered to %s %s.',
        'DISALLOW_DELETE_PRODUCT_HAS_REVIEWS' => 'Uh oh! This product cannot be deleted, please contact customer service or your sales representative for assistance.',
        'MSP_ALREADY_INVITED_REQUESTED_TO_PORTAL' => 'The MSP is already invited or requested to the portal.',
        'MSP_ALREADY_PARTNER' => 'The MSP is already a partner.',
        'EMAIL_IS_FROM_A_VENDOR_COMPANY' => 'The email is from a vendor company.',
        'MSP_NOT_PARTNER_OR_INVITED' => 'The MSP is not a partner or invited to the portal.',
        'PARTNER_PAGE_NOT_ACTIVE' => 'PARTNER_PAGE_NOT_ACTIVE',
        'PARTNER_INVITATION_NOT_FOUND' => 'The partner invitation was not found.',
        'PARTNER_INVITATION_IS_ALREADY_ACCEPTED' => 'The partner invitation is already accepted.',
        'PARTNER_INVITATION_IS_REJECTED' => 'The partner invitation is rejected.',
        'PARTNER_INVITATION_INVALID' => 'The partner invitation is Invalid.',
        'FOLLOWER_NOT_FOUND' => 'The MSP cannot be found.',
        'PLIVO_ACCOUNT_WRONG' => 'The sms failed, please check your email for the verification code.',
        'COMPANY_NOT_OWNS_CONTENT' => 'This content doesn\'t belong to company.',
        'COMPANY_NOT_OWNS_SECTION' => 'This section doesn\'t exists in partner page.',
        'SECTION_NOT_OWNS_CONTENT' => 'This content doesn\'t exists in section.',
        'CONTENT_NOT_BELONGS_SECTION' => 'This content doesn\'t belongs in section.',
        'CONTENT_ALREADY_EXISTS_IN_SECTION' => 'This content already exists in section.',
        'DOESNT_EXISTS' => 'DOESNT_EXISTS',
        'MEDIA_DOES_NOT_BELONG_TO_PARTNER_ASSET' => 'Media does not belongs to partner asset',
        'COMPANY_NOT_ALLOWED_TO_CREATE_PARTNER_PAGE' => 'Company not allowed to create partner page',
        'COMPANY_IS_NOT_PARTNER' => 'You’re not authorized to view the content. Please contact your vendor to be invited to their portal.',
        'URL_NOT_VALID' => 'URL is not valid',
        'JOBS_KEY_NOT_FOUND' => 'Job id not found, remember to use valid id.',
        'CRON_JOB_FORMAT' => 'The cron is not a valid cron expression. example: mins hours day month day_of_the_week, send only Int values or *',
        'JOB_ID_FORMAT' => 'The job id format is not valid',
        'CP_ID_NOT_ALLOWED' => 'The Channel program ID is not allowed',
        'ONLY_CP_ID_ALLOWED' => 'Only Channel program is allowed to use this parameter. Verify the user\'s company or add the as_company_id to the request.',
        'PAST_EVENT_CANNOT_BE_FEATURED' => 'Past events cannot be featured',
        'EVENT_SHOULD_BE_APPROVED' => 'Event should be approved',
        'COMPANY_SUPPORT_SCHEDULE' => 'Sorry, we’re not available at the moment. Please leave a message! Our support hours:',
        'CUSTOM_MEDIA_VISIBILITY_NOT_ALLOWED' => 'Custom media visibility is not allowed',
        'CHECK_EVENT_FEATURE_FLAG' => 'End date/time must not overlap another featured event',
        'CLIENT_ALREADY_ASSIGNED_TO_STACK' => 'This client is already associated with this stack.',
        'COMPANY_WITH_CATEGORY_AND_PRODUCT_ALREADY_ADDED_TO_STACK' => 'Company with this category and product already added to stack',
        'DUPLICATED_CUSTOMER_STACK_MESSAGE' => 'This is already added to this category in your stack',
        'DUPLICATED_CUSTOMER_STACK_INSTRUCTIONS' => 'Please select a different category/subcategory or change the product name',
        'PRODUCT_CANNOT_BE_FOUND_OR_CREATED' => 'The product cannot be found or created',
        'VENDOR_CANNOT_BE_FOUND_OR_CREATED' => 'The vendor cannot be found or created',
        'COMPANY_WITH_CATEGORY_AND_PRODUCT_ALREADY_ADDED_TO_MARKETPLACE' => 'Company already added to marketplace',
        'FILE_WILL_BE_PROCESSED' => 'Your file is being processed.',
        'USER_NOT_ALLOWED_TO_UPLOAD_FILE' => 'You don’t have permission to upload files to this page. Please contact support for assistance.',
        'INTERNAL_ERROR_UPLOADING_FILE' => 'An error occurred while processing your file. Please try again. If the problem continues, contact support.',
        'INVALID_FILE_GENERIC_ERROR' => 'There was an issue with the file you selected. Please try again, or contact support if the problem persists.',
        'INVALID_FILE_EMPTY' => 'The file you selected appears to be empty or corrupted. Please choose a different file and try again.',
        'INVALID_CSV_FILE' => 'The selected file has issues. Please correct them or choose a different file and try again.',
        'INVALID_CSV_FILE_ONLY_HEADERS' => 'The file only contains headers and no data. Please choose a different file and try again.',
        'INVALID_CSV_FILE_COLUMNS' => 'The file columns don’t match the required template. Please choose a different file and try again.',
        'INVALID_CSV_BAD_DATA' => 'The file has missing or inconsistent data. Please correct it or select a different file and try again.',
        'CSV_FILE_PROCESSED_SUCCESSFULLY' => 'Your file was processed successfully.',
        'INVALID_CONTRACTS_CSV_FILE' => 'Error importing contracts. Please check the file format and try again.',
        'INVALID_AFFILIATES_INVITATION_CSV_FILE' => 'There seems to be an issue with the file you selected. The file appears to be blank. Please choose a different file and try again.',
        'INVALID_AFFILIATES_CSV_HEADER' => 'Please check the file you are trying to use, the name of the column is incorrect, it should be "email".',
        'INVALID_AFFILIATE_COMPANY_TYPE' => 'Your email is already linked to a non-affiliated company. Please contact us for further assistance.',
        'TANGO_CATALOG_ALREADY_EXISTS' => 'Tango catalog with utid and catalog type already exists',
        'TANGO_CATALOG_DOESNT_EXISTS' => 'Tango catalog utid does not exists',
        'TANGO_CUSTOMER_OR_ACCOUNT_DOESNT_EXIST' => 'Tango customer or account does not exist',
        'TANGO_API_DOESNT_EXIST' => 'Tango API does not exist',
        'TANGO_API_KEY_NOT_SET' => 'Tango API key not set',
        'REVIEW_ALREADY_PAID' => 'Review already paid',
        'TANGO_ORDER_FAILED' => 'Tango order failed',
        'REVIEW_STATUS_NOT_APPROVED_OR_FLAGGED' => 'Review status not approved or flagged',
        'TANGO_MAX_SINGLE_PAYOUT_EXCEEDED' => 'Tango max single payout exceeded',
        'TANGO_TOTAL_MAX_PAYOUT_EXCEEDED' => 'Tango total max payout exceeded',
        'VIDEO_FILE_VALIDATION' => 'The video must be a file of type: mp4, mov, ogx, oga, ogv, ogg, webm.',
        'DOCUMENT_FILE_VALIDATION' => 'The document must be a file of type: doc, docx, ppt, pptx, pdf, rtf, xls, xlsx, txt, csv, odp, key.',
        'YOU_CANNOT_DELETE_THIS_REVIEW' => 'You cannot delete this review',
        'REVIEW_ABANDONED' => 'Review abandoned',
        'REVIEW_STATUS_ABANDONED' => 'you can\'t change the status because status is abandoned',
        'FOLDER_CONTENT_ALREADY_EXISTS' => 'Content already exists for the folder.',
        'CONTENT_ALREADY_EXISTS' => 'File already added to %s folder',
        'CHECK_AVAILABLE_BULLETIN' => 'End date/time must not overlap another bulletin for this vendor',
        'BULLETIN_ALREADY_INACTIVE' => 'Bulletin already inactive',
        'CHECK_BULLETIN_DURATION' => 'The duration of the broadcast message must be set in increments based on 1 hour intervals.',
        'CANNOT_GET_CYCLR_TOKEN' => 'Cannot retrieve Cyclr Token at this time. Please try again later.',
        'CANNOT_GET_CYCLR_MARKETPLACE_ID' => 'Cannot retrieve Cyclr Marketplace at this time. Please try again later.',
        'USER_REQUEST_NOT_BELONGS_TO_COMPANY' => 'The selected request does not belong to the specified company',
        'COMPANY_ALREADY_REQUESTED_CHANNEL_DEAL' => 'A request for this deal has already been submitted by your company. Please check with your team for further details.',
        'IS_NOT_VENDOR_COMPANY' => 'The company is not a vendor',
        'IS_NOT_MSP_COMPANY' => 'The company is not a MSP',
        'INVITE_NOT_SENT' => 'Invite not sent.',
        'CHANNEL_DEAL_STATUS_NOT_ALLOWED' => 'Status not allowed for the channel deal',
        'CHANNEL_DEAL_DELETE_NOT_PENDING_DEAL' => 'Only pending channel deals can be deleted',
        'PLAID_ERROR_LINKING_BANK' => 'Error linking bank account linked.',
        'PLAID_ERROR_TRANSACTION_NOT_BELONG_ACCOUNT' => "Transaction doesn't belong to the company",
        'PLAID_ERROR_SUBSCRIPTION_NOT_BELONG_ACCOUNT' => "Subscription doesn't belong to the company",
        'PLAID_ERROR_BANK_ACCOUNT_NOT_BELONG_TO_COMPANY' => "Bank Account doesn't belong to the company",
        'PLAID_ERROR_BANK_ACCOUNT_CAN_NOT_BE_SYNCED' => 'Bank Account reached the number of time that can be synced',
        'STATUS_SCOPE_INVALID_ENTITY' => 'The provided entity is not valid',
        'STATUS_SCOPE_UNCRONTROLLED_ENTITY' => 'The provided entity does not have a valid status',
        'STATUS_SCOPE_REASON_NOT_FOUND' => 'The provided reason could not be found',
        'RULE_NOT_FOUND' => 'The required rule to access this resource cannot be found',
    ],
];
