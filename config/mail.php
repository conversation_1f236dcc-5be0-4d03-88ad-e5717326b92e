<?php

use App\Enums\Email\EmailBladeFiles;

return [

    'mailers' => [
        'smtp_bettertracker' => [
            'transport' => 'smtp',
            'url' => env('MAIL_MYITSPEND_URL'),
            'host' => env('MAIL_MYITSPEND_HOST', 'smtp.mailgun.org'),
            'port' => env('MAIL_MYITSPEND_PORT', 587),
            'encryption' => env('MAIL_MYITSPEND_ENCRYPTION', 'tls'),
            'username' => env('MAIL_MYITSPEND_USERNAME'),
            'password' => env('MAIL_MYITSPEND_PASSWORD'),
            'from_address' => env('MAIL_MYITSPEND_FROM_ADDRESS', '<EMAIL>'),
            'from_name' => env('MAIL_MYITSPEND_FROM_NAME', 'BetterTracker'),
            'timeout' => null,
            'local_domain' => env('MAIL_MYITSPEND_EHLO_DOMAIN'),
        ],

        'mailgun' => [
            'transport' => 'mailgun',
            // 'client' => [
            //     'timeout' => 5,
            // ],
        ],
    ],

    'default_content' => [
        EmailBladeFiles::getKey(EmailBladeFiles::AbandonedReviewsReminder) => [
            'subject' => 'You have a review waiting to be finished.',
            'header_text' => '<p>Just a quick reminder that you started a review for {{model_name}} on {{created_at}}. Your valuable insights will help the IT community make informed buying decisions.</p>',
            'intro_text' => '<p>Login today, to complete and submit the review. To find your reviews, click your name > My Account > My Product Reviews. Once there click on the review titled {{review_title}}.</p>',
            'footer_text' => '<p>This message was intended for {{first_name}} {{email}}. You’re receiving this email because you’re a registered user of Channel Program.</p>',
            'description' => 'Email will be sent based on a user who had started but hasn’t finished a product review, which leaves it in an abandoned state',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::AdminMSPNewClaimersResponsesNotification) => [
            'subject' => 'New Claimers Responses Daily Report On Channel Program.',
            'header_text' => '<h1 style="text-align: center;font-size: 24px">Daily IT Service Providers Claimers Responses</h1>',
            'intro_text' => '<p>Hi CP Admin,<p/><p>You have {{new_claimers_responses}} response(s) today that need action.<p/><p>Please sign in into <a href="{{fe_url}}">ChannelProgram.com</a> and go to the Admin Dashboard -> IT Service Providers Responses page.<p/><p>Thank you!</p>',
            'footer_text' => '<p><a href="{{fe_url}}">Login now to get started!</a><p/><p>{{fe_url}}<p/><p>The Channel Program Team.</p>',
            'description' => 'Email will be sent based on an MSP requesting claimer access to their company account. Email is currently delivered to Channel Program to manage.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::AttendantWelcomeNotification) => [
            'subject' => "Success! You're registered for {{pitch_event_name}}",
            'header_text' => '<h1 style="text-align: center;font-size: 24px">Welcome!</h1><p>Congrats {{first_name}}!<p/>',
            'intro_text' => '<p class="text-up">You are registered for {{pitch_event_name}}.<br/>{{pitch_event_date}}<br/>Do not forget to add this event to your calendar with the calendar link below!</p>',
            'footer_text' => '<p>Enjoy your day,<br/><a href="{{fe_url}}">The Channel Program Team.</a><br/><br/>P.S. don’t forget <NAME_EMAIL>, so you don’t miss out on event updates, and upcoming events and offers.<br/>Having an issue? Shoot us an email at {{support_email}}</p>',
            'description' => 'Email will be sent for users who have registered for an Engage Event. NOTICE: This email is not in use at this time.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::ChatNotificationMessages) => [
            'subject' => '{{sender_first_name}} {{sender_last_name}} has sent you a {{message_type}} message for review.',
            'header_text' => 'Hi {{first_name}},',
            'intro_text' => '<p>I have added a {{message_type}} message for you in our partner portal. Please sign in to review at your earliest convenience.</p>',
            'footer_text' => '<p>While you’re in there, feel free to chat with me about this message or any other topics you need assistance with.</p>'
                . '<p>Thank you,</p>'
                . '<p>{{sender_first_name}} {{sender_last_name}}</p>'
                . '<p>{{sender_job_title}}</p>'
                . '<p>{{sender_company}}</p>'
                . '<p>{{sender_email}}</p>',
            'description' => 'Email is sent to a user (Vendor or MSP) who was offline at the time that a message (chat) was sent. Email is sent within a minute of the chat being initiated.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::ConfirmedEmail) => [
            'subject' => 'Welcome! YOU Are Ready to Command Your Channel',
            'header_text' => '<p class="text-up">Hi {{first_name}},</p>',
            'intro_text' => '<p class="text-up">We couldn’t be happier to welcome you to the Channel Program community. You’re a few clicks away from commanding your channel.</p><br/>'
                . '<p>With Channel Program, you can manage, market, and grow your channel program by: </p>'
                . '<ul>'
                . '<li>Providing your partners with a seamless partnership experience</li>'
                . '<li>Enable your partners with sales, marketing, and technical documentation they can white label, co-brand, and download</li>'
                . '<li>Connect and chat with your partners through a Channel Command (PRM) portal</li>'
                . '<li>Become searchable, findable, and reviewable by promoting profiles, products, and adding your online and in-person events to the largest events calendar in the channel industry</li>'
                . '</ul>'
                . '<p>Don’t forget to check out our <a href="https://help.channelprogram.com/getting-started-with-channel-program-as-a-vendor">Getting Started Guide</a></p>',
            'footer_text' => '<p>Thanks again for joining the Channel Program community. If you have any questions, suggestions, or feedback, we’d love to hear from you.</p><br/>'
                . '<p><a href="{{fe_url}}">The Channel Program Team.</a></p><br/>'
                . '<p class="text-down">This message was intended for {{first_name}} {{email}}. You’re receiving this email because you’re a registered user of Channel Program</p>',
            'description' => 'Email is a welcome to Channel Program email that is sent after a Vendor registration has been successfully completed.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::ConfirmedEmailMSP) => [
            'subject' => "Welcome To Channel Program! Here, You're in Command.",
            'header_text' => '<p class="text-up">Hi {{first_name}},</p>',
            'intro_text' => '<p class="text-up">Welcome to the Channel Program community, where you’re changing the channel with each log in. We couldn’t be happier that you’re here! Start getting value from your membership today with our favorite features purpose-built for MSPs, by MSPs.</p><br/>'
                . '<p class="text-up"><a href="{{fe_url}}/industrycalendar">Industry Event Calendar</a></p>'
                . '<p>See every IT channel industry event, down to the webinar level, in one place. Avoid event conflicts and stop searching your inbox and the web for the events that will move your business forward.</p><br/>'
                . '<p class="text-up"><a href="{{fe_url}}/channel-command">Channel Command</a></p>'
                . '<p>Get all your product updates, marketing materials and customer support with a single log in. Interact with your entire vendor universe with one password in one place.</p><br/>'
                . '<p class="text-up"><a href="{{fe_url}}/products">Product Reviews</a></p>'
                . '<p>Earn $15 today and make your voice heard by reviewing your vendors. Whether you’re a sole proprietor or a massive operation, every review from you and your team counts equally and immediately updates into our <a href="{{fe_url}}/categories">Stack Charts</a></p><br/>'
                . '<p class="text-up"><a href="{{fe_url}}/categories">Stack Charts</a></p>'
                . '<p>Research new vendors without flooding your inbox with sales pitches. Shop anonymously, and only hear from the vendors YOU want to talk to.</p><br/>'
                . '<p>Don’t forget to check out our <a href="https://help.channelprogram.com/getting-started-with-channel-program-as-an-it-service-provider">Getting Started Guide</a></p>',
            'footer_text' => '<p>Thanks again for joining the Channel Program community. If you have any questions, suggestions, or feedback, we’d love to hear from you.</p><br/>'
                . '<p><a href="{{fe_url}}">The Channel Program Team.</a></p><br/>'
                . '<p class="text-down">This message was intended for {{first_name}} {{email}}. You’re receiving this email because you’re a registered user of Channel Program</p><br/>'
                . '<p class="text-down">There’s so much more to discover. <a href="{{fe_url}}/login">Log in</a> now to be a part of the fastest-growing MSP community on the planet.</p>',
            'description' => 'Email is a welcome to Channel Program email that is sent after an MSP registration has been successfully completed.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::ConfirmEmail) => [
            'subject' => '{{first_name}}, Almost there! Please verify your email',
            'header_text' => '<h1 style="text-align: center;font-size: 24px">Confirm your email! {{first_name}}</h1>',
            'intro_text' => '<p class="text-up">{{first_name}},</p><p class="text-up">To complete your registration please click Confirm Your Email below.</p>',
            'footer_text' => '<p>Thanks,<br/><a href="{{fe_url}}">The Channel Program Team.</a><br/><br/>P.S. don’t forget <NAME_EMAIL>, so you don’t miss out on event updates, and upcoming events and offers.<br/>Having an issue? Shoot us an email at {{support_email}}</p>',
            'description' => 'Email is sent at the time of registration. After the user has provided their basic information the final step in the registration process is for them to verify their email. NOTICE: This email is not in use at this time.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::ResetPassword) => [
            'subject' => '{{first_name}}, Easy password reset',
            'header_text' => '<h1 style="text-align: center;font-size: 24px">Password recovery</h1>',
            'intro_text' => '<p class="text-up">No worries, {{first_name}}<br/>We can help you out. Click on the link below to reset your password.<br/>Easy peasy!</p>',
            'footer_text' => '<p>Enjoy your day!,<br/><a href="{{fe_url}}">The Channel Program Team.</a><br/><br/>P.S. don’t forget <NAME_EMAIL>, so you don’t miss out on event updates, and upcoming events and offers.<br/>Having an issue? Shoot us an email at {{support_email}}</p>',
            'description' => 'Email is sent when a user has clicked Send recovery link after clicking Forgot my password.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::ChangeEmail) => [
            'subject' => 'Address email change',
            'header_text' => '<h1 style="text-align: center;font-size: 24px">Email Reset!</h1>',
            'intro_text' => '<p class="text-up">Reset your email by clicking the link below.</p>',
            'footer_text' => '<p>Enjoy your day!,<br/><a href="{{fe_url}}">The Channel Program Team.</a><br/><br/>P.S. don’t forget <NAME_EMAIL>, so you don’t miss out on event updates, and upcoming events and offers.<br/>Having an issue? Shoot us an email at {{support_email}}</p>',
            'description' => 'Email will be sent to users who have requested that their email address be updated. NOTICE: This email is not in use at this time.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::EventApprovedNotification) => [
            'subject' => 'Channel Program - Event Submitted Confirmation',
            'header_text' => '<h1 style="text-align: center;font-size: 24px">Hi {{first_name}},</h1>',
            'intro_text' => '<p>Your event <span class="text-success">{{industry_event_name}}</span> scheduled for {{industry_event_start_date}} {{industry_event_timezone}} has been approved by Channel Program.</p>',
            'footer_text' => '<p>Looking forward to seeing you at your upcoming event!</p><br/><p class="text-down">This message was intended for {{first_name}} {{email}}.<br/>You have received this as a registered user of Channel Program.</p><br/><p>Enjoy your day,<br/><a href="{{fe_url}}">The Channel Program Team.</a></p>',
            'description' => 'Email is sent to the submitter, after a Channel Program Administrator has approved a submitted industry event.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::MinutesBeforePitchReminder) => [
            'subject' => 'Get ready for the event!',
            'header_text' => '<h1 style="text-align: center;font-size: 24px">Reminder</h1>',
            'intro_text' => '<p>Hey, {{first_name}}</p><p class="text-up">T-Minus 15 minutes until {{pitch_event_name}} gets started. Grab your coffee and get ready to COMMAND YOUR CHANNEL.</p>',
            'footer_text' => '<p>Enjoy your day!,<br/><a href="{{fe_url}}">The Channel Program Team.</a><br/><br/>P.S. don’t forget <NAME_EMAIL>, so you don’t miss out on event updates, and upcoming events and offers.<br/>Having an issue? Shoot us an email at {{support_email}}</p>',
            'description' => 'Email is sent to reminder users that an Engage event is about to begin. NOTICE: This email is not in use at this time.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::MissingVendorDistributorNotification) => [
            'display_name' => 'Missing Vendor identified by a Distributor notification',
            'subject' => 'Missing vendor as identified by a Distributor',
            'header_text' => '<h1 style="text-align: center;font-size: 24px">Missing vendor requested!</h1>',
            'intro_text' => '<p>{{user_name}} ({{user_email}}) from {{company_name}} is in the process of adding '
                . 'their distributor line card and has discovered that vendors aren’t available.</p>'
                . '<p>They would like to request the addition of these vendors to the Channel Program.</p>',
            'footer_text' => '<p>Please be sure you inform the user once the Vendors have been added.'
                . '<br/>Thank you,<br/><a href="{{fe_url}}">The Channel Program Team.</a></p>',
            'description' => 'Email will be sent from Distributors based on no matching vendor for the distributor to add to their line card. Email is currently delivered to Channel Program (Sales and Success) to manage.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::MissingProductOrVendorNotification) => [
            'subject' => 'New vendor/product requested',
            'header_text' => '<h1 style="text-align: center;font-size: 24px">New Vendor/Product requested!</h1>',
            'intro_text' => '<p class="text-up"><span class="text-success">{{user_name}} ({{user_email}})</span> at <span class="text-success">{{company_name}}</span> has requested to add the vendor <span class="text-success">{{vendor_name}}</span> and/or the vendors product of <span class="text-success">{{product_name}}</span> to Channel Program.</p>',
            'footer_text' => '<p>Enjoy your day,<br/><a href="{{fe_url}}">The Channel Program Team.</a></p>',
            'description' => 'Email will be sent from NaviStack based on an MSP requesting a specific vendor or vendor products to be added to Channel Program. Email is currently delivered to Channel Program (Sales and Success) to manage.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::MyStackFreeVendorClaimerNotification) => [
            'subject' => 'Good news, an MSP wants to connect with {{vendor_name}} on Channel Program',
            'header_text' => '',
            'intro_text' => '<p>With more than 8,000 Managed Service Provider members (and growing!), it’s no wonder that one of our MSP members  is requesting more information about {{vendor_name}}. You’re receiving this e-mail because MSPs are adding your solutions to their NaviStacks and want to see you on Channel Program.</p><p>Find out what your competition already knows about the power of partnering with Channel Program. Get in touch with our team to learn more about how we can help you connect with waiting MSPs now.</p><p>We hope to hear from you soon and start supercharging your business!</p>',
            'footer_text' => '<p class="text-down">This message was intended for {{first_name}} {{email}}.<br/>You’re receiving this email because you’re a registered user of Channel Program</p>',
            'description' => 'Email will be sent to vendors who are on a Free Vendor plan. Based on an MSP having added the vendors product via NaviStack and selecting “I am a current partner”.  Sales is CC’d on the email.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::MyStackFreeVendorClaimerNoPartnerNotification) => [
            'subject' => 'Good news, an MSP wants to connect with {{vendor_name}} on Channel Program',
            'header_text' => '',
            'intro_text' => '<p>With more than 8,000 Managed Service Provider members (and growing!), it’s no wonder that one of our MSP members  is requesting more information about {{vendor_name}}. You’re receiving this e-mail because an MSP added your solution to their NaviStack at <a href="{{fe_url}}">ChannelProgram.com</a>. This MSP could be an existing customer, or a new MSP interested in learning about your {{selected_product}} solution.</p>',
            'footer_text' => '<p class="text-down">This message was intended for {{first_name}} {{email}}.<br/>You’re receiving this email because you’re a registered user of Channel Program</p>',
            'description' => 'Email will be sent to vendors who are on a Free Vendor plan. Based on an MSP having added the vendors product via NaviStack and selecting “I’d like more information”.  Sales is CC’d on the email.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::MyStackVendorNoPrmClaimerNoPartnerNotification) => [
            'subject' => 'Good news, we have a potential prospect for you',
            'header_text' => '',
            'intro_text' => '<p>Good news, an MSP who is a member of the Channel Program community added {{vendor_name}} to their NaviStack and indicated they want more information about your {{selected_product}} solution. Follow up with {{user_first_name}} ({{company_name}}) {{user_email}} ASAP to make the most of this lead.</p>',
            'footer_text' => '<p class="text-down">This message was intended for {{first_name}} {{email}}.<br/>You’re receiving this email because you’re a registered user of Channel Program</p>',
            'description' => 'Email will be sent to vendors who do not currently have Channel Command enabled. Based on an MSP having added the vendors product via NaviStack and selecting “I’d like more information”.  Customer Success is CC’d on the email.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::MyStackVendorNoPrmClaimerPartnerNotification) => [
            'subject' => 'Good news, your client wants to connect with {{vendor_name}} on Channel Program',
            'header_text' => '',
            'intro_text' => '<p>Good news, an MSP who indicated they are a partner added {{vendor_name}} to their NaviStack. They’re indicating they would love to connect with you via your Channel Program portal. This allows you and your clients to seamlessly communicate in one platform, and helps your customers feel more connected. {{user_first_name}} ({{company_name}}) {{user_email}} has requested access to your portal. To learn more about Channel Command, the product that empowers these features, reach out to your Channel Success Manager <a href="https://meetings.hubspot.com/caitlyn-jopp">Caitlyn Jopp</a>. We’d love to get you started on having better conversations with your clients that lead to easier renewal discussions for your team.</p>',
            'footer_text' => '<p class="text-down">This message was intended for {{first_name}} {{email}}.<br/>You’re receiving this email because you’re a registered user of Channel Program</p>',
            'description' => 'Email will be sent to vendors who do not currently have Channel Command enabled. Based on an MSP having added the vendors product via NaviStack and selecting “I am a current partner”.  Customer Success is CC’d on the email.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::MyStackVendorPrmClaimerNoPartnerNotification) => [
            'subject' => 'New Connection Alert for {{vendor_name}}: An MSP is Waiting to Hear From You',
            'header_text' => '',
            'intro_text' => '<p>Great news! We’ve got another potential lead for you. {{user_first_name}} ({{company_name}}) {{user_email}} is looking for more information about your {{selected_product}} solution.</p><br/><br/>Log into Channel Program today to follow up with them and catch up on what you might’ve missed.<br/><br/>See you there!</p>',
            'footer_text' => '<p class="text-down">This message was intended for {{first_name}} {{email}}.<br/>You’re receiving this email because you’re a registered user of Channel Program</p>',
            'description' => 'Email will be sent to vendors who do has Channel Command enabled. Based on an MSP having added the vendors product via NaviStack and selecting “I’d like more information”.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::MyStackVendorPrmClaimerPartnerNotification) => [
            'subject' => 'New Connection Alert: An MSP is Waiting to Hear From {{vendor_name}}',
            'header_text' => '',
            'intro_text' => '<p>Great news! We’ve received a request from an MSP who indicates they are your partner in {{vendor_name}} and would like to join your Channel Command portal.<br/><br/>This means another one of your clients wants to get their updates, materials, and communications in a centralized place, empowering you to manage your relationship like never before.<br/><br/>{{user_first_name}} ({{company_name}}) {{user_email}} is awaiting your approval now. Keep your MSPs happy and <a href="{{fe_url}}/company-portal/{{vendor_friendly_url}}?my_channel=1">log in now</a> to validate their request.</p>',
            'footer_text' => '<p class="text-down">This message was intended for {{first_name}} {{email}}.<br/>You’re receiving this email because you’re a registered user of Channel Program</p>',
            'description' => 'Email will be sent to vendors who has Channel Command enabled. Based on an MSP having added the vendors product via NaviStack and selecting “I am a current partner”.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::PartnerInvite) => [
            'subject' => "You've been invited!",
            'header_text' => '<p>Congratulations! You’ve been invited to access the {{partner_name}} partner portal.</p>',
            'intro_text' => '<p>Please click the button below to log in and accept the invitation.</p><br/><p>If you’re a registered user of Channel Program, log in and accept the invitation. The email on your existing user must match the email that was invited.</p><br/><p>If you’re not already a registered user of Channel Program, from the login screen click Create your account here, to complete the registration. The email set during registration must match the email address that was invited.</p>',
            'footer_text' => '<p>Enjoy your day,<br/><a href="{{fe_url}}">The Channel Program Team.</a></p>',
            'description' => 'Email is sent to MSPs who have been invited by to by a Vendor to join their Channel Command Portal.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::PendingInviteRequestNotification) => [
            'subject' => 'Channel Program - Channel Command (PRM) invitations are waiting for your response',
            'header_text' => '<p class="text-up">Hi,</p>',
            'intro_text' => '<p>Please click the button below to log in and accept the invitation.</p><br/><p>If you’re a registered user of Channel Program, log in and accept the invitation. The email on your existing user must match the email that was invited.</p><br/><p>If you’re not already a registered user of Channel Program, from the login screen click Create your account here, to complete the registration. The email set during registration must match the email address that was invited.</p>',
            'footer_text' => '<p>Enjoy your day,<br/><a href="{{fe_url}}">The Channel Program Team.</a></p><br/><br/><p>This message was intended for {{mail}}.</p><p>You have received this as a registered user of Channel Program.</p>',
            'description' => 'Email is sent to MSPs on a schedule (once a week for 3 weeks) to remind them that a Vendor invitation to join their Channel Command portal is still pending.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::PitchEventVendorWelcome) => [
            'subject' => 'Success! Your registered for {{event_name}}',
            'header_text' => '<p class="text-up">Congrats {{first_name}} {{last_name}}</p>',
            'intro_text' => '<p class="text-up">You are registered for {{event_name}}.<br/>{{event_start_date}}<br/>Do not forget to add this event to your calendar with the calendar link below!</p>',
            'footer_text' => '<p><a href="https://channelprogram.com/%7B%7Bfe_url%7D%7D" rel="noopener noreferrer" target="_blank" style="color: rgb(28, 126, 214);">The Channel Program Team.</a></p><p><br></p><p>P.S. don’t forget <NAME_EMAIL>, so you don’t miss out on event updates, and upcoming events and offers.</p><p>Having an issue? Shoot us an email at {{support_email}}</p>',
            'description' => 'Email is sent to Vendors who are participating in Channel Engage. NOTICE: This email is not in use at this time.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::PartnerRequested) => [
            'subject' => 'Channel Program - Access Requests Received',
            'header_text' => '<p class="text-up">Hi {{first_name}} {{last_name}},</p>',
            'intro_text' => '<p>{{pending_request_count}} new access requests have been received and are awaiting your response.</p>',
            'footer_text' => '<p><a href="{{fe_url}}">The Channel Program Team.</a><p/>',
            'description' => 'Email is sent to Vendors daily to remind them that MSP access requests to their Channel Command portal are still pending.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::ReviewApproved) => [
            'subject' => 'Thank you for review, {{first_name}} {{last_name}}!',
            'header_text' => '<h1 class="ql-align-center">Thank you for submitting your review!</h1>',
            'intro_text' => '<p>Your valuable insights will help the IT community make informed buying decisions. Your review will also automatically enter your NaviStack, the free tool for MSPs to visualize and communicate with their entire vendor ecosystem in one spot. Log in now to add more vendors to your NaviStack: <a href="{{fe_url}}" rel="noopener noreferrer" target="_blank" style="color: rgb(28, 126, 214);">Channel Program </a></p><p><br></p><p>Reviews pertaining to, but not limited to, hardware and the following companies/products: Zoom, Slack, Microsoft, Intel, SalesForce, Hubspot, HP, and Dell will be incentivized at a reduced rate.</p><p><br></p><p><strong><em><u>IMPORTANT: </u></em><u>Incentivized reviews are issued by Tangocard / Reward Link</u></strong><u>.*</u> You should receive it within the next {{gift_card_delivery_period}} days.</p><p><br></p><p><a href="{{reviewed_model_url}}/" rel="noopener noreferrer" target="_blank" style="color: rgb(28, 126, 214);">Click here to see the {{reviewed_model_name}} review.</a></p><p><br>Know someone else who uses this product (friend, or colleague)? <a href="{{referrer_link}}" rel="noopener noreferrer" target="_blank" style="color: rgb(28, 126, 214);">Invite to review</a></p><p>If you encounter issues receiving your gift card, please email {{approval_email}}.</p><p>We will get you sorted out, promise. For incentive related claiming issues, please follow the instructions on the email sent by Tango Card / Reward-Link.</p><p><br></p><p class="ql-align-center">* Limited time offer. Maximum 10 gift cards per user. To claim your incentive, you must be an MSP and have left an unbiased and approved review on <a href="{{fe_url}}/products" rel="noopener noreferrer" target="_blank" style="color: rgb(28, 126, 214);">{{fe_url}}/products</a>. This offer is applicable for certain regions.</p><p class="ql-align-center">** Incentives will only be issued to individuals who reside in an approved region as indicated during the review creation process, regions may be added at any time without prior notice given. Channel Program will not retroactively issue incentives for newly added/approved regions.</p><p class="ql-align-center"><br></p><p class="ql-align-center">Channel Program has the right to modify or cancel this offer at any time and without notice.</p>',
            'footer_text' => '<p><a href="{{fe_url}}">The Channel Program Team.</a><br/><br/>P.S. don’t forget <NAME_EMAIL>, so you don’t miss out on event updates, and upcoming events and offers.<br/>Having an issue? Shoot us an email at {{support_email}}</p>',
            'description' => 'Email is sent to the submitter, after a Channel Program Administrator has approved a submitted review.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::ReviewApprovedForClaimer) => [
            'subject' => 'Channel Program: You have new product review for {{reviewed_model_name}}',
            'header_text' => '',
            'intro_text' => '<p>Discover your latest product reviews and seize the opportunity to engage with reviewers and share valuable content.<br/><br/>Log into Channel Program now to join the conversation and make the most of this feedback!</p>',
            'footer_text' => '<p class="text-down ql-align-center">This message was intended for {{first_name}} {{email}}.<br/><p class="ql-align-center">You’re receiving this email because you’re a registered user of Channel Program.</p>',
            'description' => 'Email is sent to vendor claimers after a product review has been approved by Channel Program Administrator. ',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::UpcomingEventNotification) => [
            'subject' => 'Can’t miss events happening in the Channel this week.',
            'header_text' => '<h1 style="text-align: center;font-size: 24px">Featured Event(s)</h1>',
            'intro_text' => '',
            'footer_text' => '<p class="text-down">Don’t see your event listed? <a href="{{fe_url}}/contact" style="display: inline-block;color:#FF6120">Contact us</a></p><br/><p>You’re receiving this email because you opted in via our applicable.</p>',
            'description' => 'Email is sent to all subscribers Monday AM to inform users of upcoming events for the week.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::VerificationCode) => [
            'subject' => 'Channel Program verification code',
            'header_text' => '<h1 style="text-align: center;font-size: 24px">Verification Code</h1>',
            'intro_text' => '<p class="text-up">Your Channel Program verification code is {{code}}</p><br/><p>Click on the product below for more details, or to get in contact with your Vendor’s Channel Manager.</p>',
            'footer_text' => '<p><a href="{{fe_url}}">The Channel Program Team.</a><br/><br/>P.S. don’t forget <NAME_EMAIL>, so you don’t miss out on event updates, and upcoming events and offers.<br/>Having an issue? Shoot us an email at {{support_email}}</p>',
            'description' => 'Email is sent at the time of registration. After the user has provided their basic information the final step in the registration process is for them enter in an OTP.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::VendorProductWeeklySummary) => [
            'subject' => 'Channel Program - New/Improved Products/Services.',
            'header_text' => '<h1 style="text-align: center;font-size: 24px">Hi {{first_name}} {{last_name}},</h1>',
            'intro_text' => '<p>Your Vendors have been busy adding new solutions for you to review.</p><br/><p>Click on the product below for more details, or to get in contact with your Vendor’s Channel Manager.</p>',
            'footer_text' => '<p><a href="{{fe_url}}">The Channel Program Team.</a><p/>',
            'description' => 'Email is sent to all MSPs weekly to inform users of new products that have been added by vendors. NOTICE: This email is not in use at this time.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::VendorWeeklySummary) => [
            'subject' => 'Channel Program - Channel PRM Activity',
            'header_text' => '<h1 style="text-align: center;font-size: 24px">Hi {{first_name}} {{last_name}},</h1>',
            'intro_text' => '<p>Your Vendors have been busy!</p><br/><p>Click on the Vendor below to access the new materials they have added to their portal.</p>',
            'footer_text' => '<p class="text-down">This message was intended for {{first_name}} {{email}}.<br/><p>You’re receiving this email because you’re a registered user of Channel Program.</p>',
            'description' => 'Email is sent to all MSPs weekly (Friday’s) to inform users of updates by their Vendors. MSPs will only receive the email if they are part of the vendors Channel Command Portal.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::WelcomeClaimer) => [
            'subject' => 'You are now an admin for {{company_name}} on Channel Program',
            'header_text' => '<h1 style="text-align: center;font-size: 24px">Congratulations! {{first_name}} {{last_name}} you are now an admin for {{company_name}} on Channel Program.</h1>',
            'intro_text' => '<p>When you log in you can see the “Manage Business Profile” link on your menu. To find this click your profile image.</p><br/><p>This means you are not able to do the following:</p><ul><li>Update your business profile</li><li>Add videos</li><li>Add products</li><li>and more…</li></ul>',
            'footer_text' => '<p><a href="{{fe_url}}/login">Login now to get started!</a><br/>{{fe_url}}/login</p><br/><p><a href="{{fe_url}}">The Channel Program Team.</a><br/><br/>P.S. don’t forget <NAME_EMAIL>, so you don’t miss out on event updates, and upcoming events and offers.<br/>Having an issue? Shoot us an email at {{support_email}}</p>',
            'description' => 'Email is sent to a Vendor user who has been made a claimer for their organization.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::WelcomeMSPClaimer) => [
            'subject' => 'You are now an admin for {{company_name}} on Channel Program',
            'header_text' => '<h1 style="text-align: center;font-size: 24px">Congratulations! {{first_name}} {{last_name}} you are now an admin for {{company_name}} on Channel Program.</h1>',
            'intro_text' => '<p>To find this, click your profile image. This means you are not able to do the following:</p><ul><li>Update your business profile</li><li>and more…</li></ul>',
            'footer_text' => '<p><a href="{{fe_url}}/login">Login now to get started!</a><br/>{{fe_url}}/login</p><br/><p><a href="{{fe_url}}">The Channel Program Team.</a><br/><br/>P.S. don’t forget <NAME_EMAIL>, so you don’t miss out on event updates, and upcoming events and offers.<br/>Having an issue? Shoot us an email at {{support_email}}</p>',
            'description' => 'Email is sent to a MSP user who has been made a claimer for their organization.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::NoActivityVendorNotification) => [
            'subject' => 'Channel Program - Channel PRM {{frequency_days}}-day check-in',
            'header_text' => '<p>Hi {{first_name}},</p>',
            'intro_text' => '<p>It’s been {{since_time}} since your Channel Partners have heard from you!</p><br/><br/>
            <p><span class="text-bold">Timeliness: </span> Keep your channel “in the know” by regularly updating your portal with content. It is essential to keep your enablement material, technical documentation, blog articles, and videos up to date.</p><br/>
            <p><span class="text-bold">Collaboration:</span> An updated portal can help facilitate collaboration and knowledge-sharing with your partners.</p><br/>
            <p><span class="text-bold">Competitive Advantage:</span> A well-maintained portal can provide a competitive advantage by enabling you to collaborate more effectively with your partners. This can lead to increased sales, better customer satisfaction, and improved brand reputation.</p><br/><br/>
            <p>Needs some help? Please reach out to your Success Manager today.</p>',
            'footer_text' => '<p>Don’t hesitate, <a href="{{fe_url}}/company-portal/{{vendor_friendly_url}}">Login now</a> to review and update {{vendor_name}}’s Channel Command (PRM) portal.<br/>Enjoy your day,<br/><a href="{{fe_url}}">The Channel Program Team.</a></p><br/><br/><p>This message was intended for {{email}}.</p><p>You have received this as a registered user of Channel Program.</p>',
            'description' => 'Email is sent to vendors who have not updated their Channel Command portal in 30 days.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::VendorPendingRequestNotification) => [
            'subject' => 'Channel Program – Channel Command (PRM) access requests are waiting for your response',
            'header_text' => '<p>Hi {{first_name}},</p>',
            'intro_text' => '<p>Just a quick reminder that you have Channel Command (PRM) portal access requests awaiting your response.<br/><br/>Connect with your MSP partners today by clicking the View Requests button below to see all pending requests.</p>',
            'footer_text' => '<p>This message was intended for {{email}}.</p><p>You have received this as a registered user of Channel Program.</p>',
            'description' => 'Email is sent to Vendors on a schedule (once a week for 3 weeks) to remind them that an MSP Channel Command portal access request is still pending.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::FreeVendorTrendingNotification) => [
            'subject' => 'Free Vendor(s) is Trending',
            'header_text' => 'Hey Sales Team!',
            'intro_text' => 'Check it out!',
            'footer_text' => '<p>You’re receiving this email because you’re a member of our Sales Team.</p>',
            'description' => 'Email will be sent to Channel Program Sales based on a Free Vendor appearing in the Top Trending Companies list.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::RegisteredEmailMSP) => [
            'subject' => 'Welcome To Channel Program!',
            'header_text' => '<p>Hi {{first_name}},</p>',
            'intro_text' => '<p>Welcome aboard! We’re thrilled to have you join the Channel Program community, where you’re changing the channel with each log in.</p><p>Please follow the link below to set your password.</p>',
            'footer_text' => '<p>Thanks again for joining the Channel Program community. If you have any questions, suggestions, or feedback, we’d love to hear from you.</p><br/>'
                . '<p><a href="{{fe_url}}">The Channel Program Team.</a></p><br/>'
                . '<p>This message was intended for {{first_name}} {{email}}. You’re receiving this email because you’re a registered user of Channel Program</p>',
            'description' => 'Email is sent to users who have registered using the event registration or have been uploaded via the Admin Dashboard.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::RegisteredEmailDirect) => [
            'subject' => 'Welcome to BetterTracker – Let’s Take Control of Your Spend',
            'header_text' => '<p>Hi {{first_name}},</p>',
            'intro_text' => '<p>Welcome to <strong>BetterTracker</strong>! 🎉 Each time you log in, you’re taking a smarter approach to managing your business expenses, contracts, and SaaS subscriptions—and we’re thrilled to be part of that journey.</p><br/>'
                . '<p>With BetterTracker, you can:</p>'
                . '<ul>'
                . '<li><strong>Centralize</strong> all your vendor contracts, subscription details, and renewal dates in one place</li>'
                . '<li><strong>Never miss a renewal</strong> again with customizable alerts and smart notifications</li>'
                . '<li><strong>Visualize your spend</strong> across categories, departments, and vendors</li>'
                . '<li><strong>Identify and eliminate waste</strong> from underused or redundant services</li>'
                . '<li><strong>Export reports</strong> for budgeting, compliance, or audits in seconds</li>'
                . '</ul>'
                . '<br/><p>Need help getting started? Reach out to {{support_email}}</p>'
                . '<br/><p>We’re here to make your business easier to run—and your financial management easier to master.</p>'
                . '<br/><p>Let’s get tracking,<br/>— <a href="{{fe_url}}">The BetterTracker Team.</a></p>',
            'footer_text' => '<p>This message was intended for {{first_name}} {{email}}. You’re receiving this email because you’re a registered user of BetterTracker</p>',
            'description' => 'Welcome email sent to new Direct users after registration on BetterTracker.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::MyStackBuildingReminder) => [
            'subject' => '{{company_name}} your NaviStack is coming along - just a few more core products to go.',
            'header_text' => '<p>Hi {{first_name}},</p>',
            'intro_text' => '<p>Your business, {{company_name}}, is making significant strides in the development of NaviStack. However, upon examining the {{navi_stack_categories_count}} core categories, it appears that you are still missing a few essential products.</p>',
            'footer_text' => '<p>Take action now and <a href="{{fe_url}}/login">login</a> to assess your stack or explore products that could be ideal for enhancing your customer support.</p>'
                . '<br/><br/><p>This message was intended for {{first_name}} {{email}}.</p>'
                . '<p>You’re receiving this email because you’re registered with Channel Program.</p><br/>',
            'description' => 'Email is sent to MSPs to remind them to build their stack.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::InviteToReviewEmail) => [
            'display_name' => 'Invite to Review Email',
            'subject' => 'Head over to Channel Program and submit a product review',
            'header_text' => '',
            'intro_text' => '<p>{{submitter_first_name}} {{submitter_last_name}} has invited you to check out the latest reviews of {{product_name}}> on Channel Program. They value your opinion and would like you to share your own review.</p>'
                . '<p><a href="{{fe_url}}/login?referral_id={{referral_id}}&redirect_url=/product/{{product_friendly_url}}">Login</a> to Channel Program now to submit your review. Your valuable insights will greatly assist the IT Community in making informed purchasing decisions.</p>',
            'footer_text' => '',
            'description' => 'Email is sent to contacts who have been recommended by a registered Channel Program user to write a review.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::RecommendNaviStackEmail) => [
            'display_name' => 'Recommend NaviStack Email',
            'subject' => 'Have you heard of NaviStack - highly recommend!',
            'header_text' => '',
            'intro_text' => '{{submitter_first_name}} {{submitter_last_name}} wanted to make sure you’re aware of NaviStack by Channel Program.</p>'
                . '<p>NaviStack enables you to:'
                . '<ul><li>Visualize your vendor stack in one convenient location.</li>'
                . '<li>Streamline communications and save valuable time.</li>'
                . '<li>Identify gaps and enhance your services.</li>'
                . '<li>Unlock new opportunities with your vendors.</li><ul/></p>'
                . '<p>NaviStack is free for MSPs. <a href="{{fe_url}}/login?referral_id={{referral_id}}">Login</a> to Channel Program now to begin building your stack.<p>',
            'footer_text' => '',
            'description' => 'Email is sent to contacts who have been recommended by a registered Channel Program MSP to learn more about NaviStack.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::FirstReviewApproved) => [
            'display_name' => 'First time review',
            'subject' => 'Thank you for review, {{first_name}}!',
            'header_text' => '',
            'intro_text' => '<p>Thank you for submitting your user review!</p>'
                . '<p>Your valuable insights will help the IT Community make informed buying designs.</p><br/>'
                . '<p>Your approved review may have resulted in the product being added to your NaviStack. <a href="{{fe_url}}">Click here</a> to learn more about NaviStack.<p>',
            'footer_text' => '',
            'description' => 'Sent to users who have submitted their first product review.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::VendorContractRenewalReminder) => [
            'display_name' => 'Vendor contract renewal reminder',
            'subject' => 'You have some products/services that are coming up for renewal in the next {{period}} period',
            'header_text' => 'Hi {{company_name}},',
            'intro_text' => '<p>We’d like to gently bring to your attention that the renewal period for the following products/services is approaching. If you are considering any alterations to your existing plan, don´t hesitate to get in touch with your vendor(s) today.</p>',
            'footer_text' => '<p>This message was intended for {{email}}.</p>'
                . '<p>You are receiving this because your company ({{company_name}}) has configured notifications through the <a href="{{fe_url}}">Channel Program</a>.</p>',
            'description' => 'Email is sent to MSPs who have enabled the reminder.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::VendorContractRenewalReminderCustomer) => [
            'display_name' => 'Vendor contract renewal reminder for customer',
            'subject' => 'You have some products/services that are coming up for renewal in the next {{period}} period',
            'header_text' => 'Hi {{company_name}},',
            'intro_text' => '<p>We’d like to gently bring to your attention that the renewal period for the following products/services is approaching. If you are considering any alterations to your existing plan, don´t hesitate to get in touch with your vendor(s) today.</p>',
            'footer_text' => '<p>This message was intended for {{email}}.</p>'
                . '<p>You are receiving this because your company ({{company_name}}) has configured notifications through the <a href="https://www.bettertracker.com">BetterTracker</a>.</p>',
            'description' => 'Email is sent to customers who have enabled the reminder.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::VendorNoticeDeadlineApproaching) => [
            'display_name' => 'Vendor notice deadline approaching',
            'subject' => 'You have some products/services where the notice period is the next {{period}} period',
            'header_text' => 'Hi {{company_name}},',
            'intro_text' => '<p>We wanted to gently remind you that the notice period is nearing its end. If you are considering canceling or modifying your current contract, make sure to contact your vendor(s) today.</p>',
            'footer_text' => '<p>This message was intended for {{email}}.</p>'
                . '<p>You received it because your company ({{company_name}}) has configured notifications through the <a href="{{fe_url}}">Channel Program</a>.</p>',
            'description' => 'Email is sent to MSPs who have enabled the reminder.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::VendorNoticeDeadlineApproachingCustomer) => [
            'display_name' => 'Vendor notice deadline approaching for customer',
            'subject' => 'You have some products/services where the notice period is the next {{period}} period',
            'header_text' => 'Hi {{company_name}},',
            'intro_text' => '<p>We wanted to gently remind you that the notice period is nearing its end. If you are considering canceling or modifying your current contract, make sure to contact your vendor(s) today.</p>',
            'footer_text' => '<p>This message was intended for {{email}}.</p>'
                . '<p>You received it because your company ({{company_name}}) has configured notifications through the <a href="https://www.bettertracker.com">BetterTracker</a>.</p>',
            'description' => 'Email is sent to customers who have enabled the reminder.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::VendorWeeklySummaryForVendor) => [
            'display_name' => 'New Materials Waiting',
            'subject' => 'Alert: You Have New Materials Waiting from Channel Program!',
            'header_text' => '<h1 style="text-align: center;font-size: 24px">Hi {{first_name}} {{last_name}},</h1>',
            'intro_text' => '<p>We wanted to inform you that there have been recent updates to the materials available in our portal.</p><br/>'
                . '<p>To access the latest versions, click link below to visit our portal where you will find the updated materials.</p><br/>',
            'footer_text' => '<p>We encourage you to review these resources at your earliest convenience.</p><br/>'
                . '<p>Thank you for your attention, and we look forward to your continued engagement with our updated materials.</p><br/>'
                . '<p>The Channel Program Team</p><br/>'
                . '<p class="text-down">This message was intended for {{first_name}} {{email}}.<br/><p>You’re receiving this email because you’re a registered user of Channel Program.</p>',
            'description' => 'Email is sent to all Vendors (Friday’s) to inform users of updates by Channel Program. Only vendors connected to Channel Programs Channel Command Portal will receive the email.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::MSPClientInvite) => [
            'display_name' => 'New User Invitation',
            'subject' => 'Invitation to Join {{client_company_name}}',
            'header_text' => '',
            'intro_text' => '<p>Hi,</p><p>{{requestor_first_name}} {{requestor_last_name}} has extended an invitation for you to join {{client_company_name}} as the administrator.</p>'
                . '<p>Don’t hesitate — sign in now or register for your new account!</p>',
            'footer_text' => '<p>This message was intended for {{email}}. You are receiving this message because someone from {{company_name}} has invited you to join today.</p>',
            'description' => 'Email sent to users to create an account and register with Channel Program.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::ResetPasswordClient) => [
            'subject' => '{{first_name}}, Easy password reset',
            'header_text' => '<h1 style="text-align: center;font-size: 24px">Password recovery</h1>',
            'intro_text' => '<p class="text-up">No worries, {{first_name}}<br/>We can help you out. Click on the link below to reset your password.<br/>Easy peasy!</p>',
            'footer_text' => '<p>Enjoy your day!</p>',
            'description' => 'Email is sent when a user has clicked Send recovery link after clicking Forgot my password.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::AffiliateLocationInvitation) => [
            'display_name' => 'New Affiliate Location Invitation',
            'subject' => 'Invitation to Join {{corporate_name}}',
            'header_text' => '<h1 style="text-align: left;font-size: 24px">Hi,</h1>',
            'intro_text' => '<p>{{requestor_first_name}} {{requestor_last_name}} has extended an invitation for you to join {{corporate_name}} in Channel Program.</p>',
            'footer_text' => '<p>This message was intended for {{email}}. You are receiving this message because someone from {{corporate_name}} has invited you to join today.</p>',
            'description' => 'Email sent to affiliate locations to create an account and register with Channel Program.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::MSPUserInvite) => [
            'display_name' => 'New MSP Company User Invitation',
            'subject' => 'Join Us at {{corporate_name}}: Invitation to Our Channel Program',
            'header_text' => '<h1 style="text-align: left;font-size: 24px">Hi,</h1>',
            'intro_text' => '<p>You have been personally invited by {{requestor_first_name}} {{requestor_last_name}} to join {{corporate_name}}.</p><p>Channel Program allows MSPs to:
                             <ul><li>Interact with vendors</li>
                             <li>Write product reviews</li>
                             <li>Manage their tech stack</li>
                             <li>Manage vendor contracts</li>
                             <li>And more!</li></ul></p><p>Click the link below to accept the invitation and get started today:</p>',
            'footer_text' => '<p>This message was intended for {{email}}. You are receiving this message because someone from {{corporate_name}} has invited you to join today.</p>',
            'description' => 'Email sent to users for MSP company types to create an account and register with Channel Program.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::MSPCustomerUserInvite) => [
            'display_name' => 'New MSP Customer User Invitation',
            'subject' => 'Join Us at {{corporate_name}}: Invitation to Our Company',
            'header_text' => '<h1 style="text-align: left;font-size: 24px">Hi,</h1>',
            'intro_text' => '<p>You have been personally invited by {{requestor_first_name}} {{requestor_last_name}} to join {{corporate_name}}.</p><p>You are allowed to:
                             <li>Manage your tech stack</li>
                             <li>Manage your products</li>
                             <li>Manage your contracts</li>
                             <li>And more!</li></ul></p><p>Click the link below to accept the invitation and get started today:</p>',
            'footer_text' => '<p>This message was intended for {{email}}. You are receiving this message because someone from {{corporate_name}} has invited you to join today.</p>',
            'description' => 'Email sent to users for MSP Customer company types to create an account and register under their MSP.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::VendorUserInvite) => [
            'display_name' => 'New Vendor Company User Invitation',
            'subject' => 'Join Us at {{corporate_name}}: Invitation to Our Channel Program',
            'header_text' => '<h1 style="text-align: left;font-size: 24px">Hi,</h1>',
            'intro_text' => '<p>You have been personally invited by {{requestor_first_name}} {{requestor_last_name}} to join {{corporate_name}}.</p><p>Channel Program enable vendors to:
                             <ul><li>Interact with MSP partners</li>
                             <li>Showcase their products</li>
                             <li>And more!</li></ul></p><p>Click the link below to accept the invitation and get started today:</p>',
            'footer_text' => '<p>This message was intended for {{email}}. You are receiving this message because someone from {{corporate_name}} has invited you to join today.</p>',
            'description' => 'Email sent to users for Vendor company types to create an account and register with Channel Program.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::NewMultiStackInvitation) => [
            'display_name' => 'New MultiStack Invitation',
            'subject' => 'Join Us at {{company_name}}: Invitation to Our Channel Program',
            'header_text' => '<h1 style="text-align: center;font-size: 24px">Hi,</h1>',
            'intro_text' => '<p>You have been personally invited by {{requestor_first_name}} {{requestor_last_name}} to join {{company_name}}</p><br/>'
                . '<p>Channel Program allows MSPs to:</p><br/>'
                . '<p>  .Interact with vendors</p><br/>'
                . '<p>  .Write product reviews</p><br/>'
                . '<p>  .Manage their tech stack</p><br/>'
                . '<p>  .Manage vendor contracts</p><br/>'
                . '<p>  .Manage locations and customers</p><br/>'
                . '<p>  .And more!</p><br/>'
                . '<p>Click the link below to accept the invitation and get started today: <br/><a href="{{acceptInvitation}}">Accept Invitation</a></p><br/>'
                . '<p>Alternatively, you can copy and paste the link below into your browser: <br/><a href="{{acceptInvitation}}">{{acceptInvitation}}</a></p><br/>',
            'footer_text' => '<p>This message was intended for {{email}}. You are receiving this message because someone from {{company_name}} has invited you to join.</p>',
            'description' => 'Email sent to locations to create an account and register with Channel Program.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::FreeVendorTopTrendingProduct) => [
            'display_name' => 'Free Vendor Top Trending Product',
            'subject' => 'Free Vendor(s) product(s) is Trending',
            'header_text' => '<h1 style="text-align: center;font-size: 24px">Hi,</h1>',
            'intro_text' => '<p>Check it out, products for our free vendors are trending.</p><br/>'
                . '<p>{{vendorCompanyName}}, {{productName}} is trending and is now {{ranking}} on the list of Top Trending Products!</p>',
            'footer_text' => '<p>You’re receiving this email because you’re a member of our Sales Team.</p>',
            'description' => 'Email will be sent to Channel Program Sales based on a Free Vendor appearing in the Top Trending Products list.',
        ],
        // Emails for deals
        EmailBladeFiles::getKey(EmailBladeFiles::DealSubmittedToVendor) => [
            'display_name' => 'Deal Registration Submitted',
            'subject' => 'Action Required: New Deal Registration Received',
            'header_text' => 'Hi {{company_name}},',
            'intro_text' => '<p>A new deal, {{deal_name}}, has been submitted by {{msp_user_first_name}} {{msp_user_last_name}} at {{owner_name}} for your consideration through <a href="{{fe_url}}">Channel Program</a>.</p>'
                . '<p>Please <a href="{{fe_access_url}}">{{fe_access_text}}</a> now to review the details of the deal and take appropriate action.</p>',
            'footer_text' => '<p>This message was intended for {{email}}. You are receiving this message because your company ({{company_name}}) has configured deal registration notifications through Channel Program.</p>',
            'description' => 'Email is sent to Vendors when a new deal registration is submitted.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::DealSubmittedToFreeVendor) => [
            'display_name' => 'Deal Registration Submitted - Free Vendor',
            'subject' => 'New Lead for {{company_name}} on ChannelProgram.com: Account Limited',
            'header_text' => 'You are receiving this e-mail because an MSP Lead is requesting to discuss a possible deal with you via <a href="{{fe_url}}">Channel Program</a>.',
            'intro_text' => '<p>You are receiving this e-mail because an MSP utilizing <a href="{{fe_url}}">Channel Program</a> would like to discuss a possible deal with you.</p>'
                . '<p>A New Lead has submitted a potential deal for {{selected_product}} but your <strong>Account is Limited<strong>.</p>'
                . '<p>Please contact <a href="mailto:{{sales_email}}">{{sales_email}}</a> for more information.</p>'
                . '<p>With more than 8,000 Managed Service Provider members (and growing!), it’s no wonder that one of our MSP members is requesting more information about {{company_name}}.</p>',
            'footer_text' => '<p>This message was intended for {{vendor_user_first_name}} {{email}}.</p>'
                . '<p>You are receiving this message because your company ({{company_name}}) has configured deal registration notifications through Channel Program.</p>',
            'description' => 'Email will be sent to vendors who are on a Free Vendor plan. Based on an MSP submitting a deal registration request. Sales is CC’d on the email.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::DealSubmittedToVendorNoChannelCommand) => [
            'display_name' => 'Deal Registration Submitted - No PRM',
            'subject' => 'New Lead for {{company_name}} on ChannelProgram.com: Portal Not Active',
            'header_text' => 'Your Partner has submitted a request via <a href="{{fe_url}}">Channel Program</a> to discuss a possible deal your company.',
            'intro_text' => '<p><i>You know, life would be easier if you were using Channel Command to manage your leads and your partners!</i></p>'
                . '<p>A new deal has been submitted for your consideration through <a href="{{fe_url}}">Channel Program</a>. However, you have not activated your Channel Command Portal which will lead to major disappointment.</p>'
                . '<p>It’s time to make your channel program more efficient and effective! Connect with your Channel Success Manager ASAP to get started.</p>',
            'footer_text' => '<p>This message was intended for {{vendor_user_first_name}} {{email}}.</p>'
                . '<p>You’re receiving this email because you’re a registered user of <a href="{{fe_url}}">Channel Program</a></p>',
            'description' => 'Email will be sent to vendors who do not currently have Channel Command enabled. Based on an MSP submitting a deal registration request. Customer Success is CC’d on the email.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::DealApprovedByVendor) => [
            'display_name' => 'Deal Registration - Deal Approved',
            'subject' => 'Deal Update: Approval Status from {{company_name}}',
            'header_text' => 'Hi {{msp_user_first_name}} ({{owner_name}}),',
            'intro_text' => '<p>We\'re excited to inform you that {{vendor_user_first_name}} {{vendor_user_last_name}} at {{company_name}} has approved your deal request for {{deal_name}}.</p>'
                . '<p>Please <a href="{{fe_access_url}}">{{fe_access_text}}</a> now to review the details.</p>',
            'footer_text' => '<p>This message was intended for {{msp_user_first_name}} {{email}}. You’re receiving this email because you’re a registered user of Channel Program.</p>',
            'description' => 'Email is sent to submitter when a submitted deal has been approved.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::DealRejectedByVendor) => [
            'display_name' => 'Deal Registration - Deal Declined',
            'subject' => 'Deal Update: Declined Status from {{company_name}}',
            'header_text' => 'Hi {{msp_user_first_name}} ({{owner_name}}),',
            'intro_text' => '<p>We regret to inform you that {{vendor_user_first_name}} {{vendor_user_last_name}} at {{company_name}} has declined your deal request for {{deal_name}}.</p>'
                . '<p>Please <a href="{{fe_access_url}}">{{fe_access_text}}</a> now to review the reason and follow-up with the vendor.</p>',
            'footer_text' => '<p>This message was intended for {{msp_user_first_name}} {{email}}. You’re receiving this email because you’re a registered user of Channel Program.</p>',
            'description' => 'Email is sent to submitter when a submitted deal has been rejected',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::DealAdittionalInfoRequested) => [
            'display_name' => 'Deal Registration Info Requested',
            'subject' => 'Action Required: Additional Information Requested for Your Recent Deal Submission with {{company_name}}',
            'header_text' => 'Hi {{msp_user_first_name}} ({{owner_name}}),',
            'intro_text' => '<p>{{company_name}} has requested more information or clarification regarding the deal you submitted for deal {{deal_name}}. {{vendor_user_first_name}} is seeking additional details to move forward with the evaluation process.</p>'
                . '<p>Please <a href="{{fe_access_url}}">{{fe_access_text}}</a> now to review the request and provide the necessary information to expedite the deal process.</p>',
            'footer_text' => '<p>This message was intended for {{msp_user_first_name}} {{email}}. You’re receiving this email because you’re a registered user of Channel Program.</p>',
            'description' => 'Email is sent to Submitter of a deal if the Vendor requires more information',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::DealAdittionalInfoProvided) => [
            'display_name' => 'Deal Registration More Info Response',
            'subject' => 'Action Required: Follow-Up on Your Request for More Information from {{owner_name}}',
            'header_text' => 'Hi {{vendor_user_first_name}} ({{company_name}}),',
            'intro_text' => '<p>{{owner_name}} has responded to your request for more information regarding the recently submitted deal, {{deal_name}}. {{msp_user_first_name}} {{msp_user_last_name}} has provided additional details to assist you.</p>'
                . '<p>Please <a href="{{fe_access_url}}">{{fe_access_text}}</a> now to review the updated information and take appropriate action.</p>',
            'footer_text' => '<p>This message was intended for {{vendor_user_first_name}} {{email}}. You’re receiving this email because you’re a registered user of Channel Program.</p>',
            'description' => 'Email is sent to Vendor when a submitter has provided more information about a deal as requested.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::DealValidityDateApproachingMSP) => [
            'display_name' => 'MSP Deal Registration Validity Date Approaching:',
            'subject' => 'Action Required: Deal Registration Validity Date is quickly approaching',
            'header_text' => 'Hi {{msp_user_first_name}} ({{owner_name}}),',
            'intro_text' => '<p>We noticed that the deal {{deal_name}} you submitted to {{company_name}} on {{submission_date}} remains open, and its validity date ({{expiration_date}}) is approaching soon.</p>'
                . '<p>Please <a href="{{fe_access_url}}">{{fe_access_text}}</a> now to review the deal details and take any necessary action before the deadline.</p>',
            'footer_text' => '<p>This message was intended for {{msp_user_first_name}} {{email}}. You’re receiving this email because you’re a registered user of Channel Program.</p>',
            'description' => 'Email is sent to MSP submitter when a deal is still open the validity date is approaching',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::DealValidityDateApproachingVendor) => [
            'display_name' => 'Vendor Deal Registration Validity Date Approaching:',
            'subject' => 'Action Required: Deal Registration Validity Date is quickly approaching',
            'header_text' => 'Hi {{vendor_user_first_name}} ({{company_name}}),',
            'intro_text' => '<p>The deal {{deal_name}} submitted by {{owner_name}} on {{submission_date}} remains open, and its validity date ({{expiration_date}}) is quickly approaching.</p>'
                . '<p>Please <a href="{{fe_access_url}}">{{fe_access_text}}</a> now to review the details of the deal and take appropriate action.</p>',
            'footer_text' => '<p>This message was intended for {{vendor_user_first_name}} {{email}}. You’re receiving this email because you’re a registered user of Channel Program.</p>',
            'description' => 'Email is sent to Vendors when a deal is still open the validity date is approaching',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::ConfirmedEmailDirect) => [
            'display_name' => 'Confirmed Email Direct',
            'subject' => 'Welcome To BetterTracker, {{first_name}}',
            'header_text' => 'Hi {{first_name}},',
            'intro_text' => '<p class="text-up"><b>Welcome to BetterTracker!</b> Each time you log in, you’re transforming the way you manage your business. We’re thrilled to have you with us!</p><br/>'
                . '<p>With BetterTracker, you can: </p>'
                . '<ul>'
                . '<li>Connect and collaborate with vendors</li>'
                . '<li>Share and explore product reviews</li>'
                . '<li>Organize and optimize your tech stack</li>'
                . '<li>Oversee vendor contracts with ease</li>'
                . '<li>Manage multiple locations efficiently</li>'
                . '</ul>'
                . '<p>And that’s just the beginning!</p>'
                . '<p>Be sure to explore our <a href="https://help.bettertracker.com/getting-started-with-bettertracker-as-direct">Getting Started Guide</a> to make the most of your experience.</p>',
            'footer_text' => '<p>Thanks again for joining! If you have any questions, suggestions, or feedback, we’d love to hear from you.</p><br/>'
                . '<p><a href="{{fe_url}}">The BetterTracker Team.</a></p><br/>'
                . '<p class="text-down">This message was intended for {{first_name}} {{email}}. You’re receiving this email because you’re a registered user of BetterTracker</p>',
            'description' => 'Email is a welcome to BetterTracker email that is sent after a Direct registration has been successfully completed.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::ApprovedDealChanged) => [
            'display_name' => 'Approved Deal Registration Updated',
            'subject' => 'Action Required: Updates Made to Your Approved Deal ({{deal_name}})',
            'header_text' => 'Hi {{company_name}},',
            'intro_text' => '<p>We wanted to inform you that the approved deal, {{deal_name}}, has been recently updated by {{msp_user_first_name}} {{msp_user_last_name}} at {{owner_name}}.</p>'
                . '<p>Please <a href="{{fe_access_url}}">{{fe_access_text}}</a> at your earliest convenience to review the latest details and take any necessary actions.</p>',
            'footer_text' => '<p>Thank you,</p>'
                . '<p>The Channel Program Team</p>'
                . '<p>This message was intended for {{email}}. You are receiving this message because your company ({{company_name}}) has configured deal registration notifications through Channel Program.</p>',
            'description' => 'Email is sent to Vendors when an MSP has edited a deal that they’ve already approved',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::DeclinedDealChanged) => [
            'display_name' => 'Rejected Deal Registration Updated',
            'subject' => 'Action Required: Updates Made to Your Rejected Deal ({{deal_name}})',
            'header_text' => 'Hi {{company_name}},',
            'intro_text' => '<p>We wanted to let you know that your previously rejected deal, {{deal_name}}, has been updated by {{msp_user_first_name}} {{msp_user_last_name}} at {{owner_name}}.</p>'
                . '<p>Please <a href="{{fe_access_url}}">{{fe_access_text}}</a> at your earliest convenience to review the latest details and take any necessary actions.</p>',
            'footer_text' => '<p>Thank you,</p>'
                . '<p>The Channel Program Team</p>'
                . '<p>This message was intended for {{email}}. You are receiving this message because your company ({{company_name}}) has configured deal registration notifications through Channel Program.</p>',
            'description' => 'Email is sent to Vendors when an MSP has edited a deal that they’ve rejected.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::CHANNEL_DEAL_APPROVED) => [
            'display_name' => 'Deal Submitted Confirmation',
            'subject' => 'Channel Program - Deal Submitted Confirmation',
            'header_text' => 'Hi {{first_name}},',
            'intro_text' => '<p>Your deal <b>{{deal_name}}</b> has been approved and can be viewed at <b>Channel Deals</b> via this link: <a href="{{deals_url}}">{{deals_url}}</a>.</p> <br/> <p>The email address associated with this deal will receive notifications as soon as a community member requests the deal.</p>',
            'footer_text' => '<p>This message was intended for {{first_name}} {{email}}. You’re receiving this email because you’re a registered user of Channel Program.</p>',
            'description' => 'Email is sent to the submitter, after a Channel Program Administrator has approved a submitted channel deal.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::CHANNEL_DEAL_SEND_LEADS) => [
            'display_name' => 'New Deal',
            'subject' => 'New Deal Request for {{deal_name}} from Channel Program',
            'header_text' => '<p>Hi,</p> <br> <p>Thank you for trusting Channel Program and for being an integral part of our community. We’re excited to inform you that we have a new potential lead for you!</p>',
            'intro_text' => '<p>On <b>{{date_requested}}</b>, <b>{{msp_first_name}} {{msp_last_name}}</b> ({{msp_email}}) from <b>{{msp_company_name}} </b> expressed interest in your deal: <b>{{deal_name}}</b> (Expiry: <b>{{deal_expiry}}</b> ).</p> <br> <p><a href="{{fe_url}}/login">Please log in</a> to your Channel Program account to connect with them and follow up on this exciting opportunity.</p>',
            'footer_text' => '<p>Thank you for your continued partnership with Channel Program.</p> <br> <p>Best regards,</p> <p>The Channel Program Team</p><br><p> <i>This message was intended for {{email}}. You are receiving this because your company ({{company_name}}) has configured notifications through Channel Program. </i></p>',
            'description' => 'Email is sent to the configured deal email, after an MSP requests the deal. Success is CC’d.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::CHANNEL_DEAL_USER_INVITATION) => [
            'display_name' => 'Invitation to Complete Your Deal',
            'subject' => 'Invitation to Complete Your Request for {{deal_name}}',
            'header_text' => 'Hi,',
            'intro_text' => '<p>{{requestor_first_name}} {{requestor_last_name}} has extended an invitation for you to join {{parent_company}} in Channel Program.<p/>',
            'footer_text' => '<p>This invitation was sent to <b>{{email_address}}</b> because a request was submitted to learn more about a vendor deal.</p> <br> <p>If you believe you received this message in error, please <a href="https://channelprogram.com/contact"> contact us</a>, and we’ll resolve the issue promptly.</p> <br> <p>Best regards,</p><p>The Channel Program Team</p>',
            'description' => 'Email is sent to the submitter to sign in or register.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::MSPLocationInvite) => [
            'display_name' => 'New Location Invitation',
            'subject' => 'Invitation to Join {{location_company}}',
            'header_text' => 'Hi,',
            'intro_text' => '<p>{{requester_first_name}} {{requester_last_name}} has extended an invitation for you to join {{location_company}} in Channel Program.</p>',
            'footer_text' => '<p>This message was intended for {{email}}. You are receiving this message because someone from {{parent_company}} has invited you to join today.</p>',
            'description' => 'Email sent to non-Affiliate locations to create an account and register with Channel Program.',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::COMPANY_EXPENSES_UPCOMING_RECURRING_CHARGES) => [
            'display_name' => 'Upcoming Recurring Charges',
            'subject' => '🚀 Heads Up! Your Upcoming Expenses Are on the Horizon 🌟',
            'header_text' => '<h1 style="text-align: center;">Hey {{first_name}}, don´t forget...</h1>',
            'intro_text' => '<p style="text-align: center;">You have <b>{{expense_count}}</b> upcoming Recurring Expenses in the next <b>{{defined_period}}</b>.</p>',
            'footer_text' => '<p style="text-align: center;">This message was sent to {{email}}. Please login to the BetterTracker platform to update you your email preferences. For more details, see our <a href="https://channelprogram.com/legal/privacy-policy"> privacy policy</a></p>',
            'description' => 'Sent every Monday at 6AM EST',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::COMPANY_EXPENSES_INCREASE_OVER_TIMEFRAME) => [
            'display_name' => 'Increase over X timeframe',
            'subject' => 'Your Spending’s on the Rise! Let’s Dive In! 📈',
            'header_text' => '<h1 style="text-align: center;">Hey {{first_name}}, heads up!</h1>',
            'intro_text' => '<p style="text-align: center;">Your recurring expenses have increased by <b>{{increased_value}}</b> compared to the previous expenses period of {{period}}. {{increased_category_and_value}}</p>',
            'footer_text' => '<p style="text-align: center;">This message was sent to {{email}}. Please login to the BetterTracker platform to update you your email preferences. For more details, see our <a href="https://channelprogram.com/legal/privacy-policy"> privacy policy</a></p>',
            'description' => 'Sent based on user chosen frequency: Weekly, Monthly, Quarterly, Disabled',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::COMPANY_EXPENSES_DECREASE_OVER_TIMEFRAME) => [
            'display_name' => 'Decrease over X timeframe',
            'subject' => '🎉 Exciting News: Your Expenses Just Got a Break!',
            'header_text' => '<h1 style="text-align: center;">Hey {{first_name}}, good news!</h1>',
            'intro_text' => '<p style="text-align: center;">Your recurring expenses have decreased by <b>{{decreased_value}}</b> compared to the previous expenses period of {{period}}. {{decreased_category_and_value}}</p>',
            'footer_text' => '<p style="text-align: center;">This message was sent to {{email}}. Please login to the BetterTracker platform to update you your email preferences. For more details, see our <a href="https://channelprogram.com/legal/privacy-policy"> privacy policy</a></p>',
            'description' => 'Sent based on user chosen frequency: Weekly, Monthly, Quarterly, Disabled',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::COMPANY_EXPENSES_INDIVIDUAL_RECURRING_EXPENSE_INCREASE) => [
            'display_name' => 'Individual Recurring Expense Increase',
            'subject' => 'Your recurring expense increased. Let’s Dive In! 📈',
            'header_text' => '<h1 style="text-align: center;">Your recurring expense increased</h1>',
            'intro_text' => '<p style="text-align: center;">Hi <b>{{first_name}}</b>, your were charged <b>{{charge_value}}</b> for your <b>{{subscription_value}}</b> subscription. This is a <b>{{increase_value}}</b> increase from the last charge.</p>',
            'footer_text' => '<p style="text-align: center;">This message was sent to {{email}}. Please login to the BetterTracker platform to update you your email preferences. For more details, see our <a href="https://channelprogram.com/legal/privacy-policy"> privacy policy</a></p>',
            'description' => 'Sent when the expense comes in',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::COMPANY_EXPENSES_INDIVIDUAL_RECURRING_EXPENSE_DECREASE) => [
            'display_name' => 'Individual Recurring Expense Decrease',
            'subject' => 'Your recurring expense decreased. 🎉',
            'header_text' => '<h1 style="text-align: center;">Your recurring expense decreased</h1>',
            'intro_text' => '<p style="text-align: center;">Hi <b>{{first_name}}</b>, your were charged <b>{{charge_value}}</b> for your <b>{{subscription_value}}</b> subscription. This is a <b>{{decrease_value}}</b> decrease from the last charge.</p>',
            'footer_text' => '<p style="text-align: center;">This message was sent to {{email}}. Please login to the BetterTracker platform to update you your email preferences. For more details, see our <a href="https://channelprogram.com/legal/privacy-policy"> privacy policy</a></p>',
            'description' => 'Sent when the expense comes in',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::COMPANY_EXPENSES_WEEKLY_SUMMARY) => [
            'display_name' => 'Weekly Summary',
            'subject' => 'Last week You Spent 🔍',
            'header_text' => '<h1 style="text-align: center;">You spent {{weekly_summary}} last week</h1>',
            'intro_text' => '<p style="text-align: center;">This is <b>{{decrease_increase_value}}</b> than you normally spend on this period. Your largest expense was <b>{{category_and_value}}</b>.</p>',
            'footer_text' => '<p style="text-align: center;">This message was sent to {{email}}. Please login to the BetterTracker platform to update you your email preferences. For more details, see our <a href="https://channelprogram.com/legal/privacy-policy"> privacy policy</a></p>',
            'description' => ' Sent Every Tuesday at 6AM EST',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::COMPANY_EXPENSES_MONTHLY_SUMMARY) => [
            'display_name' => 'Monthly Summary',
            'subject' => '🚀 Your {{month_name}} recurring expenses summary is ready! 🔍',
            'header_text' => '<h1 style="text-align: center;">Your {{month_name}} recurring expenses summary is ready!</h1>',
            'intro_text' => '<p style="text-align: center;">Check out the summary of changes to your recurring expenses.</p>',
            'footer_text' => '<p style="text-align: center;">This message was sent to {{email}}. Please login to the BetterTracker platform to update you your email preferences. For more details, see our <a href="https://channelprogram.com/legal/privacy-policy"> privacy policy</a></p>',
            'description' => 'Sent every 10th Day of New Month',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::COMPANY_EXPENSES_TIME_TO_REVIEW_YOUR_EXPENSES) => [
            'display_name' => 'Time to Review your Expenses',
            'subject' => 'Time to review your expenses! 🔍',
            'header_text' => '<h1 style="text-align: center;">Time to review your expenses</h1>',
            'intro_text' => '<p style="text-align: center;">Hi <b>{{first_name}}</b>, we recommend conducting a full review of your recurring expenses every three months to ensure you´re not paying for services you no longer use.<br/><br/>During our recent check, we noticed that you currently have <b>{{active_recurring_expenses}} active recurring expenses</b>.</p>',
            'footer_text' => '<p style="text-align: center;">This message was sent to {{email}}. Please login to the BetterTracker platform to update you your email preferences. For more details, see our <a href="https://channelprogram.com/legal/privacy-policy"> privacy policy</a></p>',
            'description' => 'Sent in 90 day increments',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::COMPANY_EXPENSES_UPCOMING_RECURRING_CHARGES_MSP) => [
            'display_name' => 'Upcoming Recurring Charges for MSPs',
            'subject' => '🚀 Heads Up! Your Upcoming Expenses Are on the Horizon 🌟',
            'header_text' => '<h1 style="text-align: center;">Hey {{first_name}}, don´t forget...</h1>',
            'intro_text' => '<p style="text-align: center;">You have <b>{{expense_count}}</b> upcoming Recurring Expenses in the next <b>{{defined_period}}</b>.</p>',
            'footer_text' => '<p style="text-align: center;">This message was sent to {{email}}. Please login to the Channel Program platform to update you your email preferences. For more details, see our <a href="https://channelprogram.com/legal/privacy-policy"> privacy policy</a></p>',
            'description' => 'Sent every Monday at 6AM EST',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::COMPANY_EXPENSES_INCREASE_OVER_TIMEFRAME_MSP) => [
            'display_name' => 'Increase over X timeframe for MSPs',
            'subject' => 'Your Spending’s on the Rise! Let’s Dive In! 📈',
            'header_text' => '<h1 style="text-align: center;">Hey {{first_name}}, heads up!</h1>',
            'intro_text' => '<p style="text-align: center;">Your recurring expenses have increased by <b>{{increased_value}}</b> compared to the previous expenses period of {{period}}. {{increased_category_and_value}}</p>',
            'footer_text' => '<p style="text-align: center;">This message was sent to {{email}}. Please login to the Channel Program platform to update you your email preferences. For more details, see our <a href="https://channelprogram.com/legal/privacy-policy"> privacy policy</a></p>',
            'description' => 'Sent based on user chosen frequency: Weekly, Monthly, Quarterly, Disabled',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::COMPANY_EXPENSES_DECREASE_OVER_TIMEFRAME_MSP) => [
            'display_name' => 'Decrease over X timeframe for MSPs',
            'subject' => '🎉 Exciting News: Your Expenses Just Got a Break!',
            'header_text' => '<h1 style="text-align: center;">Hey {{first_name}}, good news!</h1>',
            'intro_text' => '<p style="text-align: center;">Your recurring expenses have decreased by <b>{{decreased_value}}</b> compared to the previous expenses period of {{period}}. {{decreased_category_and_value}}</p>',
            'footer_text' => '<p style="text-align: center;">This message was sent to {{email}}. Please login to the Channel Program platform to update you your email preferences. For more details, see our <a href="https://channelprogram.com/legal/privacy-policy"> privacy policy</a></p>',
            'description' => 'Sent based on user chosen frequency: Weekly, Monthly, Quarterly, Disabled',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::COMPANY_EXPENSES_INDIVIDUAL_RECURRING_EXPENSE_INCREASE_MSP) => [
            'display_name' => 'Individual Recurring Expense Increase for MSPs',
            'subject' => 'Your recurring expense increased. Let’s Dive In! 📈',
            'header_text' => '<h1 style="text-align: center;">Your recurring expense increased</h1>',
            'intro_text' => '<p style="text-align: center;">Hi <b>{{first_name}}</b>, your were charged <b>{{charge_value}}</b> for your <b>{{subscription_value}}</b> subscription. This is a <b>{{increase_value}}</b> increase from the last charge.</p>',
            'footer_text' => '<p style="text-align: center;">This message was sent to {{email}}. Please login to the Channel Program platform to update you your email preferences. For more details, see our <a href="https://channelprogram.com/legal/privacy-policy"> privacy policy</a></p>',
            'description' => 'Sent when the expense comes in',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::COMPANY_EXPENSES_INDIVIDUAL_RECURRING_EXPENSE_DECREASE_MSP) => [
            'display_name' => 'Individual Recurring Expense Decrease for MSPs',
            'subject' => 'Your recurring expense decreased. 🎉',
            'header_text' => '<h1 style="text-align: center;">Your recurring expense decreased</h1>',
            'intro_text' => '<p style="text-align: center;">Hi <b>{{first_name}}</b>, your were charged <b>{{charge_value}}</b> for your <b>{{subscription_value}}</b> subscription. This is a <b>{{decrease_value}}</b> decrease from the last charge.</p>',
            'footer_text' => '<p style="text-align: center;">This message was sent to {{email}}. Please login to the Channel Program platform to update you your email preferences. For more details, see our <a href="https://channelprogram.com/legal/privacy-policy"> privacy policy</a></p>',
            'description' => 'Sent when the expense comes in',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::COMPANY_EXPENSES_WEEKLY_SUMMARY_MSP) => [
            'display_name' => 'Weekly Summary for MSPs',
            'subject' => 'Last week You Spent 🔍',
            'header_text' => '<h1 style="text-align: center;">You spent {{weekly_summary}} last week</h1>',
            'intro_text' => '<p style="text-align: center;">This is <b>{{decrease_increase_value}}</b> than you normally spend on this period. Your largest expense was <b>{{category_and_value}}</b>.</p>',
            'footer_text' => '<p style="text-align: center;">This message was sent to {{email}}. Please login to the Channel Program platform to update you your email preferences. For more details, see our <a href="https://channelprogram.com/legal/privacy-policy"> privacy policy</a></p>',
            'description' => ' Sent Every Tuesday at 6AM EST',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::COMPANY_EXPENSES_MONTHLY_SUMMARY_MSP) => [
            'display_name' => 'Monthly Summary for MSPs',
            'subject' => '🚀 Your {{month_name}} recurring expenses summary is ready! 🔍',
            'header_text' => '<h1 style="text-align: center;">Your {{month_name}} recurring expenses summary is ready!</h1>',
            'intro_text' => '<p style="text-align: center;">Check out the summary of changes to your recurring expenses.</p>',
            'footer_text' => '<p style="text-align: center;">This message was sent to {{email}}. Please login to the Channel Program platform to update you your email preferences. For more details, see our <a href="https://channelprogram.com/legal/privacy-policy"> privacy policy</a></p>',
            'description' => 'Sent every 10th Day of New Month',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::COMPANY_EXPENSES_TIME_TO_REVIEW_YOUR_EXPENSES_MSP) => [
            'display_name' => 'Time to Review your Expenses for MSPs',
            'subject' => 'Time to review your expenses! 🔍',
            'header_text' => '<h1 style="text-align: center;">Time to review your expenses</h1>',
            'intro_text' => '<p style="text-align: center;">Hi <b>{{first_name}}</b>, we recommend conducting a full review of your recurring expenses every three months to ensure you´re not paying for services you no longer use.<br/><br/>During our recent check, we noticed that you currently have <b>{{active_recurring_expenses}} active recurring expenses</b>.</p>',
            'footer_text' => '<p style="text-align: center;">This message was sent to {{email}}. Please login to the Channel Program platform to update you your email preferences. For more details, see our <a href="https://channelprogram.com/legal/privacy-policy"> privacy policy</a></p>',
            'description' => 'Sent in 90 day increments',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::HAPPY_ANNIVERSARY_ONE_MONTH) => [
            'display_name' => 'Better Tracker One Month Anniversary',
            'subject' => 'Happy Anniversary 🎉 🥳',
            'header_text' => '<h1 style="text-align: center;">Cheers to Your First Month with BetterTracker, {{first_name}}! 🎉</h1>',
            'intro_text' => '<p style="text-align: center;">Hi <b>{{first_name}}</b>,<br/>Congrats on reaching your first month using BetterTracker to manage expenses! 🥳<br/></p>' .
                '<p style="text-align: center;">We’d love to hear how we’re doing so far! Your thoughts and feedback are super important in helping us make BetterTracker even better.<br/>Can’t wait to hear what you think!<br/></p>' .
                '<p style="text-align: center;">Stay awesome,<br/>The BetterTracker Team ✨</p>',
            'footer_text' => '<p style="text-align: center;">This message was sent to {{email}}. Please login to the BetterTracker platform to update you your email preferences. For more details, see our <a href="https://channelprogram.com/legal/privacy-policy"> privacy policy</a></p>',
            'description' => 'Better Tracker One Month Anniversary',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::HAPPY_ANNIVERSARY_SIX_MONTHS) => [
            'display_name' => 'Better Tracker Six Months Anniversary',
            'subject' => 'Happy Anniversary 🎉 🥳',
            'header_text' => '<h1 style="text-align: center;">Cheers to Six Months with BetterTracker, {{first_name}}! 🎉</h1>',
            'intro_text' => '<p style="text-align: center;">Hi <b>{{first_name}}</b>,<br/>Congrats on reaching six months using BetterTracker! 🥳<br/></p>' .
                '<p style="text-align: center;">Your journey with us has been amazing, and we hope BetterTracker has made managing your expenses easier and more efficient.<br/></p>' .
                '<p style="text-align: center;">We’d love to hear how we’re doing! Your thoughts and feedback are super important in helping us make BetterTracker even better. Can’t wait to hear what you think!<br/></p>' .
                '<p style="text-align: center;">Stay awesome,<br/>The BetterTracker Team ✨</p>',
            'footer_text' => '<p style="text-align: center;">This message was sent to {{email}}. Please login to the BetterTracker platform to update you your email preferences. For more details, see our <a href="https://channelprogram.com/legal/privacy-policy"> privacy policy</a></p>',
            'description' => 'Better Tracker Six Months Anniversary',
        ],
        EmailBladeFiles::getKey(EmailBladeFiles::HAPPY_ANNIVERSARY_ONE_YEAR) => [
            'display_name' => 'Better Tracker One Year Anniversary',
            'subject' => 'Happy Anniversary 🎉 🥳',
            'header_text' => '<h1 style="text-align: center;">Cheers to One Year with BetterTracker, {{first_name}}! 🎉</h1>',
            'intro_text' => '<p style="text-align: center;">Hi <b>{{first_name}}</b>,<br/>Congrats on reaching one year using BetterTracker! 🥳<br/></p>' .
                '<p style="text-align: center;">It’s been a fantastic year, and we hope BetterTracker has been a valuable tool in managing your expenses.<br/></p>' .
                '<p style="text-align: center;">Your continued support and feedback have been crucial in helping us improve. We’d love to hear how we’re doing and any suggestions you might have for the future.<br/></p>' .
                '<p style="text-align: center;">Thank you for being a part of our journey,<br/>The BetterTracker Team ✨</p>',
            'footer_text' => '<p style="text-align: center;">This message was sent to {{email}}. Please login to the BetterTracker platform to update you your email preferences. For more details, see our <a href="https://channelprogram.com/legal/privacy-policy"> privacy policy</a></p>',
            'description' => 'Better Tracker One Year Anniversary',
        ],
    ],
];
