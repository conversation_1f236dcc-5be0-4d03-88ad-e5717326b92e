<?php

use App\Enums\Company\CompanyType as CompanyCompanyEnum;
use App\Models\Category\CategoryCompanyType;
use App\Models\Company\CompanyType;
use Illuminate\Database\Migrations\Migration;

return new class() extends Migration {
    public function up()
    {
        $mspClient = CompanyType::where('value', CompanyCompanyEnum::MSP_CLIENT)
            ->select('id', 'value')->first();
        $direct = CompanyType::where('value', CompanyCompanyEnum::DIRECT)
            ->select('id', 'value')->first();

        $toDuplicate = CategoryCompanyType::where('company_type_id', $mspClient->id)->get();
        $now = now();
        $newRows = [];
        foreach ($toDuplicate as $row) {
            $newRows[] = [
                'category_id' => $row->category_id,
                'company_type_id' => $direct->id,
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }
        if (!empty($newRows)) {
            CategoryCompanyType::insert($newRows);
        }
    }

    public function down()
    {
        $direct = CompanyType::where('value', CompanyCompanyEnum::DIRECT)
            ->select('id', 'value')->first();

        CategoryCompanyType::where('company_type_id', $direct->id)->delete();
    }
};
