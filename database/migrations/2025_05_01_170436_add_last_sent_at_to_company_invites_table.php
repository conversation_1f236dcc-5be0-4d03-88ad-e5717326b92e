<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_invites', function (Blueprint $table) {
            $table->timestamp('last_sent_at')->nullable();
        });

        Schema::table('company_partners', function (Blueprint $table) {
            $table->timestamp('last_sent_invite_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('company_invites', function (Blueprint $table) {
            $table->dropColumn('last_sent_at');
        });

        Schema::table('company_partners', function (Blueprint $table) {
            $table->dropColumn('last_sent_invite_at');
        });
    }
};
