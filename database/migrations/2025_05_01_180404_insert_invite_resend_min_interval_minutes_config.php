<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::table('app_configurations')->insert([
            'key' => 'INVITE_RESEND_MIN_INTERVAL_MINUTES',
            'value' => '120',
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('app_configurations')->where('key', 'INVITE_RESEND_MIN_INTERVAL_MINUTES')->delete();
    }
};
