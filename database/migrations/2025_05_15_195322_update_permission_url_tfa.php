<?php

use App\Models\Permission\Permission;
use Illuminate\Database\Migrations\Migration;

return new class() extends Migration {
    public function __construct()
    {
        $this->date = now();
    }
    private string $route = 'PUT:api/v1/admin/user/reset-two-factor-authentication';

    public function up(): void
    {
        Permission::create([
            'title' => $this->route,
            'description' => 'Reset two factor authentication',
            'created_at' => $this->date,
            'updated_at' => $this->date,
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Permission::where('title', $this->route)->delete();
    }
};
