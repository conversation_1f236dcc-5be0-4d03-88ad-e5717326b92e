<?php

use App\Models\AppConfiguration;
use App\Services\Redis\RedisService;
use Illuminate\Database\Migrations\Migration;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create the following app configs for the topTrending formulas
        AppConfiguration::updateOrCreate(
            ['key' => 'TOP_TRENDING_INITIAL_DAYS'],
            ['value' => config('common.topTrendingInitialDays'), 'created_at' => now()]
        );
        AppConfiguration::updateOrCreate(
            ['key' => 'TOP_TRENDING_DAYS'],
            ['value' => config('common.topTrendingDays'), 'created_at' => now()]
        );
        AppConfiguration::updateOrCreate(
            ['key' => 'TOP_TRENDING_PRODUCTS'],
            ['value' => config('common.topTrendingProducts'), 'created_at' => now()]
        );
        AppConfiguration::updateOrCreate(
            ['key' => 'TOP_TRENDING_COMPANIES'],
            ['value' => config('common.topTrendingCompanies'), 'created_at' => now()]
        );
        AppConfiguration::updateOrCreate(
            ['key' => 'REVIEW_COUNT_PERCENTAGE'],
            ['value' => config('common.trending.companies.review_count_percentage'), 'created_at' => now()]
        );
        AppConfiguration::updateOrCreate(
            ['key' => 'STACK_COUNT_PERCENTAGE'],
            ['value' => config('common.trending.companies.stack_count_percentage'), 'created_at' => now()]
        );
        RedisService::flushAllRedis();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        AppConfiguration::whereIn('key', [
            'INTERVAL_DAYS',
            'TOP_TRENDING_INITIAL_DAYS',
            'TOP_TRENDING_DAYS',
            'TOP_TRENDING_PRODUCTS',
            'TOP_TRENDING_COMPANIES',
            'REVIEW_COUNT_PERCENTAGE',
            'STACK_COUNT_PERCENTAGE',
        ])->delete();
    }
};
