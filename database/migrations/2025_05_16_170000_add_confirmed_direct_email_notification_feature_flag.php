<?php

use App\Enums\FeatureFlagEnum;
use App\Models\FeatureFlag;
use Illuminate\Database\Migrations\Migration;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        FeatureFlag::updateOrCreate(
            ['name' => FeatureFlagEnum::CONFIRMED_DIRECT_EMAIL_NOTIFICATION],
            ['activated' => true, 'created_at' => now()]
        );
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        FeatureFlag::where('name', FeatureFlagEnum::CONFIRMED_DIRECT_EMAIL_NOTIFICATION)->delete();
    }
};
