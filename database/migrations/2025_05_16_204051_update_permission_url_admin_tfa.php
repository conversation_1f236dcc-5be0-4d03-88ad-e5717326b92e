<?php

use App\Helpers\PermissionsHelper;
use App\Models\Permission\Group\PermissionGroup;
use App\Models\Permission\Permission;
use Carbon\Carbon;
use Illuminate\Database\Migrations\Migration;

return new class() extends Migration {
    private Carbon $date;
    public function __construct()
    {
        $this->date = now();
    }
    private string $route = 'PUT:api/v1/admin/user/reset-two-factor-authentication';

    public function up(): void
    {
        Permission::where('title', $this->route)->delete();
        $permissionGroupRead = PermissionGroup::select('id')->where('key', 'CP_SUPER_ADMIN')->first();
        if ($permissionGroupRead) {
            PermissionsHelper::insertPermissionGroupPermissions([$this->route], $permissionGroupRead->id, $this->date);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Permission::where('title', $this->route)->delete();
    }
};
