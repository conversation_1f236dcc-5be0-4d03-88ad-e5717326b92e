
<?php

use App\Models\Permission\Group\PermissionGroup;
use App\Models\Permission\Group\PermissionGroupPermission;
use App\Models\Permission\Permission;
use Illuminate\Database\Migrations\Migration;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $title = 'GET:api/v1/company/{friendly_url}/clients/count';
        $description = 'Get company clients list count';
        $now = now();

        // Create the permission
        $permission = Permission::updateOrCreate([
            'title' => $title,
        ], [
            'description' => $description,
            'created_at' => $now,
            'updated_at' => $now,
        ]);

        // Add to both permission groups
        $groupKeys = ['MSP_CLIENTS_MGNMT_READ', 'COMPANY_PROFILE_READ'];
        foreach ($groupKeys as $key) {
            $group = PermissionGroup::where('key', $key)->first();
            if ($group) {
                PermissionGroupPermission::updateOrCreate([
                    'permission_group_id' => $group->id,
                    'permission_id' => $permission->id,
                ]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $title = 'GET:api/v1/company/{friendly_url}/clients/count';
        $permission = Permission::where('title', $title)->first();

        if ($permission) {
            PermissionGroupPermission::where('permission_id', $permission->id)->delete();
            $permission->delete();
        }
    }
};
