<?php

use App\Enums\AppConfigEnum;
use App\Models\AppConfiguration;
use Illuminate\Database\Migrations\Migration;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        AppConfiguration::updateOrCreate(
            ['key' => AppConfigEnum::DIRECT_SUBDOMAIN],
            ['value' => 'app', 'created_at' => now()]
        );
        AppConfiguration::updateOrCreate(
            ['key' => AppConfigEnum::DIRECT_SUPPORT_EMAIL],
            ['value' => '<EMAIL>', 'created_at' => now()]
        );
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        AppConfiguration::whereIn('key', [
            AppConfigEnum::DIRECT_SUBDOMAIN,
            AppConfigEnum::DIRECT_SUPPORT_EMAIL,
        ])->delete();
    }
};
