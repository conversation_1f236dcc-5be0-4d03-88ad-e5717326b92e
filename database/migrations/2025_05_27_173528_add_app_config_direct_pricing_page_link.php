<?php

use App\Enums\AppConfigEnum;
use App\Models\AppConfiguration;
use App\Services\Redis\RedisService;
use Illuminate\Database\Migrations\Migration;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        AppConfiguration::updateOrCreate([
            'key' => AppConfigEnum::DIRECT_PRICING_PAGE_LINK,
        ], [
            'value' => 'https://bettertracker.com/pricing',
        ]);

        RedisService::deleteCollectionByModelClass(AppConfiguration::class);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        AppConfiguration::where('key', AppConfigEnum::DIRECT_PRICING_PAGE_LINK)->delete();
    }
};
