<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('plaid_subscriptions', function ($table) {
            $table->json('extra_info')->nullable()->after('ignore');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('plaid_subscriptions', function ($table) {
            $table->dropColumn('extra_info');
        });
    }
};
