services:
  laravel.test:
    build:
      context: ./vendor/laravel/sail/runtimes/8.2
      dockerfile: Dockerfile
      args:
        WWWGROUP: '${WWWGROUP}'
    image: sail-8.2/app
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    ports:
      - '${VITE_PORT:-5173}:${VITE_PORT:-5173}'
    environment:
      WWWUSER: '${WWWUSER}'
      LARAVEL_SAIL: 1
      XDEBUG_MODE: '${SAIL_XDEBUG_MODE:-off}'
      XDEBUG_CONFIG: '${SAIL_XDEBUG_CONFIG:-client_host=host.docker.internal}'
      IGNITION_LOCAL_SITES_PATH: '${PWD}'
    volumes:
      - '.:/var/www/html'
      - './docker/20-xdebug.ini:/etc/php/8.2/cli/conf.d/20-xdebug.ini'
    networks:
      - sail
    depends_on:
      - redis
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - './docker/nginx.conf:/etc/nginx/nginx.conf:ro'
      - './docker/nginx/ssl:/etc/nginx/ssl:ro'
    depends_on:
      - laravel.test
    networks:
      - sail
  redis:
    image: 'redis:alpine'
    ports:
      - '${FORWARD_REDIS_PORT:-6379}:6379'
    volumes:
      - 'sail-redis:/data'
    networks:
      - sail
    healthcheck:
      test:
        - CMD
        - redis-cli
        - ping
      retries: 3
      timeout: 5s
  cockroach:
    image: cockroachdb/cockroach
    command: start-single-node --insecure
    hostname: cockroach
    pull_policy: always
    ports:
      - "26257:26257"
      - "8080:8080"
    networks:
      - sail
    volumes:
      - cockroach-vol:/cockroach/cockroach-data
  mailhog:
    image: mailhog/mailhog
    ports:
      - "1025:1025" # SMTP
      - "8025:8025" # Web UI
    networks:
      - sail
networks:
  sail:
    driver: bridge
volumes:
  sail-redis:
    driver: local
  cockroach-vol:
    external: true
