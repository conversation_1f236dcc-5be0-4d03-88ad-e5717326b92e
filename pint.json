{"preset": "laravel", "rules": {"align_multiline_comment": true, "array_indentation": true, "array_push": false, "array_syntax": true, "assign_null_coalescing_to_coalesce_equal": false, "attribute_empty_parentheses": false, "backtick_to_shell_exec": true, "binary_operator_spaces": true, "blank_line_after_namespace": true, "blank_line_after_opening_tag": false, "blank_line_before_statement": true, "blank_line_between_import_groups": false, "blank_lines_before_namespace": true, "braces": true, "braces_position": {"functions_opening_brace": "next_line_unless_newline_at_signature_end", "classes_opening_brace": "next_line_unless_newline_at_signature_end", "control_structures_opening_brace": "same_line"}, "cast_spaces": {"space": "none"}, "class_attributes_separation": false, "class_definition": true, "clean_namespace": true, "combine_consecutive_issets": true, "combine_consecutive_unsets": true, "combine_nested_dirname": true, "comment_to_phpdoc": false, "compact_nullable_type_declaration": true, "compact_nullable_typehint": true, "concat_space": {"spacing": "one"}, "constant_case": {"case": "lower"}, "declare_equal_normalize": true, "declare_parentheses": true, "doctrine_annotation_array_assignment": false, "doctrine_annotation_braces": true, "doctrine_annotation_indentation": true, "doctrine_annotation_spaces": false, "elseif": false, "empty_loop_body": true, "empty_loop_condition": true, "encoding": true, "explicit_indirect_variable": true, "explicit_string_variable": true, "full_opening_tag": true, "function_declaration": true, "line_ending": true, "linebreak_after_opening_tag": true, "list_syntax": true, "lowercase_cast": true, "lowercase_keywords": true, "lowercase_static_reference": true, "magic_constant_casing": true, "magic_method_casing": true, "method_argument_space": false, "method_chaining_indentation": true, "multiline_comment_opening_closing": true, "new_with_parentheses": {"anonymous_class": true, "named_class": true}, "no_alternative_syntax": true, "no_extra_blank_lines": true, "no_leading_import_slash": true, "no_multiple_statements_per_line": true, "no_spaces_after_function_name": true, "not_operator_with_successor_space": false, "ordered_imports": true, "ordered_interfaces": true, "ordered_types": true, "semicolon_after_instruction": true, "simplified_null_return": false, "single_quote": true, "single_space_around_construct": true, "switch_case_semicolon_to_colon": true, "switch_case_space": true, "switch_continue_to_break": true}}