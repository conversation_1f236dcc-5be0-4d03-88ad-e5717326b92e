@component('mail::message', ['headerImage' => $headerImage])
    @slot('preHeaderImage', "deadline1.png")
    @slot('headerText', $headerText)
    @slot('introText', $introText)
    @slot('footerText', $footerText)
    @slot('unsubscribe')
    @if (!empty($totalExpensesSummaryInfo))
        <div class="table-container">
            <table class="summary-info" align="center">
                @component('mail::bettertracker.summaryInfo')
                    @slot('summaryInfo', $totalExpensesSummaryInfo)
                @endcomponent
            </table>
        </div>
    @endif
    @if (!empty($biggestChangesExpenses) && count($biggestChangesExpenses) > 0)
        <p class="subtitle">BIGGEST CHANGES</p>
        <div class="table-container">
            <table class="summary-info" align="center">
                @foreach($biggestChangesExpenses as $biggestChangesExpense)
                    @component('mail::bettertracker.summaryInfo')
                        @slot('summaryInfo', $biggestChangesExpense)
                    @endcomponent
                @endforeach
            </table>
        </div>
    @endif
    @if (!empty($upcomingThisWeekExpenses) && count($upcomingThisWeekExpenses) > 0)
        <p class="subtitle">UPCOMING THIS WEEK</p>
        <div class="table-container">
            <table class="summary-info" align="center">
                @foreach($upcomingThisWeekExpenses as $upcomingThisWeekExpense)
                    @component('mail::bettertracker.summaryInfo')
                        @slot('summaryInfo', $upcomingThisWeekExpense)
                    @endcomponent
                @endforeach
            </table>
        </div>
    @endif
    @component('mail::button', ['url' => $buttonUrl, 'showLinkAsText' => true])
        Check Your Expenses
    @endcomponent
@endcomponent
