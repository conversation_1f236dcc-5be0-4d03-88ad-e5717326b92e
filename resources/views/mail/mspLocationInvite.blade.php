@component('mail::'.$domain.'message', ['headerImage' => $headerImage, 'companyName' => $companyName])
    @slot('headerText', $headerText)
    @slot('introText', $introText ?? "")
    @slot('footerText', $footerText ?? config('mail.default_content.footer_text'))
    <p>
        Don’t hesitate — <a href="{{ $loginRoute }}">sign in now</a> or <a href="{{ $registerRoute }}">register</a> for
        your new account!
    </p>
@endcomponent
