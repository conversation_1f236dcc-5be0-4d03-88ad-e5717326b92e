<?php

namespace Feature\Company;

use Tests\TestCase;

class CompanyClientControllerTest extends TestCase
{
    private string $showAll = 'api/v1/company/{company}/clients';
    private string $generateCsv = 'api/v1/company/{company}/clients/generate-csv';
    private string $downloadPdf = 'api/v1/company/{company}/clients/download-pdf';
    private string $showClientsStack = 'api/v1/company/{company}/clients/stack';
    private string $showAllFilters = 'api/v1/company/{company}/clients/filters';
    private string $generateClientSubdomain = 'api/v1/company/{company}/clients/sub-domain';
    private string $summary = 'api/v1/company/{company}/clients/summary';
    private string $store = 'api/v1/company/{company}/clients/store';
    private string $update = 'api/v1/company/{company}/clients/update';
    private string $delete = 'api/v1/company/{company}/clients/delete';
    private string $addClientsFromCSV = 'api/v1/company/{company}/clients/add/csv';
    private string $showClientContracts = 'api/v1/company/{company}/clients/contracts';
    private string $getCustomerCountForCompany = 'api/v1/company/{company}/clients/count';

    public function test_routes(): void
    {
        $this->assertTrue($this->checkRouteExists($this->showAll));
        $this->assertTrue($this->checkRouteExists($this->generateCsv));
        $this->assertTrue($this->checkRouteExists($this->downloadPdf));
        $this->assertTrue($this->checkRouteExists($this->showClientsStack));
        $this->assertTrue($this->checkRouteExists($this->showAllFilters));
        $this->assertTrue($this->checkRouteExists($this->generateClientSubdomain));
        $this->assertTrue($this->checkRouteExists($this->summary));
        $this->assertTrue($this->checkRouteExists($this->store));
        $this->assertTrue($this->checkRouteExists($this->update));
        $this->assertTrue($this->checkRouteExists($this->delete));
        $this->assertTrue($this->checkRouteExists($this->addClientsFromCSV));
        $this->assertTrue($this->checkRouteExists($this->showClientContracts));
        $this->assertTrue($this->checkRouteExists($this->getCustomerCountForCompany));
    }
}
