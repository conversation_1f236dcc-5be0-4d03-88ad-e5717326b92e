<?php

namespace Company;

use Illuminate\Support\Facades\Route;
use Tests\TestCase;

class CompanyDashboard extends TestCase
{
    public function test_routes(): void
    {
        $this->assertTrue(Route::has('api.company.dashboard.feature-counts'));
        $this->assertTrue(Route::has('api.company.dashboard.quick-overview'));
        $this->assertTrue(Route::has('api.company.dashboard.stack-breakdown'));
        $this->assertTrue(Route::has('api.company.dashboard.contract-timeline'));
        $this->assertTrue(Route::has('api.company.dashboard.export-stack-breakdown'));
        $this->assertTrue(Route::has('api.company.dashboard.category-detail-count'));
    }
}
