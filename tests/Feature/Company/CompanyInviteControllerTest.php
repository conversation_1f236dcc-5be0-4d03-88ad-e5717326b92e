<?php

namespace Tests\Feature\Company;

use Tests\TestCase;

class CompanyInviteControllerTest extends TestCase
{
    private string $invite = 'api/v1/company/{company}/invite';
    private string $deleteInvite = 'api/v1/company/{company}/invite/delete';
    private string $inviteAffiliates = 'api/v1/company/{company}/invite/affiliates';
    private string $inviteUsers = 'api/v1/company/{company}/invite/users';
    private string $resendInviteUser = 'api/v1/company/{company}/invite/user/resend/{invite}';
    private string $resendInviteAffiliate = 'api/v1/company/{company}/invite/affiliate/resend/{invite}';
    private string $openInvitation = 'api/v1/company/{company}/invite/open-invitation';
    private string $validateInvitation = 'api/v1/company/validate-invitation';
    private string $validateInvitationAffiliate = 'api/v1/company/validate-invitation/affiliate';
    private string $inviteUsersMultipleRoles = 'api/v1/company/{friendly_url}/invite/users-multiple-roles/store';
    private string $inviteUsersValidateCSV = 'api/v1/company/invite/users-multiple-roles/validate-csv';
    private string $inviteUsersDownloadTemplate = 'api/v1/company/invite/users-multiple-roles/template-csv';

    public function test_routes(): void
    {
        $this->assertTrue($this->checkRouteExists($this->invite));
        $this->assertTrue($this->checkRouteExists($this->deleteInvite));
        $this->assertTrue($this->checkRouteExists($this->inviteAffiliates));
        $this->assertTrue($this->checkRouteExists($this->inviteUsers));
        $this->assertTrue($this->checkRouteExists($this->resendInviteUser));
        $this->assertTrue($this->checkRouteExists($this->resendInviteAffiliate));
        $this->assertTrue($this->checkRouteExists($this->openInvitation));
        $this->assertTrue($this->checkRouteExists($this->validateInvitation));
        $this->assertTrue($this->checkRouteExists($this->validateInvitationAffiliate));
        $this->assertTrue($this->checkRouteExists($this->inviteUsersMultipleRoles));
        $this->assertTrue($this->checkRouteExists($this->inviteUsersValidateCSV));
        $this->assertTrue($this->checkRouteExists($this->inviteUsersDownloadTemplate));
    }
}
