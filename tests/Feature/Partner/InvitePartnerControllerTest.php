<?php

namespace Tests\Feature\Partner;

use Tests\TestCase;

class InvitePartnerControllerTest extends TestCase
{
    private string $uriAcceptInvitation = 'api/v1/partner/invitation/{inviteId}/accept';
    private string $uriAllInvites = 'api/v1/partner/{vendor}/invites';
    private string $uriInviteUser = 'api/v1/partner/{vendor}/invite';
    private string $uriReSendInviteUser = 'api/v1/partner/{vendor}/invite/resend-invite/{invite}';
    private string $uriInviteCsvInvite = 'api/v1/partner/{vendor}/invite/csv';
    private string $uriRemoveUserInvite = 'api/v1/partner/{vendor}/remove-invite';
    private string $uriRemoveUserInviteByIds = 'api/v1/partner/remove-invite';
    private string $uriUpdatePartnership = 'api/v1/partner/invitation/{inviteId}/update-partnership';
    private string $uriAcceptMultipleInvitation = 'api/v1/partner/invitation/{vendor}/accept-multiple';
    private string $uriInviteFilters = 'api/v1/partner/{vendor}/invite/filters';
    private string $getOpenInvitation = 'api/v1/partner/{vendor}/open-invitation';

    public function test_routes(): void
    {
        $this->assertTrue($this->checkRouteExists($this->uriAcceptInvitation));
        $this->assertTrue($this->checkRouteExists($this->uriAllInvites));
        $this->assertTrue($this->checkRouteExists($this->uriInviteUser));
        $this->assertTrue($this->checkRouteExists($this->uriReSendInviteUser));
        $this->assertTrue($this->checkRouteExists($this->uriInviteCsvInvite));
        $this->assertTrue($this->checkRouteExists($this->uriRemoveUserInvite));
        $this->assertTrue($this->checkRouteExists($this->uriRemoveUserInviteByIds));
        $this->assertTrue($this->checkRouteExists($this->uriUpdatePartnership));
        $this->assertTrue($this->checkRouteExists($this->uriAcceptMultipleInvitation));
        $this->assertTrue($this->checkRouteExists($this->uriInviteFilters));
        $this->assertTrue($this->checkRouteExists($this->getOpenInvitation));
    }
}
