<?php

namespace Tests\Feature\Register;

use Tests\TestCase;

class RegisterMSPControllerTest extends TestCase
{
    private string $msp = 'api/v1/register/msp';
    private string $direct = 'api/v1/register/direct';
    // private Generator $faker;

    /*public function __construct()
    {
        parent::__construct();
        $this->faker = Factory::create();
    }*/

    public function test_routes(): void
    {
        $this->assertTrue($this->checkRouteExists($this->msp));
        $this->assertTrue($this->checkRouteExists($this->direct));
    }
}
